import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_datetime_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_radio_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_text_component.dart';
import 'package:hr_portal/app/mahas/components/mahas_themes.dart';
import 'package:hr_portal/app/mahas/components/pages/setup_page_component.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';

import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas_complement/status_request.dart';
import '../controllers/permintaan_jadwal_cuti_hamil_setup_controller.dart';

class PermintaanJadwalCutiHamilSetupView
    extends GetView<PermintaanJadwalCutiHamilSetupController> {
  const PermintaanJadwalCutiHamilSetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      controller: controller.formCon,
      title: MahasConfig.dinamisForm.cutiHamil?.tittle ?? "Cuti Hamil",
      children: () => [
        Visibility(
          visible: controller.formCon.isState != SetupPageState.create,
          child: Column(
            children: [
              Obx(() => Text(
                  "Tanggal/Waktu pengajuan : ${controller.tglPengajuan.value}")),
              const SizedBox(
                height: 10,
              ),
            ],
          ),
        ),
        Visibility(
          visible: controller.allowED == false ? true : false,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InputTextComponent(
                label: "Pegawai Request",
                controller: controller.pegawaiRequestCon,
                editable: false,
                marginBottom: 5,
              ),
              InkWell(
                onTap: () async => await Helper.fotoProfilOnTap(
                    controller.idPegawaiRequest.value),
                child: SizedBox(
                  height: 30,
                  child: Text(
                    "Lihat Foto Profil Pegawai",
                    style: MahasThemes.link,
                    textAlign: TextAlign.left,
                  ),
                ),
              ),
            ],
          ),
        ),
        Visibility(
          visible: MahasConfig.dinamisForm.cutiHamil?.anakKeVisible == true,
          child: InputTextComponent(
            label: 'Anak Ke-',
            controller: controller.anakCon,
            required: true,
            editable: controller.formCon.editable,
          ),
        ),
        InputDatetimeComponent(
          controller: controller.tglHplCon,
          label: 'Tanggal HPL',
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDatetimeComponent(
          controller: controller.dariTglCon,
          label: 'Dari Tanggal',
          required: true,
          editable: MahasConfig.dinamisForm.cutiHamil?.autoAdd45Days == true
              ? false
              : controller.formCon.editable,
        ),
        Visibility(
          visible: MahasConfig.dinamisForm.cutiHamil?.selamaVisible == false,
          child: InputDatetimeComponent(
            controller: controller.sampaiTglCon,
            label: 'Sampai Tanggal',
            required: true,
            editable: MahasConfig.dinamisForm.cutiHamil?.autoAdd45Days == true
                ? false
                : controller.formCon.editable,
          ),
        ),
        Visibility(
          visible: controller.formCon.isState == SetupPageState.detail &&
              MahasConfig.dinamisForm.cutiHamil?.berapaHariVisible == true,
          child: InputTextComponent(
            label: "Jumlah Berapa Hari",
            required: false,
            controller: controller.berapaHariCon,
            editable: false,
          ),
        ),
        Visibility(
          visible: MahasConfig.dinamisForm.cutiHamil?.selamaVisible == true,
          child: InputRadioComponent(
            controller: controller.radioCon,
            editable: controller.formCon.editable,
            required: true,
            label: 'Selama',
          ),
        ),
        InputFileComponent(
          controller: controller.fileCon,
          label: "Upload Dokumen",
          editable: controller.formCon.editable,
          required: false,
        ),
        const SizedBox(
          height: 20,
        ),
        Obx(
          () => ApprovalStatusContainer(
            isEditable: controller.formCon.editable,
            isVisible: controller.isVisible,
            isBatal: controller.isBatal,
            allowED: controller.allowED,
            isDinamisApprove: controller.isDinamisApprove,
            alasanTolakController: controller.alasanTolakCon,
            onTolakPressed: () {
              controller.approvalOnPress(false);
            },
            onTerimaPressed: () {
              controller.approvalOnPress(true);
            },
            batalOnPress: () {
              var status = controller.isBatal.isTrue ? 'batal' : 'tolak';
              alert(status);
            },
            pegawaiKadiv: controller.pegawaiKadiv.value,
            pegawaiManager: controller.pegawaiManager.value,
            accKadiv: controller.accKadiv.value,
            accManager: controller.accManager.value,
            statusTolak: controller.statusTolak.value,
            modelApproval: controller.modelApproval,
            isStop: controller.isStop.value,
          ),
        ),
      ],
    );
  }

  Future<dynamic> alert(String status) {
    return Get.dialog(
      AlertDialog(
        content: SizedBox(
          width: 1000,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              InputTextComponent(
                label: status == "tolak" ? "Alasan Ditolak" : "Alasan Batal",
                controller: controller.alasanTolakCon,
                required: true,
              ),
            ],
          ),
        ),
        contentPadding:
            const EdgeInsets.only(bottom: 0, top: 20, right: 10, left: 10),
        actionsPadding:
            const EdgeInsets.only(top: 0, bottom: 0, left: 10, right: 10),
        actions: [
          TextButton(
            child: Text(
              "Close",
              style: MahasThemes.muted,
            ),
            onPressed: () {
              Get.back(result: false);
            },
          ),
          TextButton(
            child: const Text(
              "Simpan",
            ),
            onPressed: () {
              if (controller.alasanTolakCon.isValid) {
                if (status == "tolak") {
                  controller.approvalOnPress(false);
                } else if (status == "batal") {
                  controller.batalOnPress();
                }
                Get.back(result: true);
              }
            },
          ),
        ],
      ),
    );
  }
}
