import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:get/get.dart';

import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/others/list_component.dart';
import '../../../mahas/mahas_colors.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../mahas_complement/mahas_colors.dart';
import '../../../models/etikcet_model.dart';
import '../controllers/eticket_list_controller.dart';

class EticketListView extends GetView<EticketListController> {
  const EticketListView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('E-Ticket'),
        centerTitle: true,
        actions: <Widget>[
          Padding(
            padding: const EdgeInsets.only(right: 10),
            child: GestureDetector(
              onTap: controller.addOnPress,
              child: const Icon(
                FontAwesomeIcons.plus,
                size: 24,
              ),
            ),
          ),
        ],
      ),
      body: ListComponent(
        controller: controller.listCon,
        itemBuilder: (EticketModel e) {
          return InkWell(
            onTap: () => controller.itemOnTab(e.id!),
            child: Padding(
              padding:
                  const EdgeInsets.only(left: 12, right: 12, top: 8, bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 5),
                        Text(e.judul.toString(), style: MahasThemes.title),
                        e.selesai.toString() == "null" &&
                                e.batal.toString() == "null"
                            ? const Row(
                                children: [
                                  Icon(
                                    FontAwesomeIcons.clock,
                                    size: 12,
                                  ),
                                  Text(" Menunggu")
                                ],
                              )
                            : e.batal == true
                                ? const Row(
                                    children: [
                                      Icon(
                                        FontAwesomeIcons.circleXmark,
                                        size: 12,
                                        color: MahasColors.red,
                                      ),
                                      Text(
                                        " Batal",
                                        style:
                                            TextStyle(color: MahasColors.red),
                                      )
                                    ],
                                  )
                                : const Row(
                                    children: [
                                      Icon(
                                        FontAwesomeIcons.check,
                                        size: 12,
                                        color: MahasColor.colorGreen,
                                      ),
                                      Text(
                                        " Selesai",
                                        style: TextStyle(
                                          color: MahasColor.colorGreen,
                                        ),
                                      )
                                    ],
                                  ),
                        const SizedBox(height: 5),
                      ],
                    ),
                  ),
                  Text(
                    MahasFormat.displayDate(e.tanggal),
                    style: MahasColor.muted,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
