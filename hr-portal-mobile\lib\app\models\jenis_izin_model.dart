import 'dart:convert';

import '../mahas/services/mahas_format.dart';

class JenisIzinModel {
  int? id;
  String? nama;
  int? durasiLibur;
  bool? wajibDokumen;

  JenisIzinModel();

  static JenisIzinModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static JenisIzinModel fromDynamic(dynamic dynamicData) {
    final model = JenisIzinModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.nama = dynamicData['nama'];
    model.durasiLibur = dynamicData['durasiLibur'];
    model.wajibDokumen = dynamicData['wajibDokumen'];

    return model;
  }
}
