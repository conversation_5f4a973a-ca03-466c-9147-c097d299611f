import 'dart:convert';

import '../mahas/services/mahas_format.dart';

class ProfiledivisiModel {
  int? id;
  String? nama;
  String? kadiv;
  String? manager;
  List<ProfiledivisiPegawaiModel>? pegawai;

  ProfiledivisiModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static ProfiledivisiModel fromDynamic(dynamic dynamicData) {
    final model = ProfiledivisiModel();

    model.id = dynamicData['id'];
    model.nama = dynamicData['nama'];
    model.kadiv = dynamicData['kadiv'];
    model.manager = dynamicData['manager'];
    if (dynamicData['pegawai'] != null) {
      final detailT = dynamicData['pegawai'] as List;
      model.pegawai = [];
      for (var i = 0; i < detailT.length; i++) {
        model.pegawai!.add(ProfiledivisiPegawaiModel.fromDynamic(detailT[i]));
      }
    }

    return model;
  }
}

class ProfiledivisiPegawaiModel {
  String? nama;

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static fromDynamic(dynamic dynamicData) {
    final model = ProfiledivisiPegawaiModel();

    model.nama = dynamicData['nama'];

    return model;
  }
}

class DivisiModel {
  int? id;
  String? nama;

  DivisiModel();

  static DivisiModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static DivisiModel fromDynamic(dynamic dynamicData) {
    final model = DivisiModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.nama = dynamicData['nama'];

    return model;
  }
}
