import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class PengumumanModel {
  int? id;
  String? judul;
  String? keterangan;
  DateTime? tanggalberlaku;
  DateTime? tanggalberakhir;
  String? pathfile;

  PengumumanModel();

  static PengumumanModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static PengumumanModel fromDynamic(dynamic dynamicData) {
    final model = PengumumanModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.judul = dynamicData['judul'];
    model.keterangan = dynamicData['keterangan'];
    model.tanggalberlaku =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalBerlaku']);
    model.tanggalberakhir =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalBerakhir']);
    model.pathfile = dynamicData['pathFile'];

    return model;
  }
}
