import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../models/perpus_model.dart';
import '../../../routes/app_pages.dart';

class InformasiPerpusController extends GetxController {
  int count = 5;

  List<PerpusModel> baselistData = [];
  List<String> baseUniqueCategories = [];

  List<PerpusModel> listData = [];
  List<String> uniqueCategories = [];

  final kategoriCon = InputDropdownController();
  final searchCon = InputTextController();
  final tahunCon = InputTextController(type: InputTextType.number);

  RxBool isLoading = true.obs;
  RxBool isFilter = false.obs;
  int pageIndex = 0;

  @override
  void onInit() {
    onRefresh();
    getKategori();
    super.onInit();
  }

  void onRefresh() async {
    isLoading.value = true;
    await getAllData();
    getCategories();
  }

  Future<void> getAllData() async {
    var r = await HttpApi.get(
        '/api/Perpus?pageIndex=$pageIndex&filter=${searchCon.value}&tahun=${tahunCon.value}&idKategori=${kategoriCon.value}');
    if (r.success) {
      if (listData.isNotEmpty) {
        listData.clear();
      }
      var data = json.decode(r.body);
      for (var e in data['datas']) {
        listData.add(PerpusModel.fromDynamic(e));
      }
      if (baselistData.isEmpty) {
        baselistData = listData;
      }
    } else {
      Helper.fetchErrorMessage(r, httpMethod: HttpMethod.get);
    }
  }

  void getCategories() {
    if (uniqueCategories.isNotEmpty) {
      uniqueCategories.clear();
    }
    uniqueCategories =
        listData.map((data) => data.kategoriperpusnama!).toSet().toList();
    if (baseUniqueCategories.isEmpty) {
      baseUniqueCategories = uniqueCategories;
    }
    isLoading.value = false;
  }

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.INFORMASI_PERPUS_SETUP,
      parameters: {
        'id': id.toString(),
      },
    );
  }

  Future<void> filterOnTap() async {
    Helper.dialogFilterCustom(
      confirmAction: () {
        onRefresh();
        if (kategoriCon.value != null ||
            searchCon.value != null ||
            tahunCon.value != null) {
          isFilter.value = true;
        } else {
          isFilter.value = false;
        }
      },
      isClear: isFilter.value,
      clearAction: () {
        tahunCon.value = null;
        searchCon.value = null;
        kategoriCon.value = null;
        onRefresh();
        isFilter.value = false;
      },
      children: [
        InputTextComponent(
          label: "Cari",
          controller: searchCon,
          editable: true,
          marginBottom: 0,
        ),
        const SizedBox(
          height: 10,
        ),
        InputDropdownComponent(
          label: "Kategori",
          controller: kategoriCon,
          editable: true,
          marginBottom: 0,
        ),
        const SizedBox(
          height: 10,
        ),
        InputTextComponent(
          label: "Tahun",
          controller: tahunCon,
          editable: true,
          marginBottom: 0,
        ),
      ],
    );
  }

  Future<void> getKategori() async {
    var r = await HttpApi.get("/api/Perpus/KategoriPerpus");
    if (r.success) {
      RxList<KategoriPerpusModel> listModel = RxList<KategoriPerpusModel>();
      var model = jsonDecode(r.body);
      for (var e in (model ?? [])) {
        listModel.add(KategoriPerpusModel.fromDynamic(e));
      }
      if (r.body != null && kategoriCon.items.isEmpty) {
        kategoriCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      }
    } else {
      Helper.fetchErrorMessage(r, httpMethod: HttpMethod.get);
    }
  }
}
