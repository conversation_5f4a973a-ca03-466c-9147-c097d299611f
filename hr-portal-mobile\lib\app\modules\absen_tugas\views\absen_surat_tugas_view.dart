import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/others/list_component.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';
import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/mahas_colors.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas_complement/mahas_colors.dart';
import '../../../models/absensi_surat_tugas_model.dart';
import '../controllers/absen_surat_tugas_controller.dart';

class AbsenSuratTugasView extends GetView<AbsenSuratTugasController> {
  const AbsenSuratTugasView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
            '${MahasConfig.dinamisForm.absensi?.title ?? 'Absensi'} Tugas'),
        centerTitle: true,
        actions: [
          InkWell(
            onTap: controller.filterOnTap,
            child: SizedBox(
              width: 50,
              height: Get.height,
              child: const Center(
                child: Icon(
                  FontAwesomeIcons.filter,
                  size: 20,
                  color: MahasColors.light,
                ),
              ),
            ),
          )
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(vertical: 10),
        child: ListComponent(
          controller: controller.listCon,
          itemBuilder: (AbsensiSuratTugasModel e) {
            return InkWell(
              onTap: () => controller.itemOnTab(e.id!),
              child: Padding(
                padding: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
                child: LimitedBox(
                  maxHeight: 85,
                  child: Row(
                    children: [
                      Container(
                        width: 50,
                        decoration: BoxDecoration(
                          color: e.status == "Masuk"
                              ? MahasColor.colorGreen
                              : MahasColor.colorRed,
                          border: Border.all(
                            color: MahasColors.grey.withValues(),
                          ),
                          borderRadius: BorderRadius.only(
                            bottomRight: Radius.zero,
                            topRight: Radius.zero,
                            topLeft: Radius.circular(MahasThemes.borderRadius),
                            bottomLeft:
                                Radius.circular(MahasThemes.borderRadius),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Center(
                              child: e.status == "Masuk"
                                  ? const Icon(
                                      Icons.login,
                                      color: Colors.white,
                                    )
                                  : const Icon(
                                      Icons.logout,
                                      color: Colors.white,
                                    ),
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: MahasColors.grey.withValues(),
                            ),
                            borderRadius: BorderRadius.only(
                              topRight:
                                  Radius.circular(MahasThemes.borderRadius),
                              bottomLeft: Radius.zero,
                              bottomRight:
                                  Radius.circular(MahasThemes.borderRadius),
                              topLeft: Radius.zero,
                            ),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 5),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(e.lokasi.toString(),
                                      style: MahasThemes.title),
                                  Text(
                                    MahasFormat.displayDate(e.tanggalJam),
                                    style: MahasColor.muted,
                                  ),
                                ],
                              ),
                              const SizedBox(height: 5),
                              Text(e.pegawaiManager.toString(),
                                  style: MahasThemes.title),
                              e.approveManager.toString() == "null"
                                  ? const Row(
                                      children: [
                                        Icon(
                                          FontAwesomeIcons.clock,
                                          size: 12,
                                        ),
                                        Text(" Menunggu")
                                      ],
                                    )
                                  : e.approveManager == false
                                      ? const Row(
                                          children: [
                                            Icon(
                                              FontAwesomeIcons.circleXmark,
                                              size: 12,
                                              color: MahasColors.red,
                                            ),
                                            Text(
                                              " Ditolak",
                                              style: TextStyle(
                                                  color: MahasColors.red),
                                            )
                                          ],
                                        )
                                      : const Row(
                                          children: [
                                            Icon(
                                              FontAwesomeIcons.check,
                                              size: 12,
                                              color: MahasColor.colorGreen,
                                            ),
                                            Text(
                                              " Diterima",
                                              style: TextStyle(
                                                color: MahasColor.colorGreen,
                                              ),
                                            )
                                          ],
                                        ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
