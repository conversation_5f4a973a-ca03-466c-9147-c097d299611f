import 'dart:convert';
import 'package:flutter/material.dart';
import '../mahas/services/mahas_format.dart';

class AbsensiJadwalModel {
  int? number;
  String? text;
  bool? boolean;
  DateTime? datetime;
  TimeOfDay? time;
  List<AbsensiJadwalListModel>? list;

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static fromDynamic(dynamic dynamicData) {
    final model = AbsensiJadwalModel();

    model.number = MahasFormat.dynamicToInt(dynamicData['number']);
    model.text = dynamicData['text'];
    model.boolean = MahasFormat.dynamicToBool(dynamicData['boolean']);
    model.datetime = MahasFormat.dynamicToDateTime(dynamicData['datetime']);
    model.time = dynamicData['time'];
    if (dynamicData['list'] != null) {
      final detailT = dynamicData['list'] as List;
      model.list = [];
      for (var i = 0; i < detailT.length; i++) {
        model.list!.add(AbsensiJadwalListModel.fromDynamic(detailT[i]));
      }
    }

    return model;
  }
}

class AbsensiJadwalListModel {
  int? number;
  String? text;
  List<AbsensiJadwalSubListModel>? subList;

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static fromDynamic(dynamic dynamicData) {
    final model = AbsensiJadwalListModel();

    model.number = MahasFormat.dynamicToInt(dynamicData['number']);
    model.text = dynamicData['text'];
    if (dynamicData['sub_list'] != null) {
      final detailT = dynamicData['sub_list'] as List;
      model.subList = [];
      for (var i = 0; i < detailT.length; i++) {
        model.subList!.add(AbsensiJadwalSubListModel.fromDynamic(detailT[i]));
      }
    }

    return model;
  }
}

class AbsensiJadwalSubListModel {
  int? number;
  String? text;

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static fromDynamic(dynamic dynamicData) {
    final model = AbsensiJadwalSubListModel();

    model.number = MahasFormat.dynamicToInt(dynamicData['number']);
    model.text = dynamicData['text'];

    return model;
  }
}
