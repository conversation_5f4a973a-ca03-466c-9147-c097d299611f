# C/C++ build system timings
generate_cxx_metadata
  [gap of 36ms]
  create-invalidation-state 40ms
  [gap of 32ms]
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 118ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 31ms]
  create-invalidation-state 134ms
  [gap of 71ms]
  write-metadata-json-to-file 19ms
generate_cxx_metadata completed in 256ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 39ms]
  create-invalidation-state 59ms
  [gap of 22ms]
  write-metadata-json-to-file 13ms
generate_cxx_metadata completed in 133ms

