import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../models/profile_dokumen_model.dart';
import '../../../routes/app_pages.dart';

class ProfileDokumenController extends GetxController {
  final listCon = ListComponentController<DokumenModel>(
    urlApi: (index, filter) => '/api/PegawaiDokumen/List?pageIndex=$index',
    fromDynamic: DokumenModel.fromDynamic,
    allowSearch: false,
  );

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PROFILE_DOKUMEN_SETUP,
      parameters: {
        'id': id.toString(),
      },
    )?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }

  void addOnPress() {
    Get.toNamed(Routes.PROFILE_DOKUMEN_SETUP)?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }
}
