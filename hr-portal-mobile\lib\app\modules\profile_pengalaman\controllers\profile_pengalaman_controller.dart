// ignore_for_file: unnecessary_overrides

import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../models/profile_pengalaman_model.dart';
import '../../../routes/app_pages.dart';

class ProfilePengalamanController extends GetxController {
  final listCon = ListComponentController<PengalamanModel>(
    urlApi: (index, filter) => '/api/PegawaiPengalaman/List?pageIndex=$index',
    fromDynamic: PengalamanModel.fromDynamic,
    allowSearch: false,
  );

  @override
  void onInit() {
    super.onInit();
  }

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PROFILE_PENGALAMAN_SETUP,
      parameters: {
        'id': id.toString(),
      },
    )?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }

  void addOnPress() {
    Get.toNamed(Routes.PROFILE_PENGALAMAN_SETUP)?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }
}
