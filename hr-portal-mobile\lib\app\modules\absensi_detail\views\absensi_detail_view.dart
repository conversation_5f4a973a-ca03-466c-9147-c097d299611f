import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../controllers/absensi_detail_controller.dart';

class AbsensiDetailView extends GetView<AbsensiDetailController> {
  const AbsensiDetailView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      controller: controller.formCon,
      title: 'Informasi',
      children: () => [
        InputTextComponent(
          label: "<PERSON><PERSON>",
          controller: controller.atasNamaCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDatetimeComponent(
          type: InputDatetimeType.time,
          label: "Jam Absensi",
          controller: controller.jamAbsenCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDropdownComponent(
          label: "Lokasi Absensi",
          controller: controller.namaLokasiCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          controller: controller.masukCon,
          label: 'Keterangan',
          editable: controller.formCon.editable,
        ),
        InputFileComponent(
          controller: controller.fileCon,
          label: "Foto",
          editable: controller.formCon.editable,
          required: true,
        ),
      ],
    );
  }
}
