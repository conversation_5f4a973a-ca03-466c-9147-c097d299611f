import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:get/get.dart';

import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/others/list_component.dart';
import '../../../mahas/mahas_colors.dart';
import '../../../models/profile_kontak_model.dart';
import '../controllers/profile_kontak_pegawai_history_controller.dart';

class ProfileKontakPegawaiHistoryView
    extends GetView<ProfileKontakPegawaiHistoryController> {
  const ProfileKontakPegawaiHistoryView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('History Kontak Pegawai'),
        centerTitle: true,
      ),
      body: ListComponent<ProfilekontakModel>(
        controller: controller.listCon,
        itemBuilder: (e) {
          return ListTile(
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(e.nama ?? "-", style: MahasThemes.title),
                Text(
                    e.approval == null
                        ? "Menunggu"
                        : e.approval == false
                            ? "Ditolak"
                            : "Diterima",
                    style: MahasThemes.muted)
              ],
            ),
            subtitle: e.status == "ADD"
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const Icon(
                        FontAwesomeIcons.plus,
                        size: 15,
                        color: MahasColors.green,
                      ),
                      const SizedBox(
                        width: 5,
                      ),
                      Text(
                        "Tambah",
                        style: MahasThemes.muted
                            .copyWith(color: MahasColors.green),
                      ),
                    ],
                  )
                : e.status == "UPDATE"
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Icon(
                            FontAwesomeIcons.penToSquare,
                            size: 15,
                            color: MahasColors.warning,
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          Text(
                            "Ubah",
                            style: MahasThemes.muted
                                .copyWith(color: MahasColors.warning),
                          ),
                        ],
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Icon(
                            FontAwesomeIcons.trash,
                            size: 15,
                            color: MahasColors.danger,
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          Text(
                            "Hapus",
                            style: MahasThemes.muted
                                .copyWith(color: MahasColors.danger),
                          ),
                        ],
                      ),
            onTap: () => controller.itemOnTab(e.id!),
          );
        },
      ),
    );
  }
}
