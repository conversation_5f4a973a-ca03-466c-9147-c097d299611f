import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/others/list_component.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:hr_portal/app/mahas/models/menu_item_model.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/mahas/services/http_api.dart';
import 'package:hr_portal/app/models/informasi_absensi_sesuai_divisi.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/models/api_list_resut_model.dart';
import '../../../models/profile_divisi_model.dart';

class InformasiAbsensiSesuaiDivisiController extends GetxController {
  late ListComponentController<InformasiabsensiModel> listCon;
  RxString dariTanggal =
      "${DateTime.now().year}-${DateTime.now().month}-${DateTime.now().day}"
          .obs;
  RxString sampaiTanggal =
      "${DateTime.now().year}-${DateTime.now().month}-${DateTime.now().day}"
          .obs;
  final InputDatetimeController dariTanggalCon = InputDatetimeController();
  final InputDatetimeController sampaiTanggalCon = InputDatetimeController();
  final InputDropdownController divisiCon = InputDropdownController();
  RxInt valueDivisi = 0.obs;
  final List<MenuItemModel> menus = [];
  RxInt selectedIndex = 0.obs;
  RxBool selectDivisi = false.obs;
  RxBool seeAllDivisi = false.obs;

  @override
  void onInit() async {
    valueDivisi.value = MahasConfig.selectedDivisi ?? 0;
    divisiCon.onChanged = (v) {
      valueDivisi.value = v!.value;
    };
    listCon = ListComponentController<InformasiabsensiModel>(
      urlApi: (index, filter) =>
          '/api/Absensi/Divisi?dariTanggal=${dariTanggal.value}&sampaiTanggal=${sampaiTanggal.value}&idDivisi=${valueDivisi.value}&pageIndex=$index',
      fromDynamic: InformasiabsensiModel.fromDynamic,
      allowSearch: false,
    );
    menus.add(MenuItemModel('Masuk', Icons.login, () {}));
    menus.add(MenuItemModel('Keluar', Icons.logout, () {}));
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onSeeAllDivisi();
    getDivisi();
  }

  void onSeeAllDivisi() {
    if (MahasConfig.dinamisForm.absensi?.seeAllDivisi == true) {
      selectDivisi.value = true;
      seeAllDivisi.value = true;
    } else if (MahasConfig.profile!.namajabatan == "SDM" ||
        MahasConfig.profile!.namajabatan == "Manager") {
      selectDivisi.value = true;
      seeAllDivisi.value = false;
    } else {
      selectDivisi.value = false;
      seeAllDivisi.value = false;
    }
  }

  Future<void> filterOnTap() async {
    if (selectDivisi.value) {
      divisiCon.value = valueDivisi.value;
    }
    Helper.dialogFilter(
      dariTanggalCon: dariTanggalCon,
      sampaiTanggalCon: sampaiTanggalCon,
      todayFilterOnTap: () => filter(false),
      periodFilterOnTap: () => filter(true),
      children: [
        Visibility(
          visible: seeAllDivisi.value,
          child: InputDropdownComponent(
            label: "Divisi",
            controller: divisiCon,
            editable: true,
            marginBottom: 0,
          ),
        ),
      ],
    );
  }

  void filter(bool isPeriod) async {
    if (isPeriod) {
      dariTanggal.value = dariTanggalCon.value.toString();
      sampaiTanggal.value = sampaiTanggalCon.value.toString();
      Get.back(result: true);
      await listCon.refresh();
    } else {
      dariTanggal.value =
          "${DateTime.now().year}-${DateTime.now().month}-${DateTime.now().day}";
      sampaiTanggal.value =
          "${DateTime.now().year}-${DateTime.now().month}-${DateTime.now().day}";
      Get.back(result: true);
      await listCon.refresh();
    }
  }

  Future<void> getDivisi() async {
    var url = '';
    if (seeAllDivisi.value) {
      url = "/api/Divisi/All";
    } else {
      url = "/api/Divisi";
    }
    var r = await HttpApi.get(url);
    if (r.success) {
      RxList<DivisiModel> listModel = RxList<DivisiModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(DivisiModel.fromDynamic(e));
      }
      if (r.body != null && divisiCon.items.isEmpty) {
        divisiCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      }
    } else {
      Helper.fetchErrorMessage(r, httpMethod: HttpMethod.get);
    }
  }
}
