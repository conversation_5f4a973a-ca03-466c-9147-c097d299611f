import 'dart:convert';

class InformasicutitahunanModel {
  int? tahun;
  int? quota;
  int? terpakai;
  int? sisaCuti;

  InformasicutitahunanModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static InformasicutitahunanModel fromDynamic(dynamic dynamicData) {
    final model = InformasicutitahunanModel();

    model.tahun = dynamicData['tahun'];
    model.quota = dynamicData['quota'];
    model.terpakai = dynamicData['terpakai'];
    model.sisaCuti = dynamicData['sisaCuti'];

    return model;
  }
}
