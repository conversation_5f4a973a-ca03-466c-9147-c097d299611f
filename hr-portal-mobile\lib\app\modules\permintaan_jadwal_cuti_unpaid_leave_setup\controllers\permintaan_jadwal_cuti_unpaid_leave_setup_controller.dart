import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:path/path.dart' as p;

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/components/inputs/input_lookup_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/models/api_result_model.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../models/cuti_unpaid_leave_model.dart';
import '../../../models/jenis_kontak_model.dart';
import '../../../models/pegawai_approval.dart';

class PermintaanJadwalCutiUnpaidLeaveSetupController extends GetxController {
  late SetupPageController formCon;
  final berapaHariCon = InputTextController(type: InputTextType.number);
  final alasanCon = InputTextController(type: InputTextType.paragraf);
  final pegawaiCutiCon = InputTextController();
  final fileCon = InputFileController(
    urlImage: true,
    type: FileType.custom,
    tipe: InputFileType.pdf,
    extension: ["pdf", "jpg", "jpeg", "png"],
  );
  final staffPenggantiCon = InputLookupController<LookUpPegawaiModel>(
    fromDynamic: (e) => LookUpPegawaiModel.fromDynamic(e),
    itemText: (e) => e.nama ?? "",
    itemValue: (e) => e.id,
    subtitleText: (e) => "NIP: ${e?.nip ?? '-'}",
    urlApi: (index, filter) =>
        '/api/PermintaanJadwal/CutiUnpaidLeave/Pegawai?pageIndex=$index&filter=$filter',
  );
  final tglKerjaCon = InputDatetimeController();
  final lamakerjaCon = InputTextController();
  final keteranganCon = InputTextController(type: InputTextType.paragraf);
  final sampaiTanggalCon = InputDatetimeController();
  final dariTanggalCon = InputDatetimeController();
  final noHpCon = InputTextController(type: InputTextType.number);
  final alasanTolakCon = InputTextController();
  final String nama = MahasConfig.profile!.nama!;
  late String approve;
  late bool allowED;
  Rxn<bool> accKadiv = Rxn<bool>();
  Rxn<bool> accManager = Rxn<bool>();
  RxBool isVisible = false.obs;
  late RxString pegawaiKadiv = ''.obs;
  late RxString pegawaiManager = ''.obs;
  late String kadiv;
  late bool isKadiv;
  RxString statusTolak = ''.obs;
  RxBool isBatal = false.obs;
  RxInt idPegawaiRequest = 0.obs;
  final isDinamisApprove = MahasConfig.profileMenu.approvalDinamis;
  RxBool isStop = false.obs;
  RxList<PegawaiApprovalModel> modelApproval = <PegawaiApprovalModel>[].obs;
  int? id;
  RxString tglPengajuan = ''.obs;
  RxBool isValidMasaKerja = false.obs;

  @override
  void onInit() {
    cekKadiv();
    cekApproval();
    initFormPegawai();
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PermintaanJadwal/CutiUnpaidLeave/$id',
      urlApiPost: () => '/api/PermintaanJadwal/CutiUnpaidLeave',
      urlApiPut: (id) => '/api/PermintaanJadwal/CutiUnpaidLeave/$id',
      urlApiDelete: (id) => '/api/PermintaanJadwal/CutiUnpaidLeave/$id',
      allowDelete: allowED,
      allowEdit: allowED,
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "dariTanggal": MahasFormat.dateToString(dariTanggalCon.value),
        "sampaiTanggal": MahasFormat.dateToString(sampaiTanggalCon.value),
        "jumlahHari": berapaHariCon.value,
        "alasan": alasanCon.value,
        "delegasi": staffPenggantiCon.text,
        "id_PegawaiDelegasi": staffPenggantiCon.value?.id,
        "noHp": "0${noHpCon.value}",
        "filePhoto": fileCon.value,
        "fileName": fileCon.name
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (isValidMasaKerja.value == false) {
          Helper.dialogWarning(
              "Tidak dapat mengambil Izin Tidak Ditanggung Perusahaan karena masa kerja kurang dari 3 tahun");
          return false;
        }
        if (!dariTanggalCon.isValid) return false;
        if (!sampaiTanggalCon.isValid) return false;
        berapaHariCon.value =
            sampaiTanggalCon.value.difference(dariTanggalCon.value).inDays + 1;
        if (!alasanCon.isValid) return false;
        if (!noHpCon.isValid) return false;
        if (dariTanggalCon.value.difference(DateTime.now()).inDays < 30) {
          Helper.dialogWarning(
              "Maaf, pengajuan Izin Tanpa Ditanggung Perusahaan minimal 30 hari sebelumnya. Silakan sesuaikan durasi izin");
          return false;
        }
        if (!staffPenggantiCon.isValid) return false;
        String noHp = noHpCon.value.toString();
        if (noHp.length < 10 ||
            noHp.length > 13 ||
            !RegExp(r'^[0-9]+$').hasMatch(noHp)) {
          Helper.dialogWarning("Nomor HP tidak valid");
          return false;
        }
        if (berapaHariCon.value < 30) {
          Helper.dialogWarning(
              "Maaf, pengajuan Izin Tanpa Ditanggung Perusahaan minimal 1 bulan. Silakan sesuaikan durasi izin.");
          return false;
        } else if (berapaHariCon.value > 90) {
          Helper.dialogWarning(
              "Maaf, pengajuan Izin Tanpa Ditanggung Perusahaan maksimal 3 bulan. Silakan sesuaikan durasi izin");
          return false;
        }
        return true;
      },
      successMessage: Helper.succesPengajuanMessage(),
      apiToView: (json) {
        CutiUnpaiLeaveModel model = CutiUnpaiLeaveModel.fromJson(json);
        id = model.id;
        pegawaiCutiCon.value = model.pegawairequest;
        dariTanggalCon.value = model.daritanggal;
        sampaiTanggalCon.value = model.sampaitanggal;
        berapaHariCon.value = model.jumlahhari;
        alasanCon.value = model.alasan;
        noHpCon.value = model.nohp;

        accManager.value = model.approvemanager;
        accKadiv.value = model.approvekadiv;
        pegawaiKadiv.value = model.pegawaikadiv!;
        pegawaiManager.value = model.pegawaimanager!;
        idPegawaiRequest.value = model.idPegawaiRequest!;
        staffPenggantiCon.value = LookUpPegawaiModel.init(
          model.idPegawaiDelegasi,
          model.delegasi,
        );
        fileCon.value = model.pathfoto;

        var format = DateFormat("dd MMMM yyyy HH:mm");
        tglPengajuan.value = format.format(model.tanggalinput!);

        statusTolak.value = model.alasantolak ?? '';

        // approval
        if (isDinamisApprove == false && allowED == false) {
          if ((model.approvekadiv == null &&
                  model.idPegawaiKadiv == MahasConfig.profile!.id) ||
              (model.approvekadiv == true &&
                  model.approvemanager == null &&
                  model.idPegawaiManager == MahasConfig.profile!.id)) {
            isVisible.value = true;
          }
        }

        if (isDinamisApprove == true && allowED == false) {
          getDataApproval();
        }

        if (model.pathfoto != null) {
          String extension = p.extension(model.pathfoto!).toLowerCase();
          if (extension == ".pdf") {
            updateState(InputFileType.pdf);
          } else {
            updateState(InputFileType.image);
          }
        }
      },
    );
    staffPenggantiCon.onChanged = () {
      cekNoWa(staffPenggantiCon.value?.id.toString());
    };
    staffPenggantiCon.clearOnTab = () {
      noHpCon.value = null;
    };
    cekValidasi();
    super.onInit();
  }

  void updateState(InputFileType tipe) {
    fileCon.tipe = tipe;
  }

  void cekValidasi() {
    dariTanggalCon.onChanged = () {
      if (sampaiTanggalCon.value != null) {
        berapaHariCon.value =
            sampaiTanggalCon.value.difference(dariTanggalCon.value).inDays + 1;
      }
    };
    sampaiTanggalCon.onChanged = () {
      if (dariTanggalCon.value != null) {
        berapaHariCon.value =
            sampaiTanggalCon.value.difference(dariTanggalCon.value).inDays + 1;
      }
    };
    sampaiTanggalCon.onCheck = (date) {
      DateTime dariTanggal = dariTanggalCon.value;
      DateTime sampaiTanggal = date ?? DateTime.now();
      if (sampaiTanggal.isBefore(dariTanggal)) {
        Helper.dialogWarning(
            "Tanggal Selesai tidak boleh kurang dari Tanggal Mulai");
        return false;
      }
      return true;
    };
    sampaiTanggalCon.onClear = () {
      berapaHariCon.value = null;
    };
    dariTanggalCon.onClear = () {
      berapaHariCon.value = null;
    };
  }

  Future<void> initFormPegawai() async {
    tglKerjaCon.value = MahasConfig.profile?.tanggalmulaibekerja;

    var lamaKerjaValue = await lamaKerja();
    if (isValidMasaKerja.value == false) {
      Helper.dialogWarning(
          "Tidak dapat mengambil Izin Tidak Ditanggung Perusahaan karena masa kerja kurang dari 3 tahun");
    }
    lamakerjaCon.value = lamaKerjaValue;
  }

  Future<String> lamaKerja() async {
    var tanggalMulaiBekerja = MahasConfig.profile?.tanggalmulaibekerja;

    if (tanggalMulaiBekerja != null) {
      var diff = DateTime.now().difference(tanggalMulaiBekerja).inDays;
      if (diff < 1095) {
        isValidMasaKerja.value = false;
      } else {
        isValidMasaKerja.value = true;
      }

      var tahun = diff ~/ 365;
      var sisaHari = diff % 365;
      var bulan = sisaHari ~/ 30;

      return '${tahun > 0 ? '$tahun Tahun' : ''}${tahun > 0 && bulan > 0 ? ' ' : ''}${bulan > 0 ? '$bulan Bulan' : ''} ${tahun > 0 || bulan > 0 ? 'dan ' : ''}${sisaHari % 30} Hari';
    }

    return '';
  }

  Future<void> approvalOnPress(bool approve) async {
    var id = Get.parameters['id'].toString();
    String urlKadiv = '/api/PermintaanJadwal/CutiUnpaidLeave/Approve/Kadiv/';
    String urlManager =
        '/api/PermintaanJadwal/CutiUnpaidLeave/Approve/Manager/';
    String urlApprove = '/api/PermintaanJadwal/CutiUnpaidLeave/Approve/';
    var action = await Helper.approvalAction(
      id,
      approve,
      alasanTolakCon.value,
      accKadiv.value,
      urlKadiv,
      urlManager,
      isDinamis: isDinamisApprove,
      urlApprove: urlApprove,
    );
    if (action.success) {
      final url = '/api/PermintaanJadwal/CutiUnpaidLeave/$id';
      ApiResultModel r;
      r = await HttpApi.get(url);
      if (r.success) {
        CutiUnpaiLeaveModel model = CutiUnpaiLeaveModel.fromJson(r.body);
        accKadiv.value = model.approvekadiv;
        accManager.value = model.approvemanager;
        statusTolak.value = model.alasantolak ?? '';
        if (isDinamisApprove) {
          getDataApproval();
        }
        isVisible.value = false;
        update();
      } else if (MahasConfig.hasInternet == false) {
        Helper.dialogWarning(
            "Gagal melakukan approve, silahkan cek koneksi internet");
      } else if (!action.success) {
        Helper.dialogWarning(action.message);
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan approve, silahkan cek koneksi internet");
    } else if (!action.success) {
      Helper.dialogWarning(
        "Gagal melakukan approval",
        resMassage: action.message,
        noInternetMassage:
            "Gagal melakukan approval, silahkan cek koneksi internet",
      );
    }
  }

  void cekKadiv() {
    kadiv = Get.parameters['isKadiv'].toString();
    if (kadiv == "false") {
      isKadiv = false;
    } else {
      isKadiv = true;
    }
  }

  void cekApproval() {
    approve = Get.parameters['approval'].toString();
    if (approve == "approval") {
      allowED = false;
    } else {
      allowED = true;
    }
  }

  void cekNoWa(String? id) async {
    if (formCon.isState == SetupPageState.create) {
      var r = await HttpApi.get('/api/PegawaiJenisKontak/Jenis/$id/Whatsapp');
      if (r.success) {
        var data = PegawaiKontakModel.fromJson(r.body);
        noHpCon.value = data.nama ?? '';
      }
    }
  }

  Future<void> getDataApproval() async {
    EasyLoading.show();
    final url = '/api/PermintaanJadwal/CutiUnpaidLeave/Approval/$id';
    final r = await HttpApi.get(url);
    modelApproval.clear();
    if (r.success) {
      if (r.body != null) {
        var data = json.decode(r.body);
        for (var e in data['datas']) {
          modelApproval.add(PegawaiApprovalModel.fromDynamic(e));
        }
        bool canApprove = modelApproval.any((item) =>
            item.idPegawaiapprove == MahasConfig.profile?.id &&
            item.approve == null);
        if (canApprove) {
          isVisible.value = true;
        }
        bool notApprove = modelApproval.any((item) => item.approve == false);
        if (notApprove) {
          isVisible.value = false;
        }
        isStop.value = modelApproval.any((item) => item.approve == false);
      } else {
        Helper.dialogWarning(r.message);
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan aksi, silahkan cek koneksi internet");
    } else if (r.statusCode == 404) {
      Helper.dialogWarning("Tidak Ada Data Pegawai Approval");
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }
}
