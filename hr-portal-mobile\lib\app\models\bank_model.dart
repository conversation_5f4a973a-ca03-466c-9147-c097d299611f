import 'dart:convert';

import '../mahas/services/mahas_format.dart';

class BankModel {
  int? id;
  String? nama;

  BankModel();

  static BankModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static BankModel fromDynamic(dynamic dynamicData) {
    final model = BankModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.nama = dynamicData['nama'];

    return model;
  }
}
