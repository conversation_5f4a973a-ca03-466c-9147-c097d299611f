import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_colors.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../mahas_complement/mahas_colors.dart';
import '../controllers/profile_foto_history_setup_controller.dart';

class ProfileFotoHistorySetupView
    extends GetView<ProfileFotoHistorySetupController> {
  const ProfileFotoHistorySetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      controller: controller.formCon,
      title: "Informasi",
      children: () => [
        //display header in history
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              MahasFormat.displayDate(
                controller.tglPermintaan.value,
              ),
              style: MahasThemes.muted,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                    controller.approvalFromNotif.value
                        ? "Dari : ${controller.namaPegawaiRequest}"
                        : "Kepada : ${controller.namaHRD}",
                    style: MahasThemes.muted),
                const SizedBox(
                  height: 5,
                ),
              ],
            ),
            const SizedBox(
              height: 15,
            ),
          ],
        ),
        Obx(
          () => ClipRRect(
            borderRadius: BorderRadius.circular(5),
            child: controller.imageFile.value != null
                ? Image.file(controller.imageFile.value!)
                : Image.memory(
                    controller.images.value!,
                  ),
          ),
        ),
        const SizedBox(
          height: 5,
        ),
        Visibility(
          visible: controller.formCon.isState == SetupPageState.update,
          child: TextButton(
            style: TextButton.styleFrom(minimumSize: const Size(30, 12)),
            onPressed: () {
              controller.getFromGallery();
            },
            child: const Text("Edit"),
          ),
        ),
        //status request
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(
              height: 15,
            ),
            const Text(
              'Status Request',
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(
              height: 5,
            ),
            Container(
              decoration: BoxDecoration(
                  border: Border.all(
                    color: MahasColor.primaryGrey,
                  ),
                  borderRadius:
                      BorderRadius.circular(MahasThemes.borderRadius)),
              padding: const EdgeInsets.all(10),
              child: Obx(
                () => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'HRD',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(controller.namaHRD.value),
                    controller.statusApprovalHRD.value == null
                        ? const Row(
                            children: [
                              Icon(
                                FontAwesomeIcons.clock,
                                size: 12,
                              ),
                              Text(" Menunggu")
                            ],
                          )
                        : controller.statusApprovalHRD.value == true
                            ? const Row(
                                children: [
                                  Icon(
                                    FontAwesomeIcons.check,
                                    size: 12,
                                    color: MahasColors.green,
                                  ),
                                  Text(
                                    ' Diterima',
                                    style: TextStyle(
                                      color: MahasColors.green,
                                    ),
                                  ),
                                ],
                              )
                            : controller.statusApprovalHRD.value == false
                                ? const Row(
                                    children: [
                                      Icon(
                                        FontAwesomeIcons.circleXmark,
                                        size: 12,
                                        color: MahasColors.red,
                                      ),
                                      Text(
                                        " Ditolak",
                                        style:
                                            TextStyle(color: MahasColors.red),
                                      ),
                                    ],
                                  )
                                : const Row(),
                    controller.statusApprovalHRD.value == false
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                  'Alasan : "${controller.alasanTolakHRD.value}"'),
                            ],
                          )
                        : const SizedBox(),
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 20,
            ),
          ],
        ),

        //approval buttons
        Obx(
          () {
            return Visibility(
              visible: controller.approvalButtonsVisible.value,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Expanded(
                    child: ElevatedButton(
                        onPressed: () {
                          alert("tolak");
                        },
                        style: ElevatedButton.styleFrom(
                            backgroundColor: MahasColors.red),
                        child: const Text("Tolak")),
                  ),
                  const SizedBox(
                    width: 20,
                  ),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        controller.approvalAction(true);
                      },
                      child: const Text("Terima"),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  //alasan tolak pop up
  Future<dynamic> alert(String status) {
    return Get.dialog(
      AlertDialog(
        content: SizedBox(
          width: 1000,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              InputTextComponent(
                label: status == "tolak" ? "Alasan Ditolak" : "Alasan Batal",
                controller: controller.alasanTolakCon,
                required: true,
              ),
            ],
          ),
        ),
        contentPadding:
            const EdgeInsets.only(bottom: 0, top: 20, right: 10, left: 10),
        actionsPadding:
            const EdgeInsets.only(top: 0, bottom: 0, left: 10, right: 10),
        actions: [
          TextButton(
            child: Text(
              "Close",
              style: MahasThemes.muted,
            ),
            onPressed: () {
              Get.back(result: false);
            },
          ),
          TextButton(
            child: const Text(
              "Simpan",
            ),
            onPressed: () async {
              await controller.approvalAction(false);
              Get.back(result: true);
            },
          ),
        ],
      ),
    );
  }
}
