import 'dart:convert';

import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../models/profile_hobi_model.dart';

class ProfileHobiSetupController extends GetxController {
  late SetupPageController formCon;
  final hobiCon = InputTextController();

  @override
  void onInit() {
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PegawaiHobi/$id',
      urlApiPost: () => '/api/PegawaiHobi',
      urlApiPut: (id) => '/api/PegawaiHobi/$id',
      urlApiDelete: (id) => '/api/PegawaiHobi/$id',
      bodyApi: (id) => {
        "nama": hobiCon.value,
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (!hobiCon.isValid) return false;

        return true;
      },
      apiToView: (json) {
        ProfilehobiModel model = ProfilehobiModel.fromJson(json);

        hobiCon.value = model.nama;
      },
    );
    super.onInit();
  }
}
