import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../controllers/profile_kontrak_setup_controller.dart';

class ProfileKontrakSetupView extends GetView<ProfileKontrakSetupController> {
  const ProfileKontrakSetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      controller: controller.formCon,
      title: '<PERSON><PERSON><PERSON>',
      children: () => [
        Visibility(
          visible: controller.namaJabatanCon.value != '',
          child: InputTextComponent(
            label: "Jabatan",
            controller: controller.namaJabatanCon,
            editable: false,
          ),
        ),
        InputDatetimeComponent(
          label: '<PERSON>gal Begabung',
          controller: controller.tglBergabungCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDatetimeComponent(
          controller: controller.tglBerlakuCon,
          label: '<PERSON><PERSON>',
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDatetimeComponent(
          controller: controller.tglBerakhirCon,
          label: 'Tanggal Berakhir Perjanjian Kerja',
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          controller: controller.expiredKontrakCon,
          label: 'Sisa Hari Expired Kontrak',
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Lama Waktu Bekerja",
          controller: controller.waktuBekerjaCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Sisa Hari Expired Pensiun",
          controller: controller.expiredPensiunCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "No Surat",
          controller: controller.noSuratCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "No SK",
          controller: controller.noSkCon,
          required: true,
          editable: controller.formCon.editable,
        ),
      ],
    );
  }
}
