import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/models/menu_item_model.dart';
import 'package:hr_portal/app/routes/app_pages.dart';

class InformasiSerahTerimaController extends GetxController {
  final List<MenuItemModel> menus = [];

  void cMutasiBarang() {
    Get.toNamed(Routes.INFORMASI_SERAH_TERIMA_MUTASI_BARANG);
  }

  @override
  void onInit() {
    menus.add(
        MenuItemModel('<PERSON><PERSON><PERSON>', FontAwesomeIcons.dolly, cMutasiBarang));
    super.onInit();
  }
}
