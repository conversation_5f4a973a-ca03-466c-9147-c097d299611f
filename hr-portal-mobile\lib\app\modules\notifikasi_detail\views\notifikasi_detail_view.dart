import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/notifikasi_detail_controller.dart';

class NotifikasiDetailView extends GetView<NotifikasiDetailController> {
  const NotifikasiDetailView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Notifikasi"),
        actions: const [],
      ),
      body: Container(
        padding: const EdgeInsets.all(10.0),
        child: Center(
          child: Obx(() => Text(controller.message.value)),
        ),
      ),
    );
  }
}
