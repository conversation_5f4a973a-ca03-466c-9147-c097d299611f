import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/others/list_component.dart';
import 'package:hr_portal/app/models/mutasi_barang_model.dart';
import 'package:hr_portal/app/routes/app_pages.dart';

class InformasiSerahTerimaMutasiBarangController extends GetxController {
  late ListComponentController<MutasiBarangModel> listCon;

  @override
  void onInit() {
    listCon = ListComponentController<MutasiBarangModel>(
      urlApi: (index, filter) => '/api/Mutasi/List?pageIndex=$index',
      fromDynamic: (e) => MutasiBarangModel.fromDynamic(e),
      allowSearch: false,
    );
    super.onInit();
  }

  void scanOnTap() {
    Get.toNamed(Routes.QR_CODE_SCANNER);
  }

  void itemOnTap(String id) {
    Get.toNamed(
      Routes.INFORMASI_SERAH_TERIMA_MUTASI_BARANG_SETUP,
      parameters: {"id": id},
    )?.then((value) => listCon.refresh());
  }
}
