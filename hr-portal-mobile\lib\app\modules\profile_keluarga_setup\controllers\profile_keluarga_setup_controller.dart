import 'dart:convert';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_checkbox_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_radio_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/models/api_list_resut_model.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../mahas_complement/input_detail_setup_component.dart';
import '../../../models/hubungan_keluarga_model.dart';
import '../../../models/jenis_kontak_model.dart';
import '../../../models/profile_keluarga_model.dart';
import '../../../routes/app_pages.dart';

class ProfileKeluargaSetupController extends GetxController {
  late SetupPageController formCon;

  final hubunganKeluargaCon = InputDropdownController();
  final namaCon = InputTextController();
  final jenisKelaminCon = InputRadioController(
    items: [
      RadioButtonItem(text: "Laki-laki", value: 1),
      RadioButtonItem(text: "Perempuan", value: 2),
    ],
  );
  final alamatDomisiliCon = InputTextController(type: InputTextType.paragraf);
  final noBpjsCon = InputTextController();
  final noBpjsTkCon = InputTextController();
  final ditanggungPegawaiCon = InputCheckboxController();

  // setup list detail
  late InputDetailSetupControler<ProfilekeluargaDetailkeluargakontakModel>
      detailSetupCon;
  final jenisKontakCon = InputDropdownController();
  final ketKontakCon = InputTextController();

  RxInt idPegawaiKontak = 0.obs;
  RxString namaLabel = 'Nama'.obs;

  @override
  void onInit() async {
    detailSetUp();
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PegawaiKeluarga/$id',
      urlApiPost: () => '/api/PegawaiKeluarga',
      urlApiPut: (id) => '/api/PegawaiKeluarga/$id',
      urlApiDelete: (id) => '/api/PegawaiKeluarga/$id',
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "id_PegawaiKeluarga": idPegawaiKontak.value,
        "id_HubunganKeluarga": hubunganKeluargaCon.value,
        "nama": namaCon.value,
        "jenisKelamin": jenisKelaminCon.value == 1 ? "Laki-laki" : "Perempuan",
        "alamatDomisili": alamatDomisiliCon.value,
        "noBPJS": noBpjsCon.value,
        "noBPJSTK": noBpjsTkCon.value,
        "ditanggungPegawai": ditanggungPegawaiCon.checked,
        "detailPegawaiJenisKontak": detailSetupCon.values
            .map((e) => {
                  "id_JenisKontak": e.value.idJeniskontak,
                  "nama": e.value.nama,
                })
            .toList()
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (!hubunganKeluargaCon.isValid) return false;
        if (!namaCon.isValid) return false;
        if (!jenisKelaminCon.isValid) return false;
        if (!alamatDomisiliCon.isValid) return false;
        if (!noBpjsCon.isValid) return false;
        if (!noBpjsTkCon.isValid) return false;

        return true;
      },
      apiToView: (json) {
        ProfilekeluargaModel model = ProfilekeluargaModel.fromJson(json);
        hubunganKeluargaCon.value = model.idHubungankeluarga;
        namaCon.value = model.nama;
        jenisKelaminCon.value = null;
        if (model.jeniskelamin!.isNotEmpty) {
          jenisKelaminCon.value = model.jeniskelamin == "Laki-laki" ? 1 : 2;
        }
        alamatDomisiliCon.value = model.alamatdomisili;
        noBpjsCon.value = model.nobpjs;
        noBpjsTkCon.value = model.nobpjstk;
        if (model.ditanggungpegawai == true) {
          ditanggungPegawaiCon.checked = true;
        } else {
          ditanggungPegawaiCon.checked = false;
        }
        namaLabel.value = jenisKontakCon.label ?? "Nama";
        detailSetupCon.clear();
        for (var e in model.detailkeluargakontak!) {
          detailSetupCon.addValue(
            ProfilekeluargaDetailkeluargakontakModel.init(
              e.idJeniskontak,
              e.nama,
              e.namajeniskontak,
            ),
          );
        }

        //non mahas
        idPegawaiKontak.value = model.id ?? 0;
      },
      onInit: () async {
        await getKontak();
      },
    );

    if (MahasConfig.hasHistory) {
      formCon.onSuccessSubmit = (r) {
        var idHistory = json.decode(r.body)['id'];
        Get.toNamed(
          Routes.PROFILE_KELUARGA_HISTORY_SETUP,
          parameters: {
            'id': idHistory.toString(),
            'showStatus': true.toString(),
            'withActionBack': true.toString(),
          },
        );
      };
      formCon.deleteOnTap = (id) {
        Get.toNamed(
          Routes.PROFILE_KELUARGA_HISTORY_SETUP,
          parameters: {
            'id': id.toString(),
            'showStatus': true.toString(),
            'withActionBack': true.toString(),
          },
        );
      };
    }

    super.onInit();
  }

  //start chain get data
  Future<void> getKontak() async {
    if (EasyLoading.isShow) return;
    EasyLoading.show();
    var r = await HttpApi.get("/api/JenisKontak");
    if (r.success) {
      RxList<JenisKontakModel> listModel = RxList<JenisKontakModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(JenisKontakModel.fromDynamic(e));
      }
      if (r.body != null && jenisKontakCon.items.isEmpty) {
        jenisKontakCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id, label: e.namalabel),
            )
            .toList();
      }
      jenisKontakCon.onChangedVoid = () {
        getLabel();
      };
    } else {
      Helper.fetchErrorMessage(r);
      EasyLoading.dismiss();
    }
    await getHubunganKeluarga();
  }

  void getLabel() {
    namaLabel.value = jenisKontakCon.label ?? "Nama";
  }

  Future<void> getHubunganKeluarga() async {
    var r = await HttpApi.get("/api/HubunganKeluarga");
    if (r.success) {
      RxList<HubunganKeluargaModel> listModel = RxList<HubunganKeluargaModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(HubunganKeluargaModel.fromDynamic(e));
      }
      if (r.body != null && hubunganKeluargaCon.items.isEmpty) {
        hubunganKeluargaCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      }
    } else {
      Helper.fetchErrorMessage(r);
    }
    EasyLoading.dismiss();
  }
  //end chain get data

  void detailSetUp() {
    detailSetupCon =
        InputDetailSetupControler<ProfilekeluargaDetailkeluargakontakModel>(
      itemKey: (e) => e.id,
      fromDynamic: (e) =>
          ProfilekeluargaDetailkeluargakontakModel.fromDynamic(e),
      itemText: (e) => e.nama ?? "-",
      onOpenForm: (id, e) {
        jenisKontakCon.value = e?.idJeniskontak;
        ketKontakCon.value = e?.nama;
        namaLabel.value = jenisKontakCon.label ?? "Nama";
      },
      onFormInsert: (id) {
        if (!jenisKontakCon.isValid) return null;
        if (!ketKontakCon.isValid) return null;
        var r = ProfilekeluargaDetailkeluargakontakModel();
        r.id = id;
        r.idJeniskontak = jenisKontakCon.value;
        r.namajeniskontak = jenisKontakCon.text;
        r.nama = ketKontakCon.value;

        return r;
      },
    );
  }
}
