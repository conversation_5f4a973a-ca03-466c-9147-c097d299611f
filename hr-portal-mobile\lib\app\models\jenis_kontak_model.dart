import 'dart:convert';

import '../mahas/services/mahas_format.dart';

class JenisKontakModel {
  int? id;
  String? nama;
  String? namalabel;

  JenisKontakModel();

  static JenisKontakModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static JenisKontakModel fromDynamic(dynamic dynamicData) {
    final model = JenisKontakModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.nama = dynamicData['nama'];
    model.namalabel = dynamicData['namaLabel'];

    return model;
  }
}

class PegawaiKontakModel {
  int? id;
  int? idPegawai;
  int? idJeniskontak;
  String? namajeniskontak;
  String? nama;

  PegawaiKontakModel();

  static PegawaiKontakModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static PegawaiKontakModel fromDynamic(dynamic dynamicData) {
    final model = PegawaiKontakModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.idJeniskontak =
        MahasFormat.dynamicToInt(dynamicData['id_JenisKontak']);
    model.namajeniskontak = dynamicData['namaJenisKontak'];
    model.nama = dynamicData['nama'];

    return model;
  }
}
