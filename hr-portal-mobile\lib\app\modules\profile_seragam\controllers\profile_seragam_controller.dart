import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../models/profile_seragam_model.dart';
import '../../../routes/app_pages.dart';

class ProfileSeragamController extends GetxController {
  final listCon = ListComponentController<SeragamModel>(
    urlApi: (index, filter) => '/api/PegawaiSeragam/List?pageIndex=$index',
    fromDynamic: SeragamModel.fromDynamic,
    allowSearch: false,
  );

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PROFILE_SERAGAM_SETUP,
      parameters: {
        'id': id.toString(),
      },
    )?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }

  void addOnPress() {
    Get.toNamed(Routes.PROFILE_SERAGAM_SETUP)?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }
}
