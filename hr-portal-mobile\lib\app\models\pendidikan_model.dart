import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class PendidikanModel {
  int? id;
  String? nama;

  PendidikanModel();

  static PendidikanModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static PendidikanModel fromDynamic(dynamic dynamicData) {
    final model = PendidikanModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.nama = dynamicData['nama'];

    return model;
  }
}
