// ignore_for_file: prefer_typing_uninitialized_variables, unused_local_variable

import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_datetime_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_radio_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_text_component.dart';
import 'package:hr_portal/app/mahas/components/pages/setup_page_component.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:hr_portal/app/mahas/models/api_result_model.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/mahas/services/http_api.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';
import 'package:hr_portal/app/models/cuti_hamil_model.dart';
import 'package:intl/intl.dart';
import 'package:path/path.dart' as p;

import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../models/pegawai_approval.dart';

class PermintaanJadwalCutiHamilSetupController extends GetxController {
  late SetupPageController formCon;
  final anakCon = InputTextController(
    type: InputTextType.number,
  );
  final dariTglCon = InputDatetimeController();
  final sampaiTglCon = InputDatetimeController();
  final berapaHariCon = InputTextController(type: InputTextType.number);
  final radioCon = InputRadioController(
    items: [
      RadioButtonItem.simple("1 Bulan"),
      RadioButtonItem.simple("2 Bulan"),
      RadioButtonItem.simple("3 Bulan"),
    ],
  );
  final tglHplCon = InputDatetimeController();

  final fileCon = InputFileController(
    urlImage: true,
    type: FileType.custom,
    tipe: InputFileType.pdf,
    extension: ["pdf", "jpg", "jpeg", "png"],
  );

  final alasanTolakCon = InputTextController();
  final pegawaiRequestCon = InputTextController();
  final String nama = MahasConfig.profile!.nama!;
  late String approve;
  late bool allowED;
  Rxn<bool> accKadiv = Rxn<bool>();
  Rxn<bool> accManager = Rxn<bool>();
  RxBool isVisible = false.obs;
  RxBool isBatal = false.obs;
  late RxString pegawaiKadiv = ''.obs;
  late RxString pegawaiManager = ''.obs;
  CutihamilModel model = CutihamilModel();
  Rx<DateTime> sampaiTanggal = DateTime.now().obs;
  RxString statusTolak = ''.obs;
  RxInt idPegawaiRequest = 0.obs;
  final isDinamisApprove = MahasConfig.profileMenu.approvalDinamis;
  RxBool isStop = false.obs;
  RxList<PegawaiApprovalModel> modelApproval = <PegawaiApprovalModel>[].obs;
  int? id;
  RxString tglPengajuan = ''.obs;

  @override
  void onInit() {
    cekApproval();
    tglHplCon.onChanged = () {
      if (tglHplCon.value != null) {
        DateTime hpl = tglHplCon.value;
        dariTglCon.value = hpl.subtract(const Duration(days: 45));
        sampaiTglCon.value = hpl.add(const Duration(days: 44));
      }
    };
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PermintaanJadwal/CutiHamil/$id',
      urlApiPost: () => '/api/PermintaanJadwal/CutiHamil',
      urlApiPut: (id) => '/api/PermintaanJadwal/CutiHamil/$id',
      urlApiDelete: (id) => '/api/PermintaanJadwal/CutiHamil/$id',
      allowDelete: allowED,
      allowEdit: allowED,
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "anakKe": anakCon.value,
        "dariTanggal": MahasFormat.dateToString(dariTglCon.value),
        "sampaiTanggal": MahasFormat.dateToString(
            MahasConfig.dinamisForm.cutiHamil?.selamaVisible == true
                ? sampaiTanggal.value
                : sampaiTglCon.value),
        "berapaHari": berapaHariCon.value,
        "tanggalHpl": MahasFormat.dateToString(tglHplCon.value),
        "filePhoto": fileCon.value,
        "fileName": fileCon.name
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (!anakCon.isValid) return false;
        if (!tglHplCon.isValid) return false;
        if (!dariTglCon.isValid) return false;
        if (dariTglCon.value
            .isBefore(DateTime.now().subtract(const Duration(days: 1)))) {
          Helper.dialogWarning(
              "Tanggal Mulai tidak boleh kurang dari hari ini");
          return false;
        }
        if (MahasConfig.dinamisForm.cutiHamil?.anakKeVisible == true) {
          if (anakCon.value < 1) {
            Helper.dialogWarning("Cuti Hamil Minimal Kuota \nAnak Ke-1!");
            return false;
          } else if (anakCon.value > 3) {
            Helper.dialogWarning("Cuti Hamil Melebihi Kuota \nAnak Ke-3");
            return false;
          }
        }
        if (MahasConfig.dinamisForm.cutiHamil?.selamaVisible == true) {
          if (!radioCon.isValid) return false;
          if (radioCon.value == "1 Bulan") {
            var today = dariTglCon.value;
            sampaiTanggal.value = today.add(const Duration(days: 30));
            berapaHariCon.value = 30;
          } else if (radioCon.value == "2 Bulan") {
            var today = dariTglCon.value;
            sampaiTanggal.value = today.add(const Duration(days: 60));
            berapaHariCon.value = 60;
          } else if (radioCon.value == "3 Bulan") {
            var today = dariTglCon.value;
            sampaiTanggal.value = today.add(const Duration(days: 90));
            berapaHariCon.value = 90;
          }
        }
        if (MahasConfig.dinamisForm.cutiHamil?.berapaHariVisible == true) {
          berapaHariCon.value =
              sampaiTglCon.value.difference(dariTglCon.value).inDays + 1;
          if (sampaiTglCon.value.isBefore(dariTglCon.value)) {
            Helper.dialogWarning(
                "Tanggal Selesei tidak boleh kurang dari Tanggal Mulai");
            return false;
          }
        }

        return true;
      },
      successMessage: Helper.succesPengajuanMessage(),
      apiToView: (json) {
        model = CutihamilModel.fromJson(json);
        id = model.id;
        anakCon.value = model.anakke;
        dariTglCon.value = model.daritanggal;
        sampaiTglCon.value = model.sampaitanggal;
        berapaHariCon.value = model.berapahari;
        accManager.value = model.approvemanager;
        accKadiv.value = model.approvekadiv;
        pegawaiKadiv.value = model.pegawaikadiv!;
        pegawaiManager.value = model.pegawaimanager!;

        pegawaiRequestCon.value = model.pegawairequest;
        idPegawaiRequest.value = model.idPegawaiRequest!;
        tglHplCon.value = model.tanggalhpl;
        fileCon.value = model.pathfoto;

        var format = DateFormat("dd MMMM yyyy HH:mm");
        tglPengajuan.value = format.format(model.tanggalinput!);

        Duration diff = model.sampaitanggal!.difference(model.daritanggal!);
        if (diff.inDays == 30) {
          radioCon.value = "1 Bulan";
        } else if (diff.inDays == 60) {
          radioCon.value = "2 Bulan";
        } else if (diff.inDays == 90) {
          radioCon.value = "3 Bulan";
        }

        statusTolak.value = model.alasantolak ?? '';

        // approval
        if (isDinamisApprove == false && allowED == false) {
          if ((model.approvekadiv == null &&
                  model.idPegawaiKadiv == MahasConfig.profile!.id) ||
              (model.approvekadiv == true &&
                  model.approvemanager == null &&
                  model.idPegawaiManager == MahasConfig.profile!.id)) {
            isVisible.value = true;
          }
        }

        // batal
        if (MahasConfig.dinamisForm.approval?.bisaBatal == true) {
          if (isDinamisApprove == false && allowED == false) {
            if (model.approvekadiv == true &&
                model.approvemanager == true &&
                model.idPegawaiManager == MahasConfig.profile!.id) {
              isBatal.value = true;
            }
          }
        }
        if (isDinamisApprove == true && allowED == false) {
          getDataApproval();
        }

        if (model.pathfoto != null) {
          String extension = p.extension(model.pathfoto!).toLowerCase();
          if (extension == ".pdf") {
            updateState(InputFileType.pdf);
          } else {
            updateState(InputFileType.image);
          }
        }
      },
    );

    super.onInit();
  }

  void updateState(InputFileType tipe) {
    fileCon.tipe = tipe;
  }

  void cekApproval() {
    approve = Get.parameters['approval'].toString();
    if (approve == "approval") {
      allowED = false;
    } else {
      allowED = true;
    }
  }

  Future<void> batalOnPress() async {
    var id = Get.parameters['id'].toString();
    String urlManager = '/api/PermintaanJadwal/CutiHamil/Batal/Manager/';
    var action = await Helper.batalAction(
      id,
      alasanTolakCon.value,
      urlManager,
    );
    if (action.success) {
      final url = '/api/PermintaanJadwal/CutiHamil/$id';
      ApiResultModel r;
      r = await HttpApi.get(url);
      if (r.success) {
        CutihamilModel model = CutihamilModel.fromJson(r.body);
        accKadiv.value = model.approvekadiv;
        accManager.value = model.approvemanager;
        statusTolak.value = model.alasantolak ?? '';
        isBatal.value = false;
        update();
      } else {
        Helper.fetchErrorMessage(r);
      }
    } else {
      Helper.fetchErrorMessage(action, httpMethod: HttpMethod.post);
    }
  }

  Future<void> approvalOnPress(bool approve) async {
    var id = Get.parameters['id'].toString();
    String urlKadiv = '/api/PermintaanJadwal/CutiHamil/Approve/Kadiv/';
    String urlManager = '/api/PermintaanJadwal/CutiHamil/Approve/Manager/';
    String urlApprove = '/api/PermintaanJadwal/CutiHamil/Approve/';
    var action = await Helper.approvalAction(
      id,
      approve,
      alasanTolakCon.value,
      accKadiv.value,
      urlKadiv,
      urlManager,
      isDinamis: isDinamisApprove,
      urlApprove: urlApprove,
    );
    if (action.success) {
      final url = '/api/PermintaanJadwal/CutiHamil/$id';
      ApiResultModel r;
      r = await HttpApi.get(url);
      if (r.success) {
        CutihamilModel model = CutihamilModel.fromJson(r.body);
        accKadiv.value = model.approvekadiv;
        accManager.value = model.approvemanager;
        statusTolak.value = model.alasantolak ?? '';
        if (isDinamisApprove) {
          getDataApproval();
        }
        isVisible.value = false;
        update();
      } else {
        Helper.fetchErrorMessage(r);
      }
    } else {
      Helper.fetchErrorMessage(action, httpMethod: HttpMethod.post);
    }
  }

  Future<void> getDataApproval() async {
    EasyLoading.show();
    final url = '/api/PermintaanJadwal/CutiHamil/Approval/$id';
    final r = await HttpApi.get(url);
    modelApproval.clear();
    if (r.success) {
      if (r.body != null) {
        var data = json.decode(r.body);
        for (var e in data['datas']) {
          modelApproval.add(PegawaiApprovalModel.fromDynamic(e));
        }
        bool canApprove = modelApproval.any((item) =>
            item.idPegawaiapprove == MahasConfig.profile?.id &&
            item.approve == null);
        if (canApprove) {
          isVisible.value = true;
        }
        bool notApprove = modelApproval.any((item) => item.approve == false);
        if (notApprove) {
          isVisible.value = false;
        }
        isStop.value = modelApproval.any((item) => item.approve == false);
      } else {
        Helper.dialogWarning(r.message);
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan aksi, silahkan cek koneksi internet");
    } else if (r.statusCode == 404) {
      Helper.dialogWarning("Tidak Ada Data Pegawai Approval");
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }
}
