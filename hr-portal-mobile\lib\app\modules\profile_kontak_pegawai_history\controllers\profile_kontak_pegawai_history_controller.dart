import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../models/profile_kontak_model.dart';
import '../../../routes/app_pages.dart';

class ProfileKontakPegawaiHistoryController extends GetxController {
  final listCon = ListComponentController<ProfilekontakModel>(
    urlApi: (index, filter) =>
        '/api/PegawaiJenisKontak/History/List?pageIndex=$index',
    fromDynamic: ProfilekontakModel.fromDynamic,
    allowSearch: false,
  );

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PROFILE_KONTAK_PEGAWAI_HISTORY_SETUP,
      parameters: {
        'id': id.toString(),
        'showStatus': true.toString(),
      },
    )?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }
}
