import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_colors.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../mahas_complement/mahas_colors.dart';
import '../controllers/profile_jenis_sertifikasi_history_setup_controller.dart';

class ProfileJenisSertifikasiHistorySetupView
    extends GetView<ProfileJenisSertifikasiHistorySetupController> {
  const ProfileJenisSertifikasiHistorySetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      controller: controller.formCon,
      title: "Informasi",
      children: () => [
        //display header in history
        GetBuilder(
          builder: (ProfileJenisSertifikasiHistorySetupController controller) =>
              Visibility(
            visible:
                controller.approvalFromNotif.value || controller.showStatus,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  MahasFormat.displayDate(
                    controller.tglPermintaan.value,
                  ),
                  style: MahasThemes.muted,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                        controller.approvalFromNotif.value
                            ? "Dari : ${controller.namaPegawaiRequest}"
                            : "Kepada : ${controller.namaHRD}",
                        style: MahasThemes.muted),
                    const SizedBox(
                      height: 5,
                    ),
                    controller.status.value == "UPDATE"
                        ? Row(
                            children: [
                              Icon(
                                FontAwesomeIcons.penToSquare,
                                size: 15,
                                color: MahasColors.warning,
                              ),
                              Text(
                                " Ubah",
                                style: MahasThemes.muted
                                    .copyWith(color: MahasColors.warning),
                              ),
                            ],
                          )
                        : controller.status.value == "ADD"
                            ? Row(
                                children: [
                                  const Icon(
                                    FontAwesomeIcons.plus,
                                    size: 15,
                                    color: MahasColors.green,
                                  ),
                                  Text(
                                    " Tambah",
                                    style: MahasThemes.muted
                                        .copyWith(color: MahasColors.green),
                                  ),
                                ],
                              )
                            : controller.status.value == "DELETE"
                                ? Row(
                                    children: [
                                      Icon(
                                        FontAwesomeIcons.trash,
                                        size: 15,
                                        color: MahasColors.danger,
                                      ),
                                      Text(
                                        " Hapus",
                                        style: MahasThemes.muted.copyWith(
                                            color: MahasColors.danger),
                                      ),
                                    ],
                                  )
                                : const SizedBox(),
                  ],
                ),
                const SizedBox(
                  height: 15,
                ),
              ],
            ),
          ),
        ),

        InputDropdownComponent(
          label: "Jenis Sertifikasi",
          controller: controller.jenisSertifikasiCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: 'Nama Sertifikasi',
          controller: controller.namaSertifikasiCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: 'No Sertifikasi',
          controller: controller.noSertifikasiCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDatetimeComponent(
          controller: controller.tanggalCon,
          label: 'Tanggal',
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDatetimeComponent(
          controller: controller.berlakuSampaiCon,
          label: 'Berlaku Sampai',
          required: true,
          editable: controller.formCon.editable,
        ),
        InputFileComponent(
          controller: controller.berkasPendukungCon,
          required: true,
          label: 'Berkas Pendukung (*pdf)',
          editable: controller.formCon.editable,
        ),

        //status request
        Visibility(
          visible: controller.approvalFromNotif.value || controller.showStatus,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 15,
              ),
              const Text(
                'Status Request',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(
                height: 5,
              ),
              Container(
                decoration: BoxDecoration(
                    border: Border.all(
                      color: MahasColor.primaryGrey,
                    ),
                    borderRadius:
                        BorderRadius.circular(MahasThemes.borderRadius)),
                padding: const EdgeInsets.all(10),
                child: GetBuilder(
                  builder: (ProfileJenisSertifikasiHistorySetupController
                          controller) =>
                      Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'HRD',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(controller.namaHRD.value),
                      controller.statusApprovalHRD == null
                          ? const Row(
                              children: [
                                Icon(
                                  FontAwesomeIcons.clock,
                                  size: 12,
                                ),
                                Text(" Menunggu")
                              ],
                            )
                          : controller.statusApprovalHRD == true
                              ? const Row(
                                  children: [
                                    Icon(
                                      FontAwesomeIcons.check,
                                      size: 12,
                                      color: MahasColors.green,
                                    ),
                                    Text(
                                      ' Diterima',
                                      style: TextStyle(
                                        color: MahasColors.green,
                                      ),
                                    ),
                                  ],
                                )
                              : controller.statusApprovalHRD == false
                                  ? const Row(
                                      children: [
                                        Icon(
                                          FontAwesomeIcons.circleXmark,
                                          size: 12,
                                          color: MahasColors.red,
                                        ),
                                        Text(
                                          " Ditolak",
                                          style:
                                              TextStyle(color: MahasColors.red),
                                        ),
                                      ],
                                    )
                                  : const Row(),
                      controller.statusApprovalHRD == false
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                    'Alasan : "${controller.alasanTolakHRD.value}"'),
                              ],
                            )
                          : const SizedBox(),
                    ],
                  ),
                ),
              ),
              const SizedBox(
                height: 20,
              ),
            ],
          ),
        ),

        //approval buttons
        Obx(
          () {
            return Visibility(
              visible: controller.approvalButtonsVisible.value,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Expanded(
                    child: ElevatedButton(
                        onPressed: () {
                          alert("tolak");
                        },
                        style: ElevatedButton.styleFrom(
                            backgroundColor: MahasColors.red),
                        child: const Text("Tolak")),
                  ),
                  const SizedBox(
                    width: 20,
                  ),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        controller.approvalAction(true);
                      },
                      child: const Text("Terima"),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

//alasan tolak pop up
  Future<dynamic> alert(String status) {
    return Get.dialog(
      AlertDialog(
        content: SizedBox(
          width: 1000,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              InputTextComponent(
                label: status == "tolak" ? "Alasan Ditolak" : "Alasan Batal",
                controller: controller.alasanTolakCon,
                required: true,
              ),
            ],
          ),
        ),
        contentPadding:
            const EdgeInsets.only(bottom: 0, top: 20, right: 10, left: 10),
        actionsPadding:
            const EdgeInsets.only(top: 0, bottom: 0, left: 10, right: 10),
        actions: [
          TextButton(
            child: Text(
              "Close",
              style: MahasThemes.muted,
            ),
            onPressed: () {
              Get.back(result: false);
            },
          ),
          TextButton(
            child: const Text(
              "Simpan",
            ),
            onPressed: () async {
              if (controller.alasanTolakCon.isValid) {
                await controller.approvalAction(false);
                Get.back(result: true);
              }
            },
          ),
        ],
      ),
    );
  }
}
