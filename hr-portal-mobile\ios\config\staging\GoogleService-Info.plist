<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>171917817792-jsufcs4ri8cc6d95g2uq4ad351mu1ap4.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.171917817792-jsufcs4ri8cc6d95g2uq4ad351mu1ap4</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>171917817792-t5qok4mghkjp8j7d9t0b6v650f4vtkoc.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyCOj5qu11m96gH49Xz5iwO8kJ_xDL-vxDw</string>
	<key>GCM_SENDER_ID</key>
	<string>171917817792</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.sanata.hrportal.staging</string>
	<key>PROJECT_ID</key>
	<string>hr-portal-staging</string>
	<key>STORAGE_BUCKET</key>
	<string>hr-portal-staging.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:171917817792:ios:bf088a84ad3e38ed37fa25</string>
</dict>
</plist>