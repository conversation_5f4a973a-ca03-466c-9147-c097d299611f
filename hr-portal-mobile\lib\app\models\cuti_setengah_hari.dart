import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class CutiSetengahHariModel {
  int? id;
  int? idDivisi;
  DateTime? tanggalinput;
  bool? approvekadiv;
  bool? approvemanager;
  DateTime? tanggaljam;
  String? keterangan;
  String? alasan;
  int? idPegawaiRequest;
  String? alasantolak;
  String? divisi;
  String? pegawairequest;
  String? pegawaikadiv;
  String? pegawaimanager;
  String? approvemethod;
  int? idUnitbisnis;
  int? idPegawaiKadiv;
  int? idPegawaiManager;

  CutiSetengahHariModel();

  static CutiSetengahHariModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static CutiSetengahHariModel fromDynamic(dynamic dynamicData) {
    final model = CutiSetengahHariModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idDivisi = MahasFormat.dynamicToInt(dynamicData['id_Divisi']);
    model.tanggalinput =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalInput']);
    model.approvekadiv = MahasFormat.dynamicToBool(dynamicData['approveKadiv']);
    model.approvemanager =
        MahasFormat.dynamicToBool(dynamicData['approveManager']);
    model.tanggaljam = MahasFormat.dynamicToDateTime(dynamicData['tanggalJam']);
    model.keterangan = dynamicData['keterangan'];
    model.alasan = dynamicData['alasan'];
    model.idPegawaiRequest =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Request']);
    model.alasantolak = dynamicData['alasanTolak'];
    model.divisi = dynamicData['divisi'];
    model.pegawairequest = dynamicData['pegawaiRequest'];
    model.pegawaikadiv = dynamicData['pegawaiKadiv'];
    model.pegawaimanager = dynamicData['pegawaiManager'];
    model.approvemethod = dynamicData['approveMethod'];
    model.idUnitbisnis = MahasFormat.dynamicToInt(dynamicData['id_UnitBisnis']);
    model.idPegawaiKadiv =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Kadiv']);
    model.idPegawaiManager =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Manager']);

    return model;
  }
}
