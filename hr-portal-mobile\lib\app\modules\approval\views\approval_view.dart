import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/mahas_colors.dart';
import 'package:hr_portal/app/mahas_complement/mahas_colors.dart';

import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/others/list_component.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../models/approval_model.dart';
import '../controllers/approval_controller.dart';

class ApprovalView extends GetView<ApprovalController> {
  const ApprovalView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Approval'),
        centerTitle: true,
      ),
      body: menus(),
    );
  }

  Obx menus() {
    return Obx(
      () => Container(
        padding: const EdgeInsets.all(10),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
              height: 52,
              // width: 100,
              decoration: const BoxDecoration(
                color: MahasColor.greyInputan,
                borderRadius: BorderRadius.all(
                  Radius.circular(14.0),
                ),
              ),
              child: Row(
                children: List.generate(
                  controller.menus.length,
                  (index) {
                    var item = controller.menus[index];

                    return Expanded(
                      child: InkWell(
                        onTap: () {
                          controller.selectedIndex.value = index;
                          item.onTab();
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12.0,
                            vertical: 8.0,
                          ),
                          decoration: BoxDecoration(
                            color: index == controller.selectedIndex.value
                                ? MahasColors.primary
                                : MahasColor.greyInputan,
                            borderRadius: const BorderRadius.all(
                              Radius.circular(8.0),
                            ),
                          ),
                          child: Center(
                            child: Text(
                              item.title,
                              style: TextStyle(
                                color: index == controller.selectedIndex.value
                                    ? Colors.white
                                    : MahasColors.primary,
                                fontSize: 11.0,
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Expanded(
              child: listApi(),
            ),
          ],
        ),
      ),
    );
  }

  ListComponent<ApprovalModel> listApi() {
    return ListComponent(
      controller: controller.listCon,
      itemBuilder: (ApprovalModel e) {
        switch (e.jenis) {
          case 6:
            return cardApprovalLembur(e);
          default:
            return cardApproval(e);
        }
      },
    );
  }

  Row cardApproval(ApprovalModel item) {
    return Row(
      children: [
        InkWell(
          onTap: () async =>
              await Helper.fotoProfilOnTap(item.idPegawairequest!),
          child: Padding(
            padding:
                const EdgeInsets.only(left: 12, right: 8, top: 5, bottom: 5),
            child: CircleAvatar(
              radius: 25,
              backgroundColor: MahasColors.primary,
              child: CircleAvatar(
                backgroundColor: Colors.white,
                radius: 23,
                child: Center(
                  child: Text(
                    "Lihat\nFoto",
                    style: MahasThemes.link.copyWith(fontSize: 12),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
          ),
        ),
        Expanded(
          child: InkWell(
            onTap: () => controller.itemOnTab(item.id!, item.jenis!),
            child: Padding(
              padding: const EdgeInsets.only(right: 12, top: 8, bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(controller.displayJenis(item.jenis!),
                            style: MahasThemes.title),
                        const SizedBox(height: 5),
                        Text(item.pegawairequest.toString()),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        "Tanggal Input",
                        style: MahasColor.muted,
                      ),
                      Text(
                        MahasFormat.displayDate(item.tanggalinput),
                        style: MahasColor.muted,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Row cardApprovalLembur(ApprovalModel item) {
    return Row(
      children: [
        InkWell(
          onTap: () async =>
              await Helper.fotoProfilOnTap(item.idPegawairequest!),
          child: Padding(
            padding:
                const EdgeInsets.only(left: 12, right: 8, top: 5, bottom: 5),
            child: CircleAvatar(
              radius: 25,
              backgroundColor: MahasColors.primary,
              child: CircleAvatar(
                backgroundColor: Colors.white,
                radius: 23,
                child: Center(
                  child: Text(
                    "Lihat\nFoto",
                    style: MahasThemes.link.copyWith(fontSize: 12),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
          ),
        ),
        Expanded(
          child: InkWell(
            onTap: () => controller.itemOnTab(item.id!, item.jenis!),
            child: Padding(
              padding: const EdgeInsets.only(right: 12, top: 8, bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(controller.displayJenis(item.jenis!),
                            style: MahasThemes.title),
                        const SizedBox(height: 5),
                        Text(item.pegawairequest.toString()),
                        const SizedBox(height: 5),
                        Text('${item.lamaJam} jam lembur'),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        "Tanggal Lembur",
                        style: MahasColor.muted,
                      ),
                      Text(
                        MahasFormat.displayDate(item.tanggalJam),
                        style: MahasColor.muted,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
