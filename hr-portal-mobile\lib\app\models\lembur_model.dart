import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';

class LemburModel {
  int? id;
  int? idDivisi;
  int? idPegawai;
  int? idPegawaiKadiv;
  DateTime? tanggalinput;
  String? alasan;
  String? alasanTolak;
  bool? approvemanager;
  DateTime? tanggaljam;
  DateTime? tanggallembur;
  DateTime? tanggaljamSelesai;
  int? lamajam;
  int? lamahari;
  int? lamamenit;
  String? jenis;
  String? pegawai;
  String? pegawaikadiv;
  String? pegawaimanager;
  String? divisi;
  String? keterangan;
  String? namaPegawai;
  int? idPegawaiManager;
  TimeOfDay? jamLembur;
  bool? approve;

  LemburModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static LemburModel fromDynamic(dynamic dynamicData) {
    final model = LemburModel();

    model.id = dynamicData['id'];
    model.idDivisi = dynamicData['id_Divisi'];
    model.idPegawai = dynamicData['id_Pegawai'];
    model.idPegawaiKadiv = dynamicData['id_Pegawai_Kadiv'];
    model.tanggalinput =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalInput']);
    model.alasan = dynamicData['alasan'];
    model.alasanTolak = dynamicData['alasanTolak'];
    model.approvemanager = dynamicData['approveManager'];
    model.approve = dynamicData['approve'];
    model.tanggaljam = MahasFormat.dynamicToDateTime(dynamicData['tanggalJam']);
    model.tanggallembur =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalLembur']);
    model.jamLembur = MahasFormat.stringToTime(dynamicData['jamLembur']);
    model.tanggaljamSelesai =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalJamSelesai']);
    model.lamajam = dynamicData['lamaJam'];
    model.lamamenit = dynamicData['lamaMenit'];
    model.lamahari = dynamicData['lamaHari'];
    model.jenis = dynamicData['jenis'];
    model.pegawai = dynamicData['pegawai'];
    model.namaPegawai = dynamicData['namaPegawai'];
    model.keterangan = dynamicData['keterangan'];
    model.pegawaikadiv = dynamicData['pegawaiKadiv'];
    model.pegawaimanager = dynamicData['pegawaiManager'];
    model.divisi = dynamicData['divisi'];
    model.divisi = dynamicData['keterangan'];
    model.idPegawaiManager = dynamicData['id_Pegawai_Manager'];

    return model;
  }
}

class LookuplemburModel {
  int? id;
  String? nama;

  LookuplemburModel();
  LookuplemburModel.init(this.id, this.nama);

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static LookuplemburModel fromDynamic(dynamic dynamicData) {
    final model = LookuplemburModel();

    model.id = dynamicData['id'];
    model.nama = dynamicData['nama'];

    return model;
  }
}
