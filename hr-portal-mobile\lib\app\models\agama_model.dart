import 'dart:convert';

import '../mahas/services/mahas_format.dart';

class AgamaModel {
  int? id;
  String? nama;

  AgamaModel();

  static AgamaModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static AgamaModel fromDynamic(dynamic dynamicData) {
    final model = AgamaModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.nama = dynamicData['nama'];

    return model;
  }
}
