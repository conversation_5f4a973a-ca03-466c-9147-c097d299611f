import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';

import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/others/list_component.dart';
import '../../../models/profile_kontrak_model.dart';
import '../controllers/profile_kontrak_controller.dart';

class ProfileKontrakView extends GetView<ProfileKontrakController> {
  const ProfileKontrakView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Kontrak'),
        centerTitle: true,
        // actions: <Widget>[
        //   Padding(
        //     padding: const EdgeInsets.only(right: 10),
        //     child: GestureDetector(
        //       onTap: controller.addOnPress,
        //       child: const Icon(
        //         FontAwesomeIcons.plus,
        //         size: 24,
        //       ),
        //     ),
        //   ),
        // ],
      ),
      body: ListComponent(
        controller: controller.listCon,
        itemBuilder: (KontrakModel e) {
          return InkWell(
            onTap: () => controller.itemOnTab(e.id!),
            child: Padding(
              padding:
                  const EdgeInsets.only(left: 12, right: 12, top: 8, bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 5),
                        Row(
                          children: [
                            Text("No Surat : ", style: MahasThemes.title),
                            Text(e.nosurat.toString(),
                                style: MahasThemes.title),
                          ],
                        ),
                        Row(
                          children: [
                            const Text("Tanggal Berlaku   : "),
                            Text(MahasFormat.displayDate(e.tanggalberlaku)),
                          ],
                        ),
                        Row(
                          children: [
                            const Text("Tanggal Berakhir : "),
                            Text(MahasFormat.displayDate(e.tanggalberakhir)),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
