import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class PerpusModel {
  int? id;
  String? tentang;
  int? idKategoriperpus;
  int? nomor;
  int? tahun;
  DateTime? tanggaldiundangkan;
  DateTime? tanggaldokumenterbit;
  String? namafile;
  String? pathfile;
  String? kategoriperpusnama;

  PerpusModel();

  static PerpusModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static PerpusModel fromDynamic(dynamic dynamicData) {
    final model = PerpusModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.tentang = dynamicData['tentang'];
    model.idKategoriperpus =
        MahasFormat.dynamicToInt(dynamicData['id_KategoriPerpus']);
    model.nomor = MahasFormat.dynamicToInt(dynamicData['nomor']);
    model.tahun = MahasFormat.dynamicToInt(dynamicData['tahun']);
    model.tanggaldiundangkan =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalDiundangkan']);
    model.tanggaldokumenterbit =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalDokumenTerbit']);
    model.namafile = dynamicData['namaFile'];
    model.pathfile = dynamicData['pathFile'];
    model.kategoriperpusnama = dynamicData['kategoriPerpusNama'];

    return model;
  }
}

class KategoriPerpusModel {
  int? id;
  String? nama;

  KategoriPerpusModel();

  static KategoriPerpusModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static KategoriPerpusModel fromDynamic(dynamic dynamicData) {
    final model = KategoriPerpusModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.nama = dynamicData['nama'];

    return model;
  }
}
