import 'dart:convert';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/mahas/services/http_api.dart';
import 'package:hr_portal/app/models/profile_divisi_model.dart';

class ProfileDivisiController extends GetxController {
  var models = RxList<ProfiledivisiModel>();
  var isLoading = false.obs;

  Future<void> onRefresh() async {
    if (isLoading.isTrue) return;
    isLoading.value = true;
    var r = await HttpApi.get('/api/Profile/Divisi');
    if (r.success) {
      final datas = json.decode(r.body);
      models.clear();
      for (var e in datas) {
        models.add(ProfiledivisiModel.fromDynamic(e));
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning("Gagal memuat data, silahkan cek koneksi internet");
    }
    isLoading.value = false;
  }

  @override
  void onInit() {
    super.onInit();
    onRefresh();
  }
}
