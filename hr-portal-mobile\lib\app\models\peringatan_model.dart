import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class PeringatanModel {
  int? id;
  int? idPegawai;
  String? peringatan;
  String? judul;
  String? keterangan;
  DateTime? tanggal;
  String? pathfile;

  PeringatanModel();

  static PeringatanModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static PeringatanModel fromDynamic(dynamic dynamicData) {
    final model = PeringatanModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.peringatan = dynamicData['peringatan'];
    model.judul = dynamicData['judul'];
    model.keterangan = dynamicData['keterangan'];
    model.tanggal = MahasFormat.dynamicToDateTime(dynamicData['tanggal']);
    model.pathfile = dynamicData['pathFile'];

    return model;
  }
}
