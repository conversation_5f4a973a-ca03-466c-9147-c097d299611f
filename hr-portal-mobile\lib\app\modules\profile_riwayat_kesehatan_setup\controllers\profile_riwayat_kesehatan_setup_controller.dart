import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../models/profile_riwayat_kesehatan_model.dart';
import '../../../routes/app_pages.dart';

class ProfileRiwayatKesehatanSetupController extends GetxController {
  late SetupPageController formCon;
  final fileCon = InputFileController(
    type: FileType.custom,
    extension: ['pdf'],
    tipe: InputFileType.pdf,
  );
  final nomorCon = InputTextController();
  final tanggalCon = InputDatetimeController();

  RxInt idPegawaiRiwayatKesehatan = 0.obs;

  @override
  void onInit() {
    formCon = SetupPageController(
      allowDelete: true,
      allowEdit: true,
      urlApiGet: (id) => '/api/PegawaiRiwayatKesehatan/$id',
      urlApiDelete: (id) => '/api/PegawaiRiwayatKesehatan/$id',
      urlApiPut: (id) => '/api/PegawaiRiwayatKesehatan/$id',
      urlApiPost: () => '/api/PegawaiRiwayatKesehatan',
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "nomor": nomorCon.value,
        "tanggal": MahasFormat.dateToString(tanggalCon.value),
        "berkasPendukung": fileCon.value,
        "id_PegawaiRiwayatKesehatan": idPegawaiRiwayatKesehatan.value,
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (!nomorCon.isValid) return false;
        if (!tanggalCon.isValid) return false;
        if (!fileCon.isValid) return false;

        return true;
      },
      apiToView: (json) {
        RiwayatkesehatanModel model = RiwayatkesehatanModel.fromJson(json);

        //mahascomponent
        nomorCon.value = model.nomor;
        tanggalCon.value = model.tanggal;
        fileCon.value = model.berkaspendukung;

        //non mahas
        idPegawaiRiwayatKesehatan.value = model.id ?? 0;
      },
    );

    if (MahasConfig.hasHistory) {
      formCon.onSuccessSubmit = (r) {
        var idHistory = json.decode(r.body)['id'];
        Get.toNamed(
          Routes.PROFILE_RIWAYAT_KESEHATAN_HISTORY_SETUP,
          parameters: {
            'id': idHistory.toString(),
            'showStatus': true.toString(),
            'withActionBack': true.toString(),
          },
        );
      };
      formCon.deleteOnTap = (id) {
        Get.toNamed(
          Routes.PROFILE_RIWAYAT_KESEHATAN_HISTORY_SETUP,
          parameters: {
            'id': id.toString(),
            'showStatus': true.toString(),
            'withActionBack': true.toString(),
          },
        );
      };
    }

    super.onInit();
  }
}
