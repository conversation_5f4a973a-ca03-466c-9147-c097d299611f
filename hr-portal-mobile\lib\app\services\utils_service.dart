import 'dart:convert';

import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../mahas/mahas_config.dart';
import '../mahas/services/helper.dart';
import '../mahas/services/http_api.dart';
import '../mahas/services/mahas_format.dart';
import '../models/profile_kgb_model.dart';
import '../models/profile_pegawai_str_model.dart';
import '../models/profile_sertifikasi_model.dart';

class UtilsService {
  static final storange = GetStorage();

  static void checkBirthday() {
    RxBool popUpBool = false.obs;
    DateTime time = DateTime.now();
    DateTime? birthDate = MahasConfig.profile!.tanggalLahir;
    var dateRead = storange.read('date');
    String? resetTime;
    if (birthDate != null) {
      if (time.day == birthDate.day && time.month == birthDate.month) {
        if (dateRead == null) {
          popUpBool.value = true;
          resetTime =
              "${time.year}-${time.month.toString().padLeft(2, '0')}-${time.day.toString().padLeft(2, '0')} 23:59:59";
          storange.write('date', resetTime);
        } else {
          if (time.isAfter(DateTime.parse(dateRead.toString()))) {
            storange.remove('date');
          }
        }
      } else {
        if (dateRead != null) {
          if (time.isAfter(DateTime.parse(dateRead.toString()))) {
            storange.remove('date');
          }
        }
      }
    }

    if (popUpBool.value == true) {
      Future.delayed(const Duration(milliseconds: 100))
          .then((value) => Helper.dialogBirthday(MahasConfig.profile!.nama));
      popUpBool.value = false;
    }
  }

  static void checkKontrak() {
    var dateRead = storange.read('dateKontrak');
    DateTime hariIni = DateTime.now();
    DateTime? dateReadDate;
    if (dateRead != null) {
      dateReadDate = DateTime.tryParse(dateRead);
    }
    DateTime? akhirKontrak = MahasConfig.profile!.tanggalselesaibekerja;
    String akhirKontrakText = MahasFormat.displayDate(akhirKontrak);
    DateTime sebulan = DateTime(hariIni.year, hariIni.month + 1, hariIni.day);

    if (akhirKontrak!.isBefore(sebulan) ||
        akhirKontrak.isAtSameMomentAs(sebulan)) {
      DateTime perTigaHariNotif = dateReadDate != null
          ? dateReadDate.add(const Duration(days: 3))
          : hariIni;
      if (dateRead == null || perTigaHariNotif.isBefore(hariIni)) {
        Future.delayed(const Duration(milliseconds: 100)).then(
          (value) => Helper.dialogWarning(
              "Kontrak Kerja Anda Akan Segera Berakhir Pada Tanggal $akhirKontrakText, Mohon Hubungi Bagian Terkait Untuk Melakukan Perpanjangan Kontrak Kerja"),
        );
        storange.write('dateKontrak', hariIni.toString());
      }
    } else {
      storange.remove('dateKontrak');
    }
  }

  static void checkSIP() async {
    var r = await HttpApi.get('/api/PegawaiStr/List');
    List<PegawaistrModel> sipList = [];
    if (r.success) {
      final data = json.decode(r.body);
      final datas = data['datas'];
      sipList.clear();
      for (var e in datas) {
        sipList.add(PegawaistrModel.fromDynamic(e));
      }
    }

    for (var i = 0; i < sipList.length; i++) {
      var dateRead = storange.read('dateSip$i');
      DateTime hariIni = DateTime.now();
      DateTime? dateReadDate;
      if (dateRead != null) {
        dateReadDate = DateTime.tryParse(dateRead);
      }
      var item = sipList[i];

      DateTime? akhirSip = item.tanggalberakhirsip;
      String akhirSipText = MahasFormat.displayDate(item.tanggalberakhirsip);
      DateTime sebulan = DateTime(hariIni.year, hariIni.month + 1, hariIni.day);

      if (akhirSip!.isBefore(sebulan) || akhirSip.isAtSameMomentAs(sebulan)) {
        DateTime perTigaHariNotif = dateReadDate != null
            ? dateReadDate.add(const Duration(days: 3))
            : hariIni;
        if (dateRead == null || perTigaHariNotif.isBefore(hariIni)) {
          Future.delayed(const Duration(milliseconds: 100)).then(
            (value) => Helper.dialogWarning(
                "SIP anda akan segera berakhir pada tanggal $akhirSipText, harap hubungi bagian bersangkutan untuk proses pengurusan SIP"),
          );
          storange.write('dateSip$i', hariIni.toString());
        }
      } else {
        storange.remove('dateSip$i');
      }
    }
  }

  static void checkKGB() async {
    var r = await HttpApi.get('/api/PegawaiKGB/History/List');
    List<KgbModel> kgbList = [];
    if (r.success) {
      final data = json.decode(r.body);
      final datas = data['datas'];
      kgbList.clear();
      for (var e in datas) {
        kgbList.add(KgbModel.fromDynamic(e));
      }
    }

    if (kgbList.isNotEmpty) {
      var dateRead = storange.read('dateKgb');
      DateTime hariIni = DateTime.now();
      DateTime? dateReadDate;
      if (dateRead != null) {
        dateReadDate = DateTime.tryParse(dateRead);
      }
      var item = kgbList[0];

      DateTime? akhirKgb = item.kgbberikutnya;
      String akhirKgbText = MahasFormat.displayDate(item.kgbberikutnya);
      DateTime tigabulan =
          DateTime(hariIni.year, hariIni.month + 3, hariIni.day);

      if (akhirKgb!.isBefore(tigabulan) ||
          akhirKgb.isAtSameMomentAs(tigabulan)) {
        DateTime perHariNotif = dateReadDate != null
            ? dateReadDate.add(const Duration(days: 7))
            : hariIni;
        if (dateRead == null || perHariNotif.isBefore(hariIni)) {
          Future.delayed(const Duration(milliseconds: 100)).then(
            (value) => Helper.dialogWarning(
                "KGB selanjutnya akan diperbarui pada tanggal $akhirKgbText, mohon hubungi bagian bersangkutan untuk proses pengurusan dokumen"),
          );
          storange.write('dateKgb', hariIni.toString());
        }
      } else {
        storange.remove('dateKgb');
      }
    }
  }

  static void checkSertifikasi() async {
    var r = await HttpApi.get('/api/PegawaiSertifikasi/List');
    List<SertifikasiModel> sertifikasiList = [];
    if (r.success) {
      final data = json.decode(r.body);
      final datas = data['datas'];
      sertifikasiList.clear();
      for (var e in datas) {
        sertifikasiList.add(SertifikasiModel.fromDynamic(e));
      }
    }

    for (var i = 0; i < sertifikasiList.length; i++) {
      var dateRead = storange.read('dateSertifikasi$i');
      DateTime hariIni = DateTime.now();
      DateTime? dateReadDate;
      if (dateRead != null) {
        dateReadDate = DateTime.tryParse(dateRead);
      }
      var item = sertifikasiList[i];

      DateTime? akhirSertif = item.berlakusampai;
      var noSertifikasi = item.nosertifikasi;
      var jenisSertifikasi = item.namasertifikasi;
      String akhirSipText = MahasFormat.displayDate(item.berlakusampai);
      DateTime sebulan = DateTime(hariIni.year, hariIni.month + 1, hariIni.day);

      if (akhirSertif!.isBefore(sebulan) ||
          akhirSertif.isAtSameMomentAs(sebulan)) {
        DateTime perTigaHariNotif = dateReadDate != null
            ? dateReadDate.add(const Duration(days: 3))
            : hariIni;
        if (dateRead == null || perTigaHariNotif.isBefore(hariIni)) {
          Future.delayed(const Duration(milliseconds: 100)).then(
            (value) => Helper.dialogWarning(
                "No sertifikasi $noSertifikasi - jenis $jenisSertifikasi segera berakhir pada tanggal $akhirSipText"),
          );
          storange.write('dateSertifikasi$i', hariIni.toString());
        }
      } else {
        storange.remove('dateSertifikasi$i');
      }
    }
  }
}
