import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:hr_portal/app/mahas/components/mahas_themes.dart';
import 'package:hr_portal/app/mahas/components/others/empty_component.dart';
import 'package:hr_portal/app/mahas/components/others/shimmer_component.dart';
import 'package:intl/intl.dart';

import '../../../mahas/mahas_colors.dart';
import '../controllers/informasi_quota_tukar_lembur_controller.dart';

class InformasiQuotaTukarLemburView
    extends GetView<InformasiQuotaTukarLemburController> {
  const InformasiQuotaTukarLemburView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tukar Lembur'),
        centerTitle: true,
      ),
      body: Obx(() {
        return RefreshIndicator(
          backgroundColor: Colors.white,
          onRefresh: controller.onRefresh,
          child: controller.isLoading.value
              ? const ShimmerComponent()
              : controller.noData.value
                  ? EmptyComponent(
                      onPressed: controller.onRefresh,
                    )
                  : ListView(
                      children: [
                        Container(
                          padding: const EdgeInsets.only(
                              top: 10, left: 10, right: 10),
                          child: Obx(
                            () => Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Visibility(
                                  visible: !controller.isLoading.value,
                                  child: Column(
                                    children: controller.models
                                        .map(
                                          (e) => Container(
                                            padding: const EdgeInsets.all(10),
                                            margin: const EdgeInsets.only(
                                                bottom: 10),
                                            decoration: BoxDecoration(
                                              border: Border.all(
                                                color: MahasColors.grey
                                                    .withValues(alpha: .5),
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      MahasThemes.borderRadius),
                                            ),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.stretch,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.start,
                                              children: [
                                                Text("Jenis Tukar",
                                                    style: MahasThemes.muted),
                                                Text("${e.jenis}"),
                                                const Padding(
                                                    padding: EdgeInsets.all(5)),
                                                Text("Lama lembur",
                                                    style: MahasThemes.muted),
                                                Text("${e.lamajam} jam"),
                                                const Padding(
                                                    padding: EdgeInsets.all(5)),
                                                Text("Tanggal Lembur",
                                                    style: MahasThemes.muted),
                                                Text(DateFormat("dd MMMM yyyy")
                                                    .format(e.tanggaljam!)),
                                                const Padding(
                                                    padding:
                                                        EdgeInsets.all(10)),
                                              ],
                                            ),
                                          ),
                                        )
                                        .toList(),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                      ],
                    ),
        );
      }),
    );
  }
}
