import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class RiwayatpendidikanModel {
  int? id;
  int? idPegawai;
  String? pegawainama;
  int? idPendidikan;
  String? namapendidikan;
  int? idKlasifikasipendidikan;
  String? namaklasifikasipendidikan;
  int? daritahun;
  int? sampaitahun;
  String? keterangan;
  String? berkaspendukung;
  String? programstudi;
  String? nomorijazah;
  String? alasantolak;
  String? pathFoto;
  String? status;
  DateTime? tanggalrequest;
  DateTime? tanggalapproval;
  bool? approved;
  int? idPegawaiapproval;
  String? pegawaiapprovalnama;
  int? idPegawairiwayatpendidikan;

  RiwayatpendidikanModel();

  static RiwayatpendidikanModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static RiwayatpendidikanModel fromDynamic(dynamic dynamicData) {
    final model = RiwayatpendidikanModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.pegawainama = dynamicData['pegawaiNama'];
    model.idPendidikan = MahasFormat.dynamicToInt(dynamicData['id_Pendidikan']);
    model.namapendidikan = dynamicData['namaPendidikan'];
    model.idKlasifikasipendidikan =
        MahasFormat.dynamicToInt(dynamicData['id_KlasifikasiPendidikan']);
    model.namaklasifikasipendidikan = dynamicData['namaKlasifikasiPendidikan'];
    model.daritahun = MahasFormat.dynamicToInt(dynamicData['dariTahun']);
    model.sampaitahun = MahasFormat.dynamicToInt(dynamicData['sampaiTahun']);
    model.keterangan = dynamicData['keterangan'];
    model.berkaspendukung = dynamicData['berkasPendukung'];
    model.programstudi = dynamicData['programStudi'];
    model.nomorijazah = dynamicData['nomorIjazah'];
    model.alasantolak = dynamicData['alasanTolak'];
    model.pathFoto = dynamicData['pathFoto'];
    model.status = dynamicData['status'];
    model.tanggalrequest =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalRequest']);
    model.tanggalapproval =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalApproval']);
    model.approved = MahasFormat.dynamicToBool(dynamicData['approved']);
    model.idPegawaiapproval =
        MahasFormat.dynamicToInt(dynamicData['id_PegawaiApproval']);
    model.pegawaiapprovalnama = dynamicData['pegawaiApprovalNama'];
    model.idPegawairiwayatpendidikan =
        MahasFormat.dynamicToInt(dynamicData['id_PegawaiRiwayatPendidikan']);

    return model;
  }
}
