import 'package:get/get.dart';

import '../modules/absen_tugas/bindings/absen_surat_tugas_binding.dart';
import '../modules/absen_tugas/views/absen_surat_tugas_view.dart';
import '../modules/absen_tugas_setup/bindings/absen_surat_tugas_setup_binding.dart';
import '../modules/absen_tugas_setup/views/absen_surat_tugas_setup_view.dart';
import '../modules/absensi_detail/bindings/absensi_detail_binding.dart';
import '../modules/absensi_detail/views/absensi_detail_view.dart';
import '../modules/approval/bindings/approval_binding.dart';
import '../modules/approval/views/approval_view.dart';
import '../modules/data_absen/bindings/data_absen_binding.dart';
import '../modules/data_absen/views/data_absen_view.dart';
import '../modules/data_absensi_list/bindings/data_absensi_list_binding.dart';
import '../modules/data_absensi_list/views/data_absensi_list_view.dart';
import '../modules/eticket/bindings/eticket_binding.dart';
import '../modules/eticket/views/eticket_view.dart';
import '../modules/eticket_list/bindings/eticket_list_binding.dart';
import '../modules/eticket_list/views/eticket_list_view.dart';
import '../modules/gaji/bindings/gaji_binding.dart';
import '../modules/gaji/views/gaji_view.dart';
import '../modules/gaji_setup/bindings/gaji_setup_binding.dart';
import '../modules/gaji_setup/views/gaji_setup_view.dart';
import '../modules/home/<USER>/home_binding.dart';
import '../modules/home/<USER>/home_view.dart';
import '../modules/informasi/bindings/informasi_binding.dart';
import '../modules/informasi/views/informasi_view.dart';
import '../modules/informasi_absensi_jadwal_anggota_divisi/bindings/informasi_absensi_jadwal_anggota_divisi_binding.dart';
import '../modules/informasi_absensi_jadwal_anggota_divisi/views/informasi_absensi_jadwal_anggota_divisi_view.dart';
import '../modules/informasi_absensi_sesuai_divisi/bindings/informasi_absensi_sesuai_divisi_binding.dart';
import '../modules/informasi_absensi_sesuai_divisi/views/informasi_absensi_sesuai_divisi_view.dart';
import '../modules/informasi_lembur_ganti_uang/bindings/informasi_lembur_ganti_uang_binding.dart';
import '../modules/informasi_lembur_ganti_uang/views/informasi_lembur_ganti_uang_view.dart';
import '../modules/informasi_pengumuman/bindings/informasi_pengumuman_binding.dart';
import '../modules/informasi_pengumuman/views/informasi_pengumuman_view.dart';
import '../modules/informasi_pengumuman_setup/bindings/informasi_pengumuman_setup_binding.dart';
import '../modules/informasi_pengumuman_setup/views/informasi_pengumuman_setup_view.dart';
import '../modules/informasi_peringatan/bindings/informasi_peringatan_binding.dart';
import '../modules/informasi_peringatan/views/informasi_peringatan_view.dart';
import '../modules/informasi_peringatan_setup/bindings/informasi_peringatan_setup_binding.dart';
import '../modules/informasi_peringatan_setup/views/informasi_peringatan_setup_view.dart';
import '../modules/informasi_perpus/bindings/informasi_perpus_binding.dart';
import '../modules/informasi_perpus/views/informasi_perpus_view.dart';
import '../modules/informasi_perpus_setup/bindings/informasi_perpus_setup_binding.dart';
import '../modules/informasi_perpus_setup/views/informasi_perpus_setup_view.dart';
import '../modules/informasi_quota_cuti_tahunan/bindings/informasi_quota_cuti_tahunan_binding.dart';
import '../modules/informasi_quota_cuti_tahunan/views/informasi_quota_cuti_tahunan_view.dart';
import '../modules/informasi_quota_izin/bindings/informasi_quota_izin_binding.dart';
import '../modules/informasi_quota_izin/views/informasi_quota_izin_view.dart';
import '../modules/informasi_quota_tukar_lembur/bindings/informasi_quota_tukar_lembur_binding.dart';
import '../modules/informasi_quota_tukar_lembur/views/informasi_quota_tukar_lembur_view.dart';
import '../modules/informasi_serah_terima/bindings/informasi_serah_terima_binding.dart';
import '../modules/informasi_serah_terima/views/informasi_serah_terima_view.dart';
import '../modules/informasi_serah_terima_mutasi_barang/bindings/informasi_serah_terima_mutasi_barang_binding.dart';
import '../modules/informasi_serah_terima_mutasi_barang/views/informasi_serah_terima_mutasi_barang_view.dart';
import '../modules/informasi_serah_terima_mutasi_barang_setup/bindings/informasi_serah_terima_mutasi_barang_setup_binding.dart';
import '../modules/informasi_serah_terima_mutasi_barang_setup/views/informasi_serah_terima_mutasi_barang_setup_view.dart';
import '../modules/jadwal/bindings/jadwal_binding.dart';
import '../modules/jadwal/views/jadwal_view.dart';
import '../modules/login/bindings/login_binding.dart';
import '../modules/login/views/login_view.dart';
import '../modules/login_password/bindings/login_password_binding.dart';
import '../modules/login_password/views/login_password_view.dart';
import '../modules/notifikasi/bindings/notifikasi_binding.dart';
import '../modules/notifikasi/views/notifikasi_view.dart';
import '../modules/notifikasi_detail/bindings/notifikasi_detail_binding.dart';
import '../modules/notifikasi_detail/views/notifikasi_detail_view.dart';
import '../modules/permintaan_jadwal/bindings/permintaan_jadwal_binding.dart';
import '../modules/permintaan_jadwal/views/permintaan_jadwal_view.dart';
import '../modules/permintaan_jadwal_cuti_day_payment/bindings/permintaan_jadwal_cuti_day_payment_binding.dart';
import '../modules/permintaan_jadwal_cuti_day_payment/views/permintaan_jadwal_cuti_day_payment_view.dart';
import '../modules/permintaan_jadwal_cuti_day_payment_setup/bindings/permintaan_jadwal_cuti_day_payment_setup_binding.dart';
import '../modules/permintaan_jadwal_cuti_day_payment_setup/views/permintaan_jadwal_cuti_day_payment_setup_view.dart';
import '../modules/permintaan_jadwal_cuti_hamil/bindings/permintaan_jadwal_cuti_hamil_binding.dart';
import '../modules/permintaan_jadwal_cuti_hamil/views/permintaan_jadwal_cuti_hamil_view.dart';
import '../modules/permintaan_jadwal_cuti_hamil_setup/bindings/permintaan_jadwal_cuti_hamil_setup_binding.dart';
import '../modules/permintaan_jadwal_cuti_hamil_setup/views/permintaan_jadwal_cuti_hamil_setup_view.dart';
import '../modules/permintaan_jadwal_cuti_sakit/bindings/permintaan_jadwal_cuti_sakit_binding.dart';
import '../modules/permintaan_jadwal_cuti_sakit/views/permintaan_jadwal_cuti_sakit_view.dart';
import '../modules/permintaan_jadwal_cuti_sakit_setup/bindings/permintaan_jadwal_cuti_sakit_setup_binding.dart';
import '../modules/permintaan_jadwal_cuti_sakit_setup/views/permintaan_jadwal_cuti_sakit_setup_view.dart';
import '../modules/permintaan_jadwal_cuti_setengah_hari/bindings/permintaan_jadwal_cuti_setengah_hari_binding.dart';
import '../modules/permintaan_jadwal_cuti_setengah_hari/views/permintaan_jadwal_cuti_setengah_hari_view.dart';
import '../modules/permintaan_jadwal_cuti_setengah_hari_setup/bindings/permintaan_jadwal_cuti_setengah_hari_setup_binding.dart';
import '../modules/permintaan_jadwal_cuti_setengah_hari_setup/views/permintaan_jadwal_cuti_setengah_hari_setup_view.dart';
import '../modules/permintaan_jadwal_cuti_tahunan/bindings/permintaan_jadwal_cuti_tahunan_binding.dart';
import '../modules/permintaan_jadwal_cuti_tahunan/views/permintaan_jadwal_cuti_tahunan_view.dart';
import '../modules/permintaan_jadwal_cuti_tahunan_setup/bindings/permintaan_jadwal_cuti_tahunan_setup_binding.dart';
import '../modules/permintaan_jadwal_cuti_tahunan_setup/views/permintaan_jadwal_cuti_tahunan_setup_view.dart';
import '../modules/permintaan_jadwal_cuti_tukar_lembur/bindings/permintaan_jadwal_cuti_tukar_lembur_binding.dart';
import '../modules/permintaan_jadwal_cuti_tukar_lembur/views/permintaan_jadwal_cuti_tukar_lembur_view.dart';
import '../modules/permintaan_jadwal_cuti_tukar_lembur_setup/bindings/permintaan_jadwal_cuti_tukar_lembur_setup_binding.dart';
import '../modules/permintaan_jadwal_cuti_tukar_lembur_setup/views/permintaan_jadwal_cuti_tukar_lembur_setup_view.dart';
import '../modules/permintaan_jadwal_cuti_unpaid_leave/bindings/permintaan_jadwal_cuti_unpaid_leave_binding.dart';
import '../modules/permintaan_jadwal_cuti_unpaid_leave/views/permintaan_jadwal_cuti_unpaid_leave_view.dart';
import '../modules/permintaan_jadwal_cuti_unpaid_leave_setup/bindings/permintaan_jadwal_cuti_unpaid_leave_setup_binding.dart';
import '../modules/permintaan_jadwal_cuti_unpaid_leave_setup/views/permintaan_jadwal_cuti_unpaid_leave_setup_view.dart';
import '../modules/permintaan_jadwal_izin/bindings/permintaan_jadwal_izin_binding.dart';
import '../modules/permintaan_jadwal_izin/views/permintaan_jadwal_izin_view.dart';
import '../modules/permintaan_jadwal_izin_setup/bindings/permintaan_jadwal_izin_setup_binding.dart';
import '../modules/permintaan_jadwal_izin_setup/views/permintaan_jadwal_izin_setup_view.dart';
import '../modules/permintaan_jadwal_lembur/bindings/permintaan_jadwal_lembur_binding.dart';
import '../modules/permintaan_jadwal_lembur/views/permintaan_jadwal_lembur_view.dart';
import '../modules/permintaan_jadwal_lembur_custom_kepala_unit_setup/bindings/permintaan_jadwal_lembur_custom_kepala_unit_setup_binding.dart';
import '../modules/permintaan_jadwal_lembur_custom_kepala_unit_setup/views/permintaan_jadwal_lembur_custom_kepala_unit_setup_view.dart';
import '../modules/permintaan_jadwal_lembur_custom_pegawai_setup/bindings/permintaan_jadwal_lembur_custom_pegawai_setup_binding.dart';
import '../modules/permintaan_jadwal_lembur_custom_pegawai_setup/views/permintaan_jadwal_lembur_custom_pegawai_setup_view.dart';
import '../modules/permintaan_jadwal_lembur_setup/bindings/permintaan_jadwal_lembur_setup_binding.dart';
import '../modules/permintaan_jadwal_lembur_setup/views/permintaan_jadwal_lembur_setup_view.dart';
import '../modules/permintaan_jadwal_tukar_jadwal/bindings/permintaan_jadwal_tukar_jadwal_binding.dart';
import '../modules/permintaan_jadwal_tukar_jadwal/views/permintaan_jadwal_tukar_jadwal_view.dart';
import '../modules/permintaan_jadwal_tukar_jadwal_setup/bindings/permintaan_jadwal_tukar_jadwal_setup_binding.dart';
import '../modules/permintaan_jadwal_tukar_jadwal_setup/views/permintaan_jadwal_tukar_jadwal_setup_view.dart';
import '../modules/profile/bindings/profile_binding.dart';
import '../modules/profile/views/profile_view.dart';
import '../modules/profile_data_pribadi/bindings/profile_data_pribadi_binding.dart';
import '../modules/profile_data_pribadi/views/profile_data_pribadi_view.dart';
import '../modules/profile_data_pribadi_history/bindings/profile_data_pribadi_history_binding.dart';
import '../modules/profile_data_pribadi_history/views/profile_data_pribadi_history_view.dart';
import '../modules/profile_divisi/bindings/profile_divisi_binding.dart';
import '../modules/profile_divisi/views/profile_divisi_view.dart';
import '../modules/profile_dokumen/bindings/profile_dokumen_binding.dart';
import '../modules/profile_dokumen/views/profile_dokumen_view.dart';
import '../modules/profile_dokumen_setup/bindings/profile_dokumen_setup_binding.dart';
import '../modules/profile_dokumen_setup/views/profile_dokumen_setup_view.dart';
import '../modules/profile_foto_history/bindings/profile_foto_history_binding.dart';
import '../modules/profile_foto_history/views/profile_foto_history_view.dart';
import '../modules/profile_foto_history_setup/bindings/profile_foto_history_setup_binding.dart';
import '../modules/profile_foto_history_setup/views/profile_foto_history_setup_view.dart';
import '../modules/profile_hobi/bindings/profile_hobi_binding.dart';
import '../modules/profile_hobi/views/profile_hobi_view.dart';
import '../modules/profile_hobi_setup/bindings/profile_hobi_setup_binding.dart';
import '../modules/profile_hobi_setup/views/profile_hobi_setup_view.dart';
import '../modules/profile_jenis_sertifikasi/bindings/profile_jenis_sertifikasi_binding.dart';
import '../modules/profile_jenis_sertifikasi/views/profile_jenis_sertifikasi_view.dart';
import '../modules/profile_jenis_sertifikasi_history/bindings/profile_jenis_sertifikasi_history_binding.dart';
import '../modules/profile_jenis_sertifikasi_history/views/profile_jenis_sertifikasi_history_view.dart';
import '../modules/profile_jenis_sertifikasi_history_setup/bindings/profile_jenis_sertifikasi_history_setup_binding.dart';
import '../modules/profile_jenis_sertifikasi_history_setup/views/profile_jenis_sertifikasi_history_setup_view.dart';
import '../modules/profile_jenis_sertifikasi_setup/bindings/profile_jenis_sertifikasi_setup_binding.dart';
import '../modules/profile_jenis_sertifikasi_setup/views/profile_jenis_sertifikasi_setup_view.dart';
import '../modules/profile_kategoribios/bindings/profile_kategoribios_binding.dart';
import '../modules/profile_kategoribios/views/profile_kategoribios_view.dart';
import '../modules/profile_keluarga/bindings/profile_keluarga_binding.dart';
import '../modules/profile_keluarga/views/profile_keluarga_view.dart';
import '../modules/profile_keluarga_history/bindings/profile_keluarga_history_binding.dart';
import '../modules/profile_keluarga_history/views/profile_keluarga_history_view.dart';
import '../modules/profile_keluarga_history_setup/bindings/profile_keluarga_history_setup_binding.dart';
import '../modules/profile_keluarga_history_setup/views/profile_keluarga_history_setup_view.dart';
import '../modules/profile_keluarga_setup/bindings/profile_keluarga_setup_binding.dart';
import '../modules/profile_keluarga_setup/views/profile_keluarga_setup_view.dart';
import '../modules/profile_kontak_pegawai/bindings/profile_kontak_pegawai_binding.dart';
import '../modules/profile_kontak_pegawai/views/profile_kontak_pegawai_view.dart';
import '../modules/profile_kontak_pegawai_history/bindings/profile_kontak_pegawai_history_binding.dart';
import '../modules/profile_kontak_pegawai_history/views/profile_kontak_pegawai_history_view.dart';
import '../modules/profile_kontak_pegawai_history_setup/bindings/profile_kontak_pegawai_history_setup_binding.dart';
import '../modules/profile_kontak_pegawai_history_setup/views/profile_kontak_pegawai_history_setup_view.dart';
import '../modules/profile_kontak_pegawai_setup/bindings/profile_kontak_pegawai_setup_binding.dart';
import '../modules/profile_kontak_pegawai_setup/views/profile_kontak_pegawai_setup_view.dart';
import '../modules/profile_kontrak/bindings/profile_kontrak_binding.dart';
import '../modules/profile_kontrak/views/profile_kontrak_view.dart';
import '../modules/profile_kontrak_setup/bindings/profile_kontrak_setup_binding.dart';
import '../modules/profile_kontrak_setup/views/profile_kontrak_setup_view.dart';
import '../modules/profile_pegawai_kgb/bindings/profile_pegawai_kgb_binding.dart';
import '../modules/profile_pegawai_kgb/views/profile_pegawai_kgb_view.dart';
import '../modules/profile_pegawai_kgb_setup/bindings/profile_pegawai_kgb_setup_binding.dart';
import '../modules/profile_pegawai_kgb_setup/views/profile_pegawai_kgb_setup_view.dart';
import '../modules/profile_pegawai_pangkat/bindings/profile_pegawai_pangkat_binding.dart';
import '../modules/profile_pegawai_pangkat/views/profile_pegawai_pangkat_view.dart';
import '../modules/profile_pegawai_pangkat_setup/bindings/profile_pegawai_pangkat_setup_binding.dart';
import '../modules/profile_pegawai_pangkat_setup/views/profile_pegawai_pangkat_setup_view.dart';
import '../modules/profile_pegawai_str/bindings/profile_pegawai_str_binding.dart';
import '../modules/profile_pegawai_str/views/profile_pegawai_str_view.dart';
import '../modules/profile_pegawai_str_history/bindings/profile_pegawai_str_history_binding.dart';
import '../modules/profile_pegawai_str_history/views/profile_pegawai_str_history_view.dart';
import '../modules/profile_pegawai_str_history_setup/bindings/profile_pegawai_str_history_setup_binding.dart';
import '../modules/profile_pegawai_str_history_setup/views/profile_pegawai_str_history_setup_view.dart';
import '../modules/profile_pegawai_str_setup/bindings/profile_pegawai_str_setup_binding.dart';
import '../modules/profile_pegawai_str_setup/views/profile_pegawai_str_setup_view.dart';
import '../modules/profile_pengalaman/bindings/profile_pengalaman_binding.dart';
import '../modules/profile_pengalaman/views/profile_pengalaman_view.dart';
import '../modules/profile_pengalaman_setup/bindings/profile_pengalaman_setup_binding.dart';
import '../modules/profile_pengalaman_setup/views/profile_pengalaman_setup_view.dart';
import '../modules/profile_rekening/bindings/profile_rekening_binding.dart';
import '../modules/profile_rekening/views/profile_rekening_view.dart';
import '../modules/profile_rekening_history/bindings/profile_rekening_history_binding.dart';
import '../modules/profile_rekening_history/views/profile_rekening_history_view.dart';
import '../modules/profile_rekening_history_setup/bindings/profile_rekening_history_setup_binding.dart';
import '../modules/profile_rekening_history_setup/views/profile_rekening_history_setup_view.dart';
import '../modules/profile_rekening_setup/bindings/profile_rekening_setup_binding.dart';
import '../modules/profile_rekening_setup/views/profile_rekening_setup_view.dart';
import '../modules/profile_riwayat_kesehatan/bindings/profile_riwayat_kesehatan_binding.dart';
import '../modules/profile_riwayat_kesehatan/views/profile_riwayat_kesehatan_view.dart';
import '../modules/profile_riwayat_kesehatan_history/bindings/profile_riwayat_kesehatan_history_binding.dart';
import '../modules/profile_riwayat_kesehatan_history/views/profile_riwayat_kesehatan_history_view.dart';
import '../modules/profile_riwayat_kesehatan_history_setup/bindings/profile_riwayat_kesehatan_history_setup_binding.dart';
import '../modules/profile_riwayat_kesehatan_history_setup/views/profile_riwayat_kesehatan_history_setup_view.dart';
import '../modules/profile_riwayat_kesehatan_setup/bindings/profile_riwayat_kesehatan_setup_binding.dart';
import '../modules/profile_riwayat_kesehatan_setup/views/profile_riwayat_kesehatan_setup_view.dart';
import '../modules/profile_riwayat_pendidikan/bindings/profile_riwayat_pendidikan_binding.dart';
import '../modules/profile_riwayat_pendidikan/views/profile_riwayat_pendidikan_view.dart';
import '../modules/profile_riwayat_pendidikan_history/bindings/profile_riwayat_pendidikan_history_binding.dart';
import '../modules/profile_riwayat_pendidikan_history/views/profile_riwayat_pendidikan_history_view.dart';
import '../modules/profile_riwayat_pendidikan_history_setup/bindings/profile_riwayat_pendidikan_history_setup_binding.dart';
import '../modules/profile_riwayat_pendidikan_history_setup/views/profile_riwayat_pendidikan_history_setup_view.dart';
import '../modules/profile_riwayat_pendidikan_setup/bindings/profile_riwayat_pendidikan_setup_binding.dart';
import '../modules/profile_riwayat_pendidikan_setup/views/profile_riwayat_pendidikan_setup_view.dart';
import '../modules/profile_seragam/bindings/profile_seragam_binding.dart';
import '../modules/profile_seragam/views/profile_seragam_view.dart';
import '../modules/profile_seragam_setup/bindings/profile_seragam_setup_binding.dart';
import '../modules/profile_seragam_setup/views/profile_seragam_setup_view.dart';
import '../modules/qr_code_scanner/bindings/qr_code_scanner_binding.dart';
import '../modules/qr_code_scanner/views/qr_code_scanner_view.dart';
import '../modules/splash_screen/bindings/splash_screen_binding.dart';
import '../modules/splash_screen/views/splash_screen_view.dart';
import '../modules/unauthorized/bindings/unauthorized_binding.dart';
import '../modules/unauthorized/views/unauthorized_view.dart';

// ignore_for_file: constant_identifier_names

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static const INITIAL = Routes.SPLASH_SCREEN;

  static final routes = [
    GetPage(
      name: _Paths.HOME,
      page: () => const HomeView(),
      binding: HomeBinding(),
    ),
    GetPage(
      name: _Paths.LOGIN,
      page: () => const LoginView(),
      binding: LoginBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE,
      page: () => const ProfileView(),
      binding: ProfileBinding(),
    ),
    GetPage(
      name: _Paths.UNAUTHORIZED,
      page: () => const UnauthorizedView(),
      binding: UnauthorizedBinding(),
    ),
    GetPage(
      name: _Paths.SPLASH_SCREEN,
      page: () => const SplashScreenView(),
      binding: SplashScreenBinding(),
    ),
    GetPage(
      name: _Paths.JADWAL,
      page: () => const JadwalView(),
      binding: JadwalBinding(),
    ),
    GetPage(
      name: _Paths.DATA_ABSEN,
      page: () => const DataAbsenView(),
      binding: DataAbsenBinding(),
    ),
    GetPage(
      name: _Paths.PERMINTAAN_JADWAL,
      page: () => const PermintaanJadwalView(),
      binding: PermintaanJadwalBinding(),
    ),
    GetPage(
      name: _Paths.PERMINTAAN_JADWAL_TUKAR_JADWAL,
      page: () => const PermintaanJadwalTukarJadwalView(),
      binding: PermintaanJadwalTukarJadwalBinding(),
    ),
    GetPage(
      name: _Paths.PERMINTAAN_JADWAL_LEMBUR,
      page: () => const PermintaanJadwalLemburView(),
      binding: PermintaanJadwalLemburBinding(),
    ),
    GetPage(
      name: _Paths.PERMINTAAN_JADWAL_CUTI_HAMIL,
      page: () => const PermintaanJadwalCutiHamilView(),
      binding: PermintaanJadwalCutiHamilBinding(),
    ),
    GetPage(
      name: _Paths.PERMINTAAN_JADWAL_CUTI_SAKIT,
      page: () => const PermintaanJadwalCutiSakitView(),
      binding: PermintaanJadwalCutiSakitBinding(),
    ),
    GetPage(
      name: _Paths.PERMINTAAN_JADWAL_CUTI_TAHUNAN,
      page: () => const PermintaanJadwalCutiTahunanView(),
      binding: PermintaanJadwalCutiTahunanBinding(),
    ),
    GetPage(
      name: _Paths.PERMINTAAN_JADWAL_CUTI_TUKAR_LEMBUR,
      page: () => const PermintaanJadwalCutiTukarLemburView(),
      binding: PermintaanJadwalCutiTukarLemburBinding(),
    ),
    GetPage(
      name: _Paths.PERMINTAAN_JADWAL_IZIN,
      page: () => const PermintaanJadwalIzinView(),
      binding: PermintaanJadwalIzinBinding(),
    ),
    GetPage(
      name: _Paths.INFORMASI,
      page: () => const InformasiView(),
      binding: InformasiBinding(),
    ),
    GetPage(
      name: _Paths.INFORMASI_QUOTA_CUTI_TAHUNAN,
      page: () => const InformasiQuotaCutiTahunanView(),
      binding: InformasiQuotaCutiTahunanBinding(),
    ),
    GetPage(
      name: _Paths.NOTIFIKASI,
      page: () => const NotifikasiView(),
      binding: NotifikasiBinding(),
    ),
    GetPage(
      name: _Paths.NOTIFIKASI_DETAIL,
      page: () => const NotifikasiDetailView(),
      binding: NotifikasiDetailBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_DATA_PRIBADI,
      page: () => const ProfileDataPribadiView(),
      binding: ProfileDataPribadiBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_DIVISI,
      page: () => const ProfileDivisiView(),
      binding: ProfileDivisiBinding(),
    ),
    GetPage(
      name: _Paths.APPROVAL,
      page: () => const ApprovalView(),
      binding: ApprovalBinding(),
    ),
    GetPage(
      name: _Paths.PERMINTAAN_JADWAL_CUTI_HAMIL_SETUP,
      page: () => const PermintaanJadwalCutiHamilSetupView(),
      binding: PermintaanJadwalCutiHamilSetupBinding(),
    ),
    GetPage(
      name: _Paths.PERMINTAAN_JADWAL_IZIN_SETUP,
      page: () => const PermintaanJadwalIzinSetupView(),
      binding: PermintaanJadwalIzinSetupBinding(),
    ),
    GetPage(
      name: _Paths.PERMINTAAN_JADWAL_CUTI_SAKIT_SETUP,
      page: () => const PermintaanJadwalCutiSakitSetupView(),
      binding: PermintaanJadwalCutiSakitSetupBinding(),
    ),
    GetPage(
      name: _Paths.PERMINTAAN_JADWAL_CUTI_TUKAR_LEMBUR_SETUP,
      page: () => const PermintaanJadwalCutiTukarLemburSetupView(),
      binding: PermintaanJadwalCutiTukarLemburSetupBinding(),
    ),
    GetPage(
      name: _Paths.PERMINTAAN_JADWAL_LEMBUR_SETUP,
      page: () => const PermintaanJadwalLemburSetupView(),
      binding: PermintaanJadwalLemburSetupBinding(),
    ),
    GetPage(
      name: _Paths.PERMINTAAN_JADWAL_CUTI_TAHUNAN_SETUP,
      page: () => const PermintaanJadwalCutiTahunanSetupView(),
      binding: PermintaanJadwalCutiTahunanSetupBinding(),
    ),
    GetPage(
      name: _Paths.PERMINTAAN_JADWAL_TUKAR_JADWAL_SETUP,
      page: () => const PermintaanJadwalTukarJadwalSetupView(),
      binding: PermintaanJadwalTukarJadwalSetupBinding(),
    ),
    GetPage(
      name: _Paths.INFORMASI_QUOTA_TUKAR_LEMBUR,
      page: () => const InformasiQuotaTukarLemburView(),
      binding: InformasiQuotaTukarLemburBinding(),
    ),
    GetPage(
      name: _Paths.INFORMASI_ABSENSI_SESUAI_DIVISI,
      page: () => const InformasiAbsensiSesuaiDivisiView(),
      binding: InformasiAbsensiSesuaiDivisiBinding(),
    ),
    GetPage(
      name: _Paths.INFORMASI_LEMBUR_GANTI_UANG,
      page: () => const InformasiLemburGantiUangView(),
      binding: InformasiLemburGantiUangBinding(),
    ),
    GetPage(
      name: _Paths.ABSEN_SURAT_TUGAS,
      page: () => const AbsenSuratTugasView(),
      binding: AbsenSuratTugasBinding(),
    ),
    GetPage(
      name: _Paths.ABSEN_SURAT_TUGAS_SETUP,
      page: () => const AbsenSuratTugasSetupView(),
      binding: AbsenSuratTugasSetupBinding(),
    ),
    GetPage(
      name: _Paths.DATA_ABSENSI_LIST,
      page: () => const DataAbsensiListView(),
      binding: DataAbsensiListBinding(),
    ),
    GetPage(
      name: _Paths.ETICKET,
      page: () => const EticketView(),
      binding: EticketBinding(),
    ),
    GetPage(
      name: _Paths.ETICKET_LIST,
      page: () => const EticketListView(),
      binding: EticketListBinding(),
    ),
    GetPage(
      name: _Paths.GAJI,
      page: () => const GajiView(),
      binding: GajiBinding(),
    ),
    GetPage(
      name: _Paths.GAJI_SETUP,
      page: () => const GajiSetupView(),
      binding: GajiSetupBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_JENIS_SERTIFIKASI,
      page: () => const ProfileJenisSertifikasiView(),
      binding: ProfileJenisSertifikasiBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_KELUARGA,
      page: () => const ProfileKeluargaView(),
      binding: ProfileKeluargaBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_RIWAYAT_KESEHATAN,
      page: () => const ProfileRiwayatKesehatanView(),
      binding: ProfileRiwayatKesehatanBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_HOBI,
      page: () => const ProfileHobiView(),
      binding: ProfileHobiBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_PENGALAMAN,
      page: () => const ProfilePengalamanView(),
      binding: ProfilePengalamanBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_REKENING,
      page: () => const ProfileRekeningView(),
      binding: ProfileRekeningBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_RIWAYAT_PENDIDIKAN,
      page: () => const ProfileRiwayatPendidikanView(),
      binding: ProfileRiwayatPendidikanBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_JENIS_SERTIFIKASI_SETUP,
      page: () => const ProfileJenisSertifikasiSetupView(),
      binding: ProfileJenisSertifikasiSetupBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_KELUARGA_SETUP,
      page: () => const ProfileKeluargaSetupView(),
      binding: ProfileKeluargaSetupBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_RIWAYAT_KESEHATAN_SETUP,
      page: () => const ProfileRiwayatKesehatanSetupView(),
      binding: ProfileRiwayatKesehatanSetupBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_PENGALAMAN_SETUP,
      page: () => const ProfilePengalamanSetupView(),
      binding: ProfilePengalamanSetupBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_HOBI_SETUP,
      page: () => const ProfileHobiSetupView(),
      binding: ProfileHobiSetupBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_KONTAK_PEGAWAI,
      page: () => const ProfileKontakPegawaiView(),
      binding: ProfileKontakPegawaiBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_KONTAK_PEGAWAI_SETUP,
      page: () => const ProfileKontakPegawaiSetupView(),
      binding: ProfileKontakPegawaiSetupBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_RIWAYAT_PENDIDIKAN_SETUP,
      page: () => const ProfileRiwayatPendidikanSetupView(),
      binding: ProfileRiwayatPendidikanSetupBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_PENGALAMAN_SETUP,
      page: () => const ProfilePengalamanSetupView(),
      binding: ProfilePengalamanSetupBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_REKENING_SETUP,
      page: () => const ProfileRekeningSetupView(),
      binding: ProfileRekeningSetupBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_SERAGAM,
      page: () => const ProfileSeragamView(),
      binding: ProfileSeragamBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_SERAGAM_SETUP,
      page: () => const ProfileSeragamSetupView(),
      binding: ProfileSeragamSetupBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_DOKUMEN,
      page: () => const ProfileDokumenView(),
      binding: ProfileDokumenBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_DOKUMEN_SETUP,
      page: () => const ProfileDokumenSetupView(),
      binding: ProfileDokumenSetupBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_DATA_PRIBADI_HISTORY,
      page: () => const ProfileDataPribadiHistoryView(),
      binding: ProfileDataPribadiHistoryBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_KONTRAK,
      page: () => const ProfileKontrakView(),
      binding: ProfileKontrakBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_KONTRAK_SETUP,
      page: () => const ProfileKontrakSetupView(),
      binding: ProfileKontrakSetupBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_KONTAK_PEGAWAI_HISTORY,
      page: () => const ProfileKontakPegawaiHistoryView(),
      binding: ProfileKontakPegawaiHistoryBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_KONTAK_PEGAWAI_HISTORY_SETUP,
      page: () => const ProfileKontakPegawaiHistorySetupView(),
      binding: ProfileKontakPegawaiHistorySetupBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_KELUARGA_HISTORY,
      page: () => const ProfileKeluargaHistoryView(),
      binding: ProfileKeluargaHistoryBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_KELUARGA_HISTORY_SETUP,
      page: () => const ProfileKeluargaHistorySetupView(),
      binding: ProfileKeluargaHistorySetupBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_RIWAYAT_KESEHATAN_HISTORY,
      page: () => const ProfileRiwayatKesehatanHistoryView(),
      binding: ProfileRiwayatKesehatanHistoryBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_RIWAYAT_KESEHATAN_HISTORY_SETUP,
      page: () => const ProfileRiwayatKesehatanHistorySetupView(),
      binding: ProfileRiwayatKesehatanHistorySetupBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_PEGAWAI_STR,
      page: () => const ProfilePegawaiStrView(),
      binding: ProfilePegawaiStrBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_PEGAWAI_STR_SETUP,
      page: () => const ProfilePegawaiStrSetupView(),
      binding: ProfilePegawaiStrSetupBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_PEGAWAI_STR_HISTORY,
      page: () => const ProfilePegawaiStrHistoryView(),
      binding: ProfilePegawaiStrHistoryBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_PEGAWAI_STR_HISTORY_SETUP,
      page: () => const ProfilePegawaiStrHistorySetupView(),
      binding: ProfilePegawaiStrHistorySetupBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_REKENING_HISTORY,
      page: () => const ProfileRekeningHistoryView(),
      binding: ProfileRekeningHistoryBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_REKENING_HISTORY_SETUP,
      page: () => const ProfileRekeningHistorySetupView(),
      binding: ProfileRekeningHistorySetupBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_JENIS_SERTIFIKASI_HISTORY,
      page: () => const ProfileJenisSertifikasiHistoryView(),
      binding: ProfileJenisSertifikasiHistoryBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_JENIS_SERTIFIKASI_HISTORY_SETUP,
      page: () => const ProfileJenisSertifikasiHistorySetupView(),
      binding: ProfileJenisSertifikasiHistorySetupBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_FOTO_HISTORY,
      page: () => const ProfileFotoHistoryView(),
      binding: ProfileFotoHistoryBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_FOTO_HISTORY_SETUP,
      page: () => const ProfileFotoHistorySetupView(),
      binding: ProfileFotoHistorySetupBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_RIWAYAT_PENDIDIKAN_HISTORY,
      page: () => const ProfileRiwayatPendidikanHistoryView(),
      binding: ProfileRiwayatPendidikanHistoryBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_RIWAYAT_PENDIDIKAN_HISTORY_SETUP,
      page: () => const ProfileRiwayatPendidikanHistorySetupView(),
      binding: ProfileRiwayatPendidikanHistorySetupBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_PEGAWAI_KGB,
      page: () => const ProfilePegawaiKgbView(),
      binding: ProfilePegawaiKgbBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_PEGAWAI_KGB_SETUP,
      page: () => const ProfilePegawaiKgbSetupView(),
      binding: ProfilePegawaiKgbSetupBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_PEGAWAI_PANGKAT,
      page: () => const ProfilePegawaiPangkatView(),
      binding: ProfilePegawaiPangkatBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_PEGAWAI_PANGKAT_SETUP,
      page: () => const ProfilePegawaiPangkatSetupView(),
      binding: ProfilePegawaiPangkatSetupBinding(),
    ),
    GetPage(
      name: _Paths.ABSENSI_DETAIL,
      page: () => const AbsensiDetailView(),
      binding: AbsensiDetailBinding(),
    ),
    GetPage(
      name: _Paths.INFORMASI_PERPUS,
      page: () => const InformasiPerpusView(),
      binding: InformasiPerpusBinding(),
    ),
    GetPage(
      name: _Paths.INFORMASI_PERPUS_SETUP,
      page: () => const InformasiPerpusSetupView(),
      binding: InformasiPerpusSetupBinding(),
    ),
    GetPage(
      name: _Paths.INFORMASI_SERAH_TERIMA,
      page: () => const InformasiSerahTerimaView(),
      binding: InformasiSerahTerimaBinding(),
    ),
    GetPage(
      name: _Paths.INFORMASI_SERAH_TERIMA_MUTASI_BARANG,
      page: () => const InformasiSerahTerimaMutasiBarangView(),
      binding: InformasiSerahTerimaMutasiBarangBinding(),
    ),
    GetPage(
      name: _Paths.INFORMASI_SERAH_TERIMA_MUTASI_BARANG_SETUP,
      page: () => const InformasiSerahTerimaMutasiBarangSetupView(),
      binding: InformasiSerahTerimaMutasiBarangSetupBinding(),
    ),
    GetPage(
      name: _Paths.QR_CODE_SCANNER,
      page: () => const QrCodeScannerView(),
      binding: QrCodeScannerBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_KATEGORIBIOS,
      page: () => const ProfileKategoribiosView(),
      binding: ProfileKategoribiosBinding(),
    ),
    GetPage(
      name: _Paths.PERMINTAAN_JADWAL_LEMBUR_CUSTOM_KEPALA_UNIT_SETUP,
      page: () => const PermintaanJadwalLemburCustomKepalaUnitSetupView(),
      binding: PermintaanJadwalLemburCustomKepalaUnitSetupBinding(),
    ),
    GetPage(
      name: _Paths.PERMINTAAN_JADWAL_LEMBUR_CUSTOM_PEGAWAI_SETUP,
      page: () => const PermintaanJadwalLemburCustomPegawaiSetupView(),
      binding: PermintaanJadwalLemburCustomPegawaiSetupBinding(),
    ),
    GetPage(
      name: _Paths.INFORMASI_QUOTA_IZIN,
      page: () => const InformasiQuotaIzinView(),
      binding: InformasiQuotaIzinBinding(),
    ),
    GetPage(
      name: _Paths.LOGIN_PASSWORD,
      page: () => const LoginPasswordView(),
      binding: LoginPasswordBinding(),
    ),
    GetPage(
      name: _Paths.PERMINTAAN_JADWAL_CUTI_DAY_PAYMENT,
      page: () => const PermintaanJadwalCutiDayPaymentView(),
      binding: PermintaanJadwalCutiDayPaymentBinding(),
    ),
    GetPage(
      name: _Paths.PERMINTAAN_JADWAL_CUTI_DAY_PAYMENT_SETUP,
      page: () => const PermintaanJadwalCutiDayPaymentSetupView(),
      binding: PermintaanJadwalCutiDayPaymentSetupBinding(),
    ),
    GetPage(
      name: _Paths.PERMINTAAN_JADWAL_CUTI_UNPAID_LEAVE,
      page: () => const PermintaanJadwalCutiUnpaidLeaveView(),
      binding: PermintaanJadwalCutiUnpaidLeaveBinding(),
    ),
    GetPage(
      name: _Paths.PERMINTAAN_JADWAL_CUTI_UNPAID_LEAVE_SETUP,
      page: () => const PermintaanJadwalCutiUnpaidLeaveSetupView(),
      binding: PermintaanJadwalCutiUnpaidLeaveSetupBinding(),
    ),
    GetPage(
      name: _Paths.PERMINTAAN_JADWAL_CUTI_SETENGAH_HARI,
      page: () => const PermintaanJadwalCutiSetengahHariView(),
      binding: PermintaanJadwalCutiSetengahHariBinding(),
    ),
    GetPage(
      name: _Paths.PERMINTAAN_JADWAL_CUTI_SETENGAH_HARI_SETUP,
      page: () => const PermintaanJadwalCutiSetengahHariSetupView(),
      binding: PermintaanJadwalCutiSetengahHariSetupBinding(),
    ),
    GetPage(
      name: _Paths.INFORMASI_ABSENSI_JADWAL_ANGGOTA_DIVISI,
      page: () => const InformasiAbsensiJadwalAnggotaDivisiView(),
      binding: InformasiAbsensiJadwalAnggotaDivisiBinding(),
    ),
    GetPage(
      name: _Paths.INFORMASI_PENGUMUMAN,
      page: () => const InformasiPengumumanView(),
      binding: InformasiPengumumanBinding(),
    ),
    GetPage(
      name: _Paths.INFORMASI_PENGUMUMAN_SETUP,
      page: () => const InformasiPengumumanSetupView(),
      binding: InformasiPengumumanSetupBinding(),
    ),
    GetPage(
      name: _Paths.INFORMASI_PERINGATAN,
      page: () => const InformasiPeringatanView(),
      binding: InformasiPeringatanBinding(),
    ),
    GetPage(
      name: _Paths.INFORMASI_PERINGATAN_SETUP,
      page: () => const InformasiPeringatanSetupView(),
      binding: InformasiPeringatanSetupBinding(),
    ),
  ];
}
