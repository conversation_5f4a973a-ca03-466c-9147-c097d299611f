import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../mahas/components/mahas_themes.dart';
import '../mahas/mahas_colors.dart';
import '../mahas_complement/mahas_colors.dart' as mahas_colors;

class ApprovalStatusWidget extends StatelessWidget {
  final String pegawai;
  final dynamic approveStatus;

  const ApprovalStatusWidget({
    super.key,
    required this.pegawai,
    required this.approveStatus,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          pegawai,
          style: MahasThemes.title,
        ),
        _buildApprovalStatus(),
      ],
    );
  }

  Widget _buildApprovalStatus() {
    if (approveStatus.toString() == "null") {
      return const Row(
        children: [
          Icon(
            FontAwesomeIcons.clock,
            size: 12,
          ),
          Text(" Menunggu"),
        ],
      );
    } else if (approveStatus == false) {
      return const Row(
        children: [
          Icon(
            FontAwesomeIcons.circleXmark,
            size: 12,
            color: MahasColors.red,
          ),
          Text(
            " Ditolak",
            style: TextStyle(color: MahasColors.red),
          ),
        ],
      );
    } else {
      return const Row(
        children: [
          Icon(
            FontAwesomeIcons.check,
            size: 12,
            color: mahas_colors.MahasColor.colorGreen,
          ),
          Text(
            " Diterima",
            style: TextStyle(
              color: mahas_colors.MahasColor.colorGreen,
            ),
          ),
        ],
      );
    }
  }
}
