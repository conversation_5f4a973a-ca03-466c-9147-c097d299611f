import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';
import 'package:hr_portal/app/mahas_complement/mahas_colors.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/models/api_list_resut_model.dart';
import '../../../mahas/models/menu_item_model.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../models/informasi_absensi_sesuai_divisi.dart';
import '../../../models/profile_divisi_model.dart';

class InformasiAbsensiJadwalAnggotaDivisiController extends GetxController {
  InformasiAbsensiKeluarMasukModel listCon = InformasiAbsensiKeluarMasukModel();
  List<InformasiabsensiModel> masukList = [];
  List<InformasiabsensiModel> keluarList = [];
  RxString tanggal =
      "${DateTime.now().year}-${DateTime.now().month}-${DateTime.now().day}"
          .obs;
  final InputDatetimeController tanggalCon = InputDatetimeController();
  final InputDropdownController divisiCon = InputDropdownController();
  final InputDropdownController statusCon = InputDropdownController(
    items: [
      DropdownItem(text: 'Semua', value: 0),
      DropdownItem(text: 'Tepat Waktu', value: 1),
      DropdownItem(text: 'Terlambat', value: 2),
      DropdownItem(text: 'Belum Absen', value: 3),
      DropdownItem(text: 'Cuti/Tidak Ada Jadwal', value: 4),
    ],
  );
  RxInt valueDivisi = 0.obs;
  final List<MenuItemModel> menus = [];
  RxInt selectedIndex = 0.obs;

  RxBool isLoading = true.obs;
  RxBool isFilter = false.obs;

  @override
  void onInit() async {
    tanggalCon.value = DateTime.now();
    valueDivisi.value = MahasConfig.selectedDivisi ?? 0;
    divisiCon.onChanged = (v) {
      valueDivisi.value = v!.value;
    };
    statusCon.value = 0;
    menus.add(MenuItemModel('Masuk', Icons.login, () {}));
    menus.add(MenuItemModel('Keluar', Icons.logout, () {}));

    await getDivisi();
    await onRefresh();
    super.onInit();
  }

  Future<void> filterOnTap() async {
    var hariIni =
        DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day);

    divisiCon.value = valueDivisi.value;

    Helper.dialogFilterCustom(
      confirmAction: () {
        tanggalCon.value ??= hariIni;
        tanggal.value = MahasFormat.dateToString(tanggalCon.value)!;
        onRefresh();
        if (tanggalCon.value != hariIni ||
            valueDivisi.value != MahasConfig.selectedDivisi ||
            statusCon.value != 0) {
          isFilter.value = true;
        } else {
          isFilter.value = false;
        }
      },
      isClear: isFilter.value,
      clearAction: () {
        valueDivisi.value = MahasConfig.selectedDivisi ?? 0;
        tanggalCon.value = hariIni;
        tanggal.value = tanggalCon.value.toString();
        statusCon.value = 0;
        onRefresh();
        isFilter.value = false;
      },
      children: [
        InputDatetimeComponent(
          label: "Tanggal",
          controller: tanggalCon,
          editable: true,
          marginBottom: 0,
        ),
        const SizedBox(
          height: 10,
        ),
        InputDropdownComponent(
          label: "Divisi",
          controller: divisiCon,
          editable: true,
          marginBottom: 0,
        ),
        const SizedBox(
          height: 10,
        ),
        InputDropdownComponent(
          label: "Status",
          controller: statusCon,
          editable: true,
          marginBottom: 0,
        ),
      ],
    );
  }

  Future<void> onRefresh() async {
    isLoading.value = true;
    await getAllData();
    isLoading.value = false;
  }

  Future<void> getAllData() async {
    var r = await HttpApi.get(
        '/api/Absensi/HariIni?tanggal=${tanggal.value}&idDivisi=${valueDivisi.value}');
    if (r.success) {
      listCon = InformasiAbsensiKeluarMasukModel.fromJson(r.body!);
      masukList.clear();
      keluarList.clear();
      masukList = listCon.masuk ?? [];
      keluarList = listCon.keluar ?? [];
      statusFilter();
    } else {
      Helper.fetchErrorMessage(r, httpMethod: HttpMethod.get);
    }
  }

  Future<void> getDivisi() async {
    var r = await HttpApi.get("/api/Divisi");
    if (r.success) {
      RxList<DivisiModel> listModel = RxList<DivisiModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(DivisiModel.fromDynamic(e));
      }
      if (r.body != null && divisiCon.items.isEmpty) {
        divisiCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      }
    } else {
      Helper.fetchErrorMessage(r, httpMethod: HttpMethod.get);
    }
  }

  Color colorKeterangan(String keterangan) {
    switch (keterangan) {
      case "Belum Absen":
        return MahasColor.colorOrange;
      case "Terlambat" || "Pulang Mendahului":
        return MahasColor.colorRed;
      case "Absen Masuk" || "Absen Pulang":
        return MahasColor.colorGreen;
      default:
        return MahasColor.greyInputan;
    }
  }

  void statusFilter() {
    if (listCon.masuk == null && listCon.keluar == null) return;

    switch (statusCon.value) {
      case 0: // Semua
        masukList = listCon.masuk ?? [];
        keluarList = listCon.keluar ?? [];
        break;
      case 1: // Tepat Waktu
        masukList = listCon.masuk
                ?.where((e) => e.keterangan == 'Absen Masuk')
                .toList() ??
            [];
        keluarList = listCon.keluar
                ?.where((e) => e.keterangan == 'Absen Pulang')
                .toList() ??
            [];
        break;
      case 2: // Terlambat
        masukList =
            listCon.masuk?.where((e) => e.keterangan == 'Terlambat').toList() ??
                [];
        keluarList = listCon.keluar
                ?.where((e) => e.keterangan == 'Pulang Mendahului')
                .toList() ??
            [];
        break;
      case 3: // Belum Absen
        masukList = listCon.masuk
                ?.where((e) => e.keterangan == 'Belum Absen')
                .toList() ??
            [];
        keluarList = listCon.keluar
                ?.where((e) => e.keterangan == 'Belum Absen')
                .toList() ??
            [];
        break;
      case 4: // Cuti/Tidak Ada Jadwal (Ambil yang bukan Tepat Waktu, Terlambat, dan Belum Absen)
        final excluded = {
          'Absen Masuk',
          'Absen Pulang',
          'Terlambat',
          'Pulang Mendahului',
          'Belum Absen'
        };
        masukList = listCon.masuk
                ?.where((e) => !excluded.contains(e.keterangan))
                .toList() ??
            [];
        keluarList = listCon.keluar
                ?.where((e) => !excluded.contains(e.keterangan))
                .toList() ??
            [];
        break;
    }
  }
}
