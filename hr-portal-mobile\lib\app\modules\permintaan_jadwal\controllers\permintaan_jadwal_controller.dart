import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:hr_portal/app/mahas/models/menu_item_model.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/mahas/services/http_api.dart';
import 'package:hr_portal/app/models/divisi_model.dart';
import 'package:hr_portal/app/routes/app_pages.dart';

import '../../../models/profile_model.dart';

class PermintaanJadwalController extends GetxController {
  final List<MenuItemModel> menus = [];
  RxBool isKadiv = false.obs;
  RxBool isAtasan = false.obs;
  List<DivisiModel>? listDivisi;

  final List<ProfilDivisiModel>? divisiPegawai = MahasConfig.profile?.divisi;

  void cHamil() {
    Get.toNamed(Routes.PERMINTAAN_JADWAL_CUTI_HAMIL);
  }

  void cSakit() {
    Get.toNamed(Routes.PERMINTAAN_JADWAL_CUTI_SAKIT);
  }

  void cTukarLembur() {
    Get.toNamed(Routes.PERMINTAAN_JADWAL_CUTI_TUKAR_LEMBUR);
  }

  void cTahunan() {
    Get.toNamed(Routes.PERMINTAAN_JADWAL_CUTI_TAHUNAN);
  }

  void izin() {
    Get.toNamed(Routes.PERMINTAAN_JADWAL_IZIN);
  }

  void lembur() {
    if (MahasConfig.profileMenu.customLemburPage && !isKadiv.value) {
      Get.toNamed(Routes.PERMINTAAN_JADWAL_LEMBUR_CUSTOM_PEGAWAI_SETUP);
    } else {
      Get.toNamed(Routes.PERMINTAAN_JADWAL_LEMBUR);
    }
  }

  void tukarJadwal() {
    Get.toNamed(Routes.PERMINTAAN_JADWAL_TUKAR_JADWAL);
  }

  void cDayPayment() {
    Get.toNamed(Routes.PERMINTAAN_JADWAL_CUTI_DAY_PAYMENT);
  }

  void cUnpaidLeave() {
    Get.toNamed(Routes.PERMINTAAN_JADWAL_CUTI_UNPAID_LEAVE);
  }

  void cSetengahHari() {
    Get.toNamed(Routes.PERMINTAAN_JADWAL_CUTI_SETENGAH_HARI);
  }

  void approval() {
    Get.toNamed(Routes.APPROVAL);
  }

  getDivisi() async {
    var r = await HttpApi.get('/api/Profile/Divisi');
    if (r.success) {
      listDivisi = divisiModelFromJson(r.body);
      for (var i = 0; i < listDivisi!.length; i++) {
        if (MahasConfig.selectedDivisi == listDivisi![i].id) {
          if (MahasConfig.profile!.nama == listDivisi![i].kadiv) {
            isKadiv.value = true;
          }
        }
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning("Gagal memuat data, silahkan cek koneksi internet");
    } else {
      Helper.dialogWarning(r.message);
    }
  }

  @override
  void onInit() {
    getKadivOrManager();
    if (MahasConfig.profile?.statusPegawai?.toLowerCase() == "freelance" ||
        MahasConfig.profile?.statusPegawai?.toLowerCase() == "trainee") {
      menus.add(
          MenuItemModel('Izin', FontAwesomeIcons.personCircleQuestion, izin));
      menus.add(MenuItemModel('Lembur', FontAwesomeIcons.clock, lembur));
      menus.add(MenuItemModel(
          'Tukar Jadwal', FontAwesomeIcons.timeline, tukarJadwal));
      menus.add(MenuItemModel('Approval', FontAwesomeIcons.check, approval));
    } else {
      if (MahasConfig.profile?.jeniskelamin == "Perempuan" ||
          MahasConfig.profile?.jeniskelamin == null) {
        menus.add(MenuItemModel(
            MahasConfig.dinamisForm.cutiHamil?.tittle ?? "Cuti Hamil",
            FontAwesomeIcons.personPregnant,
            cHamil));
      }
      menus.add(MenuItemModel(
          MahasConfig.dinamisForm.cutiSakit?.tittle ?? "Cuti Sakit",
          FontAwesomeIcons.faceTired,
          cSakit));
      if (MahasConfig.profileMenu.cutiTukarLembur) {
        menus.add(MenuItemModel(
            'Cuti Tukar Lembur', FontAwesomeIcons.businessTime, cTukarLembur));
      }
      menus.add(MenuItemModel(
          'Cuti Tahunan', FontAwesomeIcons.calendarXmark, cTahunan));
      if (MahasConfig.profileMenu.cutiSetengahHari) {
        menus.add(MenuItemModel(
            'Cuti Setengah Hari', FontAwesomeIcons.calendarDay, cSetengahHari));
      }
      if (MahasConfig.profileMenu.cutiDayPayment) {
        menus.add(MenuItemModel(
            'Cuti Day Payment', FontAwesomeIcons.calendarWeek, cDayPayment));
      }
      if (MahasConfig.profileMenu.cutiUnpaidLeave) {
        menus.add(MenuItemModel('Izin Tanpa Ditanggung Perusahaan',
            FontAwesomeIcons.calendarCheck, cUnpaidLeave));
      }
      menus.add(MenuItemModel(MahasConfig.dinamisForm.izin?.tittle ?? "Izin",
          FontAwesomeIcons.personCircleQuestion, izin));
      if (MahasConfig.profileMenu.lembur) {
        menus.add(MenuItemModel('Lembur', FontAwesomeIcons.clock, lembur));
      }
      if (MahasConfig.profileMenu.tukarJadwal) {
        menus.add(MenuItemModel(
            'Tukar Jadwal', FontAwesomeIcons.timeline, tukarJadwal));
      }

      if (MahasConfig.dinamisForm.approval?.khususAtasan == true) {
        if (isAtasan.value) {
          menus
              .add(MenuItemModel('Approval', FontAwesomeIcons.check, approval));
        }
      } else {
        menus.add(MenuItemModel('Approval', FontAwesomeIcons.check, approval));
      }
    }
    getDivisi();
    super.onInit();
  }

  void getKadivOrManager() async {
    divisiPegawai?.forEach((element) {
      if (element.idDivisi == MahasConfig.selectedDivisi) {
        if (element.sebagaikadiv == true || element.sebagaimanager == true) {
          isAtasan.value = true;
        }
      }
    });
  }
}
