// ignore_for_file: unnecessary_overrides

import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../models/profile_rekening_model.dart';
import '../../../routes/app_pages.dart';

class ProfileRekeningController extends GetxController {
  final listCon = ListComponentController<ProfileRekeningModel>(
    urlApi: (index, filter) => '/api/PegawaiRekening/List?pageIndex=$index',
    fromDynamic: ProfileRekeningModel.fromDynamic,
    allowSearch: false,
  );

  @override
  void onInit() {
    super.onInit();
  }

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PROFILE_REKENING_SETUP,
      parameters: {
        'id': id.toString(),
      },
    )?.then((value) {
      listCon.refresh();
    });
  }

  void popupMenuButtonOnSelected(String v) async {
    if (v == 'Tambah') {
      Get.toNamed(Routes.PROFILE_REKENING_SETUP)?.then(
        (value) {
          listCon.refresh();
        },
      );
    } else if (v == 'History') {
      Get.toNamed(
        Routes.PROFILE_REKENING_HISTORY,
        parameters: {
          'showStatus': true.toString(),
        },
      )?.then(
        (value) {
          listCon.refresh();
        },
      );
    }
  }
}
