import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class InformasiquotajenisizinModel {
  String? jenisizin;
  int? tahun;
  int? quota;
  int? terpakai;
  int? sisaizin;

  InformasiquotajenisizinModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static InformasiquotajenisizinModel fromDynamic(dynamic dynamicData) {
    final model = InformasiquotajenisizinModel();

    model.jenisizin = dynamicData['jenisIzin'];
    model.tahun = MahasFormat.dynamicToInt(dynamicData['tahun']);
    model.quota = MahasFormat.dynamicToInt(dynamicData['quota']);
    model.terpakai = MahasFormat.dynamicToInt(dynamicData['terpakai']);
    model.sisaizin = MahasFormat.dynamicToInt(dynamicData['sisaIzin']);

    return model;
  }
}
