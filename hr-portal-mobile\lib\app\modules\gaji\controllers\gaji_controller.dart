import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../models/gaji_model.dart';
import '../../../routes/app_pages.dart';

class GajiController extends GetxController {
  final listCon = ListComponentController<GajiModel>(
    urlApi: (index, filter) => '/api/SlipGaji/List?pageIndex=$index',
    fromDynamic: GajiModel.fromDynamic,
    allowSearch: false,
  );

  final listConStatis = ListComponentController<SlipGajiStatisModel>(
    urlApi: (index, filter) => '/api/SlipGaji/Statis/List?pageIndex=$index',
    fromDynamic: SlipGajiStatisModel.fromDynamic,
    allowSearch: false,
  );

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.GAJI_SETUP,
      parameters: {
        'id': id.toString(),
      },
    );
  }
}
