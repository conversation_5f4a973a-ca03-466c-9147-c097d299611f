import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:path/path.dart' as p;

import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/models/api_list_resut_model.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../models/klasifikasi_pendidikan_model.dart';
import '../../../models/pendidikan_model.dart';
import '../../../models/profile_riwayat_pendidikan_model.dart';
import '../../../routes/app_pages.dart';

class ProfileRiwayatPendidikanSetupController extends GetxController {
  late SetupPageController formCon;
  final pendidikanCon = InputDropdownController();
  final klasifikasiPendidikanCon = InputDropdownController();
  final programStudiCon = InputTextController();
  final dariTahunCon = InputTextController(type: InputTextType.number);
  final sampaiTahunCon = InputTextController(type: InputTextType.number);
  final keteranganCon = InputTextController(type: InputTextType.paragraf);
  final nomorIjazahCon = InputTextController();
  final fileCon = InputFileController(
    urlImage: true,
    type: FileType.custom,
    tipe: InputFileType.pdf,
    extension: ["pdf", "jpg", "jpeg", "png"],
  );

  RxInt idPegawaiRiwayatPendidikan = 0.obs;

  @override
  void onInit() {
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PegawaiRiwayatPendidikan/$id',
      urlApiDelete: (id) => '/api/PegawaiRiwayatPendidikan/$id',
      urlApiPut: (id) => '/api/PegawaiRiwayatPendidikan/$id',
      urlApiPost: () => '/api/PegawaiRiwayatPendidikan',
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "id_PegawaiRiwayatPendidikan": idPegawaiRiwayatPendidikan.value,
        "id_KlasifikasiPendidikan": klasifikasiPendidikanCon.value,
        "id_Pendidikan": pendidikanCon.value,
        "dariTahun": dariTahunCon.value,
        "sampaiTahun": sampaiTahunCon.value,
        "keterangan": keteranganCon.value,
        "filePhoto": fileCon.value,
        "fileName": fileCon.name,
        "programStudi": programStudiCon.value,
        "nomorIjazah": nomorIjazahCon.value,
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (!pendidikanCon.isValid) return false;
        if (!klasifikasiPendidikanCon.isValid) return false;
        if (!programStudiCon.isValid) return false;
        if (!dariTahunCon.isValid) return false;
        if (!sampaiTahunCon.isValid) return false;

        return true;
      },
      apiToView: (json) {
        RiwayatpendidikanModel model = RiwayatpendidikanModel.fromJson(json);

        pendidikanCon.value = model.idPendidikan;
        klasifikasiPendidikanCon.value = model.idKlasifikasipendidikan;
        programStudiCon.value = model.programstudi;
        dariTahunCon.value = model.daritahun;
        sampaiTahunCon.value = model.sampaitahun;
        keteranganCon.value = model.keterangan;
        nomorIjazahCon.value = model.nomorijazah;
        fileCon.value = model.pathFoto;

        //non mahas
        idPegawaiRiwayatPendidikan.value = model.id ?? 0;

        if (model.pathFoto != null) {
          String extension = p.extension(model.pathFoto!).toLowerCase();
          if (extension == ".pdf") {
            updateState(InputFileType.pdf);
          } else {
            updateState(InputFileType.image);
          }
        }
      },
      onInit: () async {
        await getPendidikan();
        await getKlasifikasiPendidikan();
      },
    );

    if (MahasConfig.hasHistory) {
      formCon.onSuccessSubmit = (r) {
        var idHistory = json.decode(r.body)['id'];
        Get.toNamed(
          Routes.PROFILE_RIWAYAT_PENDIDIKAN_HISTORY_SETUP,
          parameters: {
            'id': idHistory.toString(),
            'showStatus': true.toString(),
            'withActionBack': true.toString(),
          },
        );
      };
      formCon.deleteOnTap = (id) {
        Get.toNamed(
          Routes.PROFILE_RIWAYAT_PENDIDIKAN_HISTORY_SETUP,
          parameters: {
            'id': id.toString(),
            'showStatus': true.toString(),
            'withActionBack': true.toString(),
          },
        );
      };
    }
    super.onInit();
  }

  void updateState(InputFileType tipe) {
    fileCon.tipe = tipe;
  }

  Future<void> getPendidikan() async {
    if (EasyLoading.isShow) {
      EasyLoading.dismiss();
    }
    EasyLoading.show();
    var r = await HttpApi.get("/api/Pendidikan");
    if (r.success) {
      RxList<PendidikanModel> listModel = RxList<PendidikanModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(PendidikanModel.fromDynamic(e));
      }
      if (pendidikanCon.items.isNotEmpty) {
        pendidikanCon.items.clear();
      }
      if (listModel.isNotEmpty && pendidikanCon.items.isEmpty) {
        pendidikanCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      }
    } else {
      Helper.fetchErrorMessage(r);
    }
    EasyLoading.dismiss();
  }

  Future<void> getKlasifikasiPendidikan() async {
    if (EasyLoading.isShow) {
      EasyLoading.dismiss();
    }
    EasyLoading.show();
    var r = await HttpApi.get("/api/KlasifikasiPendidikan");
    if (r.success) {
      RxList<KlasifikasiPendidikanModel> listModel =
          RxList<KlasifikasiPendidikanModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(KlasifikasiPendidikanModel.fromDynamic(e));
      }
      if (klasifikasiPendidikanCon.items.isNotEmpty) {
        klasifikasiPendidikanCon.items.clear();
      }
      if (listModel.isNotEmpty && klasifikasiPendidikanCon.items.isEmpty) {
        klasifikasiPendidikanCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      }
    } else {
      Helper.fetchErrorMessage(r);
    }
    EasyLoading.dismiss();
  }
}
