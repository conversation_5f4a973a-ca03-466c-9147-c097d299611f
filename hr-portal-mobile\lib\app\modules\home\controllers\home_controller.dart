// ignore_for_file: unused_local_variable

import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'dart:convert';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hr_portal/app/constant/environment_constant.dart';
import 'package:hr_portal/app/mahas/components/mahas_themes.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:hr_portal/app/mahas/models/api_list_resut_model.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';
import 'package:hr_portal/app/mahas_complement/helper.dart';
import 'package:hr_portal/app/models/absen_jadwal_model.dart';
import 'package:hr_portal/app/models/absensi_model.dart';
import 'package:hr_portal/app/models/profile_model.dart';
import 'package:hr_portal/app/models/shift_hari_ini_model.dart';
import 'package:hr_portal/app/services/local_notification_service.dart';
import 'package:intl/intl.dart';
import 'package:flutter/foundation.dart';
import 'package:get_storage/get_storage.dart';
import 'package:hr_portal/app/mahas/mahas_service.dart';
import 'package:hr_portal/app/routes/app_pages.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/mahas_colors.dart';
import '../../../mahas/models/api_result_model.dart';
import '../../../mahas/services/http_api.dart';
import 'package:timezone/timezone.dart' as tz;

import '../../../mahas_complement/profile_storage.dart';
import '../../../models/lokasi_absen_model.dart';
import '../../../models/notifikasi_model.dart';
import '../../../services/connection_checker_service.dart';
import '../../../services/upgrade_app_service.dart';
import '../../../services/utils_service.dart';

class HomeController extends GetxController {
  static final storange = GetStorage();
  Position? _position;
  late String _device;
  late String _os;
  Rxn<ProfileModel> profile = Rxn(MahasConfig.profile!);
  List<ProfilDivisiModel> dropdown = MahasConfig.profile!.divisi!;
  late Rx<ProfilDivisiModel?> selectedDrowpdown;
  RxBool notifikasi = false.obs;
  RxString jammasuk = "Datang".obs;
  RxString jamkeluar = "Pulang".obs;
  RxString jamMasukLembur = "Clock In".obs;
  RxString jamKeluarLembur = "Clock Out".obs;
  DateTime? jamMasukAbsensi;
  DateTime? jamKeluarAbsensi;
  DateTime? jamMasukAbsensiFaceRecognition;
  DateTime? jamKeluarAbsensiFaceRecognition;
  DateTime? jamMasukAbsensiLembur;
  DateTime? jamKeluarAbsensiLembur;
  RxString jamMasukIstirahat = "Mulai Istirahat".obs;
  RxString jamKeluarIstirahat = "Selesai Istirahat".obs;
  List<Absen> listAbsensi = [];
  List<Absen> listAbsensiLembur = [];
  List<AbsensiFacereCognitionModel> listAbsensiFaceRecognition = [];
  AbsensiLemburModel dataAbsensiLembur = AbsensiLemburModel();
  AbsensiIstirahatModel dataAbsensIstirahat = AbsensiIstirahatModel();
  List<Absen> listAbsensiIstirahat = [];
  RxString shift = "Tidak ada shift hari ini".obs;
  RxString jamMulaiShift = "-".obs;
  RxString jamSelesaiShift = "-".obs;
  RxString absensi = "---".obs;
  RxBool absenVisible = true.obs;
  RxBool hasDataAbsen = true.obs;
  RxBool absenBerhasil = false.obs;
  RxBool absenVisibleLembur = true.obs;
  RxBool hasDataAbsenLembur = false.obs;
  RxBool absenLemburBerhasil = false.obs;
  final Color _gantiWarna = Colors.brown;
  RxBool lokasiAbsensi = false.obs;
  RxBool popUpBool = false.obs;
  List<LokasiabsenModel> locations = [];
  LokasiabsenModel? selectedLocation;
  final imageFile = Rxn<File>();
  final images = Rxn<Uint8List>();
  RxBool absenIstirahatMasukBerhasil = false.obs;
  RxBool absenIstirahatKeluarBerhasil = false.obs;
  final keteranganLemburCon = InputTextController();

  void divisionChanged(ProfilDivisiModel? val) {
    selectedDrowpdown.value = val;
    MahasConfig.selectedDivisi = val?.idDivisi;
    getKadivOrManager();
  }

  @override
  void onInit() {
    _determinePosition();
    notificationPermission();
    if (MahasConfig.hasInternet == true) {
      getImage();
      cekJadwalWithNotif();
      cekNotif();
      getAbsen("");
      lokasiAbsen();
      _checkUserDevice();
      getDataAbsensiIstirahat("");
      if (MahasConfig.profileMenu.cutiSetengahHari == true) {
        cekCutiSetengahHari();
      }
    }
    selectedDrowpdown = dropdown
        .firstWhereOrNull((e) => e.idDivisi == MahasConfig.selectedDivisi)
        .obs;
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    if (MahasConfig.hasInternet == true) {
      UpgradeAppService.checkingUpdateApp();
      UtilsService.checkBirthday();
      UtilsService.checkKontrak();
      UtilsService.checkSertifikasi();
      if (MahasConfig.profileMenu.str == true) {
        UtilsService.checkSIP();
      }
      if (MahasConfig.profileMenu.kgb == true) {
        UtilsService.checkKGB();
      }
      getKadivOrManager();
      notifikasiPermission();
      notifCreateJadwal();
    } else {
      ifNoInternet();
    }
  }

  Future<void> getProfile() async {
    var r = await HttpApi.get('/api/Profile');
    if (r.success) {
      MahasConfig.profile = ProfileModel.fromJson(r.body);
      profile.value = MahasConfig.profile;
      ProfileService.profile = r.body;
      ProfileService.savedata();
    }
  }

  void getImage() {
    if (MahasConfig.profile?.urlphoto != null) {
      images.value = base64Decode(profile.value!.urlphoto!);
    }
  }

  void itemOnTab(int id) {
    Get.toNamed(Routes.PERMINTAAN_JADWAL_LEMBUR_CUSTOM_KEPALA_UNIT_SETUP,
        parameters: {"approve": "approve", "id": id.toString()},
        arguments: {"pegawai": true});
  }

  Future onRefresh() async {
    await InternetConnectionService.isInternet();
    if (MahasConfig.hasInternet == true) {
      await MahasService.initApp();
      await getProfile();
      lokasiAbsen();
      getImage();
      cekJadwalWithNotif();
      getAbsen("");
      cekNotif();
      getCurrentLocation();
      getKadivOrManager();
    } else {
      Helper.dialogWarning(
          "Pastikan terkoneksi dengan internet, lalu coba lagi");
    }
    return true;
  }

  void ifNoInternet() {
    Helper.dialogWarning(
        "Anda tidak terkoneksi dengan Internet! Tarik layar kebawah untuk memuat ulang!");
  }

  void getKadivOrManager() async {
    MahasConfig.profile?.divisi?.forEach((element) {
      if (element.idDivisi == MahasConfig.selectedDivisi) {
        MahasConfig.atasan.isKadiv =
            element.sebagaikadiv == true ? true : false;

        MahasConfig.atasan.isManager =
            element.sebagaimanager == true ? true : false;
      }
    });
  }

  _checkUserDevice() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (!kIsWeb) {
      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        _device = ('${androidInfo.brand}_${androidInfo.id}').toString();
        _os = androidInfo.version.release.toString();
      } else if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        _device = '${iosInfo.utsname.machine}_${iosInfo.name}';
        _os = iosInfo.systemVersion.toString();
      } else if (kIsWeb) {
        WebBrowserInfo webBrowserInfo = await deviceInfo.webBrowserInfo;
        _device = webBrowserInfo.userAgent.toString();
        _os = webBrowserInfo.platform.toString();
      }
    }
  }

  void absenOnTab() async {
    if (EasyLoading.isShow) {
      EasyLoading.dismiss();
    }
    EasyLoading.show();
    if (MahasConfig.profileMenu.absen == true) {
      if (MahasConfig.hasInternet == true) {
        await actionAbsen();
      } else {
        Helper.dialogWarning(
          "Tidak dapat melakukan absensi karena tidak ada koneksi internet",
        );
      }
    } else {
      Helper.dialogWarning(
          "Fitur absensi belum aktif, silahkan gunakan absensi manual");
    }
    EasyLoading.dismiss();
  }

  Future<void> actionAbsen() async {
    bool haveShift = await getShift();
    if (haveShift) {
      var gpsEnable = await cekEnableGps();
      if (gpsEnable) {
        await getCurrentLocation();
        if (MahasConfig.hasInternet == true) {
          if (hasDataAbsen.value) {
            absenVisible.value = true;
            lokasiAbsensi.value = false;
            _openDialogAbsensi();
            hasDataAbsen.value = false;
          }
        } else {
          Helper.dialogWarning(
            "Tidak dapat melakukan absensi karena tidak ada koneksi internet",
          );
        }
      }
    }
  }

  Future<void> notificationPermission() async {
    PermissionStatus permission;
    permission = await Permission.notification.request();
    if (permission == PermissionStatus.denied) {
      permission = await Permission.notification.request();
      if (permission == PermissionStatus.denied) {
        Helper.dialogWarning("Harap berikan izin notifikasi");
      }
    }

    if (permission == PermissionStatus.permanentlyDenied) {
      permission = await Permission.notification.request();
    }
  }

  Future<void> actionAbsenLembur() async {
    Get.back();
    if (EasyLoading.isShow) EasyLoading.dismiss();
    EasyLoading.show();
    await getAbsenLembur();
    var gpsEnable = await cekEnableGps();
    if (gpsEnable) {
      await getCurrentLocation();
      EasyLoading.dismiss();
      if (MahasConfig.hasInternet == true) {
        if (hasDataAbsenLembur.value) {
          absenVisibleLembur.value = true;
          lokasiAbsensi.value = false;
          _openDialogAbsensiLembur();
        }
      } else {
        Helper.dialogWarning(
          "Tidak dapat melakukan absensi karena tidak ada koneksi internet",
        );
      }
    }
    EasyLoading.dismiss();
  }

  Future<void> actionAbsenIstirahat() async {
    Get.back();
    if (EasyLoading.isShow) EasyLoading.dismiss();
    EasyLoading.show();

    var gpsEnable = await cekEnableGps();
    if (gpsEnable) {
      await getCurrentLocation();
      EasyLoading.dismiss();
      if (MahasConfig.hasInternet == true) {
        _openDialogAbsensiIstirahat();
      } else {
        Helper.dialogWarning(
          "Tidak dapat melakukan absensi karena tidak ada koneksi internet",
        );
      }
    }
    EasyLoading.dismiss();
  }

  void lokasiAbsen() async {
    var r = await HttpApi.get('/api/Absensi/DaftarLokasi');
    if (r.success) {
      final datas = json.decode(r.body);
      locations.clear();
      for (var e in datas) {
        locations.add(LokasiabsenModel.fromDynamic(e));
      }
    } else {
      Helper.dialogWarning(r.message);
    }
  }

  Future<bool> cekRadius() async {
    // cek lokasi pengguna setiap absensi
    await getCurrentLocation();

    if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Tidak ada koneksi internet, coba beberapa saat lagi!");
      return false;
    }
    // Koordinat pengguna
    double userLat = _position!.latitude;
    double userLon = _position!.longitude;

    // Memeriksa apakah pengguna berada dalam lokasi yang ada di daftar
    LokasiabsenModel? isUserInLocation =
        LokasiabsenModel().isUserInLocation(userLat, userLon, locations);

    if (isUserInLocation != null) {
      selectedLocation = isUserInLocation;
      return true;
    } else {
      Helper.dialogWarning("Anda tidak berada dalam radius\nLokasi Absensi");
      return false;
    }
  }

  void _openDialogAbsensi() {
    absensi.value = "---";
    HelperComp.dialogCustomWidget([
      const Icon(FontAwesomeIcons.mapPin),
      const SizedBox(
        height: 10,
      ),
      Obx(() {
        return lokasiAbsensi.value == true
            ? Text(
                shift.value,
                textAlign: TextAlign.center,
                style: MahasThemes.title,
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    shift.value,
                    textAlign: TextAlign.center,
                    style: MahasThemes.title,
                  ),
                  Text(
                    jamMulaiShift.value != "-" && jamSelesaiShift.value != "-"
                        ? "  (${jamMulaiShift.value} - ${jamSelesaiShift.value})"
                        : "",
                    textAlign: TextAlign.center,
                    style: MahasThemes.title,
                  ),
                ],
              );
      }),
      Obx(
        () {
          return Text(
            absensi.value,
            textAlign: TextAlign.center,
          );
        },
      ),
      const SizedBox(
        height: 10,
      ),
      Obx(() {
        return Visibility(
          visible: absenVisible.value,
          child: Row(
            children: [
              Obx(() {
                return Expanded(
                  child: absenBerhasil.value == true &&
                          jammasuk.value != "Datang"
                      ? ButtonAbsen(
                          icon: FontAwesomeIcons.arrowRightToBracket,
                          subtitle: jammasuk.value,
                          gantiWarna: _gantiWarna,
                          onTap: () {
                            if (MahasConfig.profileMenu.absenwithfoto == true) {
                              absenWithPhotoOnPress(true);
                            } else {
                              absenOnPress(true);
                            }
                          },
                        )
                      : ButtonAbsen(
                          icon: FontAwesomeIcons.arrowRightToBracket,
                          subtitle: jammasuk.value,
                          onTap: () {
                            if (MahasConfig.profileMenu.absenwithfoto == true) {
                              absenWithPhotoOnPress(true);
                            } else {
                              absenOnPress(true);
                            }
                          },
                        ),
                );
              }),
              const SizedBox(
                width: 10,
              ),
              Obx(() {
                return Expanded(
                  child: absenBerhasil.value == true &&
                          jamkeluar.value != "Pulang"
                      ? ButtonAbsen(
                          icon: FontAwesomeIcons.arrowRightFromBracket,
                          subtitle: jamkeluar.value,
                          gantiWarna: _gantiWarna,
                          onTap: () {
                            if (MahasConfig.profileMenu.absenwithfoto == true) {
                              absenWithPhotoOnPress(false);
                            } else {
                              absenOnPress(false);
                            }
                          },
                        )
                      : ButtonAbsen(
                          icon: FontAwesomeIcons.arrowRightFromBracket,
                          subtitle: jamkeluar.value,
                          onTap: () {
                            if (MahasConfig.profileMenu.absenwithfoto == true) {
                              absenWithPhotoOnPress(false);
                            } else {
                              absenOnPress(false);
                            }
                          },
                        ),
                );
              }),
            ],
          ),
        );
      }),
      const SizedBox(
        height: 10,
      ),
      Obx(() => Visibility(
            visible: absenVisible.value,
            child: SizedBox(
              height: 40,
              width: double.infinity,
              child: TextButton(
                  onPressed: () {
                    Get.back(result: true);
                    Get.toNamed(Routes.ABSEN_SURAT_TUGAS_SETUP);
                  },
                  child: Text(
                    "${MahasConfig.dinamisForm.absensi?.title ?? 'Absensi'} Surat Tugas",
                    textAlign: TextAlign.center,
                    style: MahasThemes.link,
                  )),
            ),
          )),
      Visibility(
        visible: MahasConfig.profileMenu.absensiQr == true,
        child: SizedBox(
          height: 40,
          width: double.infinity,
          child: TextButton(
              onPressed: () {
                Get.back(result: true);
                Get.toNamed(Routes.QR_CODE_SCANNER, arguments: {
                  "jenis": "absenQR",
                  "os": _os,
                  "device": _device,
                  "latitude": _position!.latitude,
                  "longitude": _position!.longitude
                })?.then((value) {
                  if (value != null) {
                    getAbsen(value);
                  }
                });
              },
              child: Text(
                "Absensi QR Code",
                textAlign: TextAlign.center,
                style: MahasThemes.link,
              )),
        ),
      ),
      Visibility(
        visible: MahasConfig.profileMenu.customLemburPage == true,
        child: SizedBox(
          height: 40,
          width: double.infinity,
          child: TextButton(
              onPressed: () {
                actionAbsenLembur();
              },
              child: const Text(
                "Absensi Lembur",
                textAlign: TextAlign.center,
                style: TextStyle(
                    fontWeight: FontWeight.bold, color: MahasColors.green),
              )),
        ),
      ),
      Visibility(
        visible: MahasConfig.profileMenu.absensiIstirahat == true,
        child: SizedBox(
          height: 40,
          width: double.infinity,
          child: TextButton(
              onPressed: () {
                actionAbsenIstirahat();
              },
              child: const Text(
                "Absensi Istirahat",
                textAlign: TextAlign.center,
                style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Color.fromARGB(255, 231, 145, 54)),
              )),
        ),
      ),
    ]);
  }

  void _openDialogAbsensiIstirahat() {
    HelperComp.dialogCustomWidget([
      const Icon(FontAwesomeIcons.mugHot),
      const SizedBox(
        height: 10,
      ),
      Text(
        "ABSENSI ISTIRAHAT",
        textAlign: TextAlign.center,
        style: MahasThemes.title,
      ),
      const SizedBox(
        height: 10,
      ),
      Obx(
        () => Row(
          children: [
            Expanded(
              child: absenIstirahatMasukBerhasil.value == true &&
                      jamMasukIstirahat.value != "Mulai istirahat"
                  ? ButtonAbsen(
                      icon: FontAwesomeIcons.arrowRightToBracket,
                      subtitle: jamMasukIstirahat.value,
                      gantiWarna: _gantiWarna,
                      fontSize: 13,
                      //onTap: () => absensiIstirahatOnPress(true),
                    )
                  : ButtonAbsen(
                      icon: FontAwesomeIcons.arrowRightToBracket,
                      subtitle: "Mulai istirahat",
                      onTap: () => absensiIstirahatOnPress(true),
                      fontSize: 13,
                    ),
            ),
            const SizedBox(
              width: 10,
            ),
            Expanded(
              child: absenIstirahatKeluarBerhasil.value == true &&
                      jamKeluarIstirahat.value != "Selesai istirahat"
                  ? ButtonAbsen(
                      icon: FontAwesomeIcons.arrowRightFromBracket,
                      subtitle: jamKeluarIstirahat.value,
                      gantiWarna: _gantiWarna,
                      fontSize: 13,
                      //onTap: () => absensiIstirahatOnPress(false),
                    )
                  : ButtonAbsen(
                      icon: FontAwesomeIcons.arrowRightFromBracket,
                      subtitle: "Selesai istirahat",
                      onTap: () => absensiIstirahatOnPress(false),
                      fontSize: 13,
                    ),
            ),
          ],
        ),
      ),
      const SizedBox(
        height: 10,
      ),
    ]);
  }

  void _openDialogAbsensiLembur() {
    HelperComp.dialogCustomWidget([
      const Icon(FontAwesomeIcons.briefcase),
      const SizedBox(
        height: 10,
      ),
      Text(
        "ABSENSI LEMBUR",
        textAlign: TextAlign.center,
        style: MahasThemes.title,
      ),
      const SizedBox(
        height: 10,
      ),
      Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            "${MahasFormat.displayDate(dataAbsensiLembur.tanggalLembur)} (${MahasFormat.displayTime(dataAbsensiLembur.jamLembur)})",
            textAlign: TextAlign.center,
            style: MahasThemes.title,
          ),
          const SizedBox(
            height: 10,
          ),
          Text(
            "${dataAbsensiLembur.lamaHari}d ${dataAbsensiLembur.lamaJam}h ${dataAbsensiLembur.lamaMenit}m",
            textAlign: TextAlign.center,
            style: MahasThemes.title,
          ),
        ],
      ),
      const SizedBox(
        height: 10,
      ),
      Obx(
        () => Row(
          children: [
            Expanded(
              child: absenLemburBerhasil.value == true &&
                      jamMasukLembur.value != "Clock In"
                  ? ButtonAbsen(
                      icon: FontAwesomeIcons.arrowRightToBracket,
                      subtitle: jamMasukLembur.value,
                      gantiWarna: _gantiWarna,
                      onTap: () => absenLemburOnPress(true),
                    )
                  : ButtonAbsen(
                      icon: FontAwesomeIcons.arrowRightToBracket,
                      subtitle: "Clock In",
                      onTap: () => absenLemburOnPress(true),
                    ),
            ),
            const SizedBox(
              width: 10,
            ),
            Expanded(
              child: absenLemburBerhasil.value == true &&
                      jamKeluarLembur.value != "Clock Out"
                  ? ButtonAbsen(
                      icon: FontAwesomeIcons.arrowRightFromBracket,
                      subtitle: jamKeluarLembur.value,
                      gantiWarna: _gantiWarna,
                      onTap: () => absenLemburOnPress(false),
                    )
                  : ButtonAbsen(
                      icon: FontAwesomeIcons.arrowRightFromBracket,
                      subtitle: "Clock Out",
                      onTap: () => absenLemburOnPress(false),
                    ),
            ),
          ],
        ),
      ),
      const SizedBox(
        height: 10,
      ),
      Obx(
        () => Visibility(
          visible: jamKeluarLembur.value != "Clock Out",
          child: Column(
            children: [
              InputTextComponent(
                label: "Keterangan Lembur",
                controller: keteranganLemburCon,
                required: false,
              ),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: MahasColors.primary,
                      ),
                      onPressed: () {
                        submitKeteranganLembur();
                      },
                      child: const Text('Submit'),
                    ),
                  ),
                ],
              ),
              const SizedBox(
                height: 10,
              ),
            ],
          ),
        ),
      ),
      SizedBox(
        height: 40,
        width: double.infinity,
        child: TextButton(
            onPressed: () {
              itemOnTab(dataAbsensiLembur.idPenugasanLembur!);
            },
            child: Text(
              "Lihat detail penugasan",
              textAlign: TextAlign.center,
              style: MahasThemes.link,
            )),
      ),
    ]);
  }

  Future<void> _determinePosition() async {
    bool serviceEnabled;
    LocationPermission permission;

    // Test if location services are enabled.
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return Future.error('Location services are disabled.');
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return Future.error('Location permissions are denied');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      return Future.error(
          'Location permissions are permanently denied, we cannot request permissions.');
    }
    await getCurrentLocation();
  }

  Future<void> getCurrentLocation() async {
    bool isGpsEnabled = await Geolocator.isLocationServiceEnabled();
    if (!isGpsEnabled) {
      EasyLoading.dismiss();
    }
    _position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(minutes: 1));
  }

  Future<bool> cekEnableGps() async {
    bool isGpsEnabled = await Geolocator.isLocationServiceEnabled();
    if (!isGpsEnabled) {
      EasyLoading.dismiss();
      var aktifkan = await Helper.dialogQuestion(
        message:
            "Aktifkan GPS anda terlebih dulu untuk fitur absensi, aktifkan?",
        textConfirm: 'Aktifkan',
        textCancel: 'Tidak',
      );
      if (aktifkan == true) {
        var r = await Geolocator.openLocationSettings();
        return r;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }

  void absenOnPress(bool masuk, {String? foto, String? namafile}) async {
    if (EasyLoading.isShow) return;
    EasyLoading.show();
    bool inRadius = await cekRadius();
    if (_position!.isMocked) {
      Helper.dialogWarning("Harap Matikan Mock Location Anda!");
    } else if (inRadius) {
      // ignore: prefer_typing_uninitialized_variables
      var body;
      String url;
      if (MahasConfig.profileMenu.absenwithfoto != true) {
        body = {
          "latitude": _position!.latitude,
          "longitude": _position!.longitude,
          "masuk": masuk,
          "id_Lokasi": selectedLocation?.id,
          "device": _device,
          "os": _os
        };
        url = "/api/absensi";
      } else {
        body = {
          "latitude": _position!.latitude,
          "longitude": _position!.longitude,
          "masuk": masuk,
          "device": _device,
          "os": _os,
          "fileName": namafile,
          "id_Lokasi": selectedLocation?.id,
          "id_Divisi": MahasConfig.selectedDivisi,
          "fileAbsensiPhoto": foto
        };
        url = "/api/absensi/foto";
      }

      var r = await HttpApi.post(
        url,
        body: body,
      );

      var rLog = HttpApi.post(
        "/api/absensi/log",
        body: {
          "latitude": _position!.latitude,
          "longitude": _position!.longitude,
          "masuk": masuk,
          "id_Lokasi": selectedLocation?.id,
          "device": _device,
          "os": _os,
          "berhasilAbsen": true,
        },
      );

      if (r.success) {
        lokasiAbsensi.value = true;

        if (EasyLoading.isShow) {
          EasyLoading.dismiss();
        }
        Helper.dialogSuccess(masuk == true
            ? "Absensi Datang Berhasil!\nTerimakasih"
            : "Absensi Pulang Berhasil!\nTerimakasih");
        if (masuk == true) {
          absensi.value = "Datang";
        } else if (masuk == false) {
          absensi.value = "Pulang";
        } else {
          absensi.value = "Libur";
        }
        await getAbsen(masuk.toString());
        if (MahasConfig.dinamisForm.cutiTahunan?.quotaVisible == true) {
          checkPenguranganQuotaCutiTahunan(masuk);
        }
      } else if (!r.success) {
        handleError(r);
        if (EasyLoading.isShow) {
          EasyLoading.dismiss();
        }
        return;
      }
    } else if (!inRadius) {
      var rLog = HttpApi.post(
        "/api/absensi/log",
        body: {
          "latitude": _position!.latitude,
          "longitude": _position!.longitude,
          "masuk": masuk,
          "id_Lokasi": null,
          "device": _device,
          "os": _os,
          "berhasilAbsen": false,
        },
      );
    }
    EasyLoading.dismiss();
  }

  void checkPenguranganQuotaCutiTahunan(bool masuk) async {
    DateTime now = DateTime.now();
    DateTime jamMulai = DateTime(
      now.year,
      now.month,
      now.day,
      int.parse(jamMulaiShift.value.split(':')[0]),
      int.parse(jamMulaiShift.value.split(':')[1]),
    );

    DateTime jamSelesai = DateTime(
      now.year,
      now.month,
      now.day,
      int.parse(jamSelesaiShift.value.split(':')[0]),
      int.parse(jamSelesaiShift.value.split(':')[1]),
    );

    DateTime jamMasuk = DateTime(
      now.year,
      now.month,
      now.day,
      int.parse(jammasuk.value.split(':')[0]),
      int.parse(jammasuk.value.split(':')[1]),
    );

    // Penanganan shift malam (jam selesai lebih kecil dari jam mulai)
    if (jamSelesai.isBefore(jamMulai)) {
      jamSelesai = jamSelesai.add(const Duration(days: 1));
    }

    // Hitung selisih waktu
    Duration selisihMasuk = jamMulai.difference(now);
    Duration selisihKerja = now.difference(jamMasuk);

    if (masuk == true) {
      if (selisihMasuk.inMinutes < -240) {
        var pengurangan = await penguranganQuotaCutiTahunan();
        if (pengurangan == true) {
          Helper.dialogWarning(
              "Anda terlambat lebih dari 4 jam dari jam mulai shift! Quota Cuti Tahunan Anda akan dikurangi!");
          return;
        }
      }
    } else if (masuk == false) {
      if (selisihMasuk.inMinutes > -240 && selisihKerja.inMinutes < 240) {
        var pengurangan = await penguranganQuotaCutiTahunan();
        if (pengurangan == true) {
          Helper.dialogWarning(
              "Anda belum bekerja minimal 4 jam! Quota Cuti Tahunan Anda akan dikurangi!");
        }
        return;
      }
    }
  }

  Future<bool> penguranganQuotaCutiTahunan() async {
    var r = await HttpApi.post(
      "/api/PermintaanJadwal/CutiTahunan/PenguranganQuotaCutiTahunan",
    );
    if (r.success) {
      return true;
    } else {
      return false;
    }
  }

  void absenLemburOnPress(bool masuk) async {
    if (EasyLoading.isShow) return;
    EasyLoading.show();
    bool inRadius = await cekRadius();
    if (_position!.isMocked) {
      Helper.dialogWarning("Harap Matikan Mock Location Anda!");
    } else if (inRadius) {
      var r = await HttpApi.post(
        "/api/absensi/lembur",
        body: {
          "id_PenugasanLembur": dataAbsensiLembur.idPenugasanLembur,
          "latitude": _position!.latitude,
          "longitude": _position!.longitude,
          "masuk": masuk,
          "device": _device,
          "os": _os
        },
      );
      if (r.success) {
        lokasiAbsensi.value = true;

        if (EasyLoading.isShow) {
          EasyLoading.dismiss();
        }
        Helper.dialogSuccess(masuk == true
            ? "Absensi Lembur Datang Berhasil!"
            : "Absensi Lembur Pulang Berhasil!");
        if (masuk == true) {
          absensi.value = "Datang";
        } else if (masuk == false) {
          absensi.value = "Pulang";
        } else {
          absensi.value = "Libur";
        }
        getDataAbsenLembur(masuk.toString());
      }
      if (!r.success) {
        handleError(r);
        return;
      }
    }
    EasyLoading.dismiss();
  }

  void absenWithPhotoOnPress(bool masuk) async {
    if (EasyLoading.isShow) return;
    try {
      XFile? pickedFile = await ImagePicker().pickImage(
        source: ImageSource.camera,
        imageQuality: 30,
      );
      if (pickedFile != null) {
        final path = pickedFile.path;
        Uint8List imageBytes = await pickedFile.readAsBytes();
        String image = base64Encode(imageBytes);
        if (path.contains('.jpg')) {
          Get.dialog(
            AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(MahasThemes.borderRadius),
                ),
              ),
              clipBehavior: Clip.antiAlias,
              contentPadding: const EdgeInsets.only(
                  bottom: 0, top: 15, right: 15, left: 15),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Center(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(5),
                      child: Image.file(File(path)),
                    ),
                  ),
                ],
              ),
              actionsAlignment: MainAxisAlignment.spaceAround,
              actions: [
                TextButton(
                  style: TextButton.styleFrom(minimumSize: const Size(30, 12)),
                  onPressed: () {
                    Get.back();
                  },
                  child: const Text("Cancel"),
                ),
                TextButton(
                  style: TextButton.styleFrom(minimumSize: const Size(30, 12)),
                  onPressed: () {
                    absenOnPress(
                      masuk,
                      foto: image,
                      namafile: pickedFile.name,
                    );
                    Get.back();
                  },
                  child: const Text("Absen"),
                ),
              ],
            ),
          );
        }
      }
      // ignore: empty_catches
    } catch (e) {}
    EasyLoading.dismiss();
  }

  Future<bool> getShift() async {
    final url =
        "/api/Absensi/ShiftHariIni?Id_Divisi=${MahasConfig.selectedDivisi}";
    final r = await HttpApi.get(url);
    if (r.success) {
      DateTime currentTime = DateTime.now();

      hasDataAbsen.value = true;
      ShifthariiniModel shiftModel = ShifthariiniModel.fromJson(r.body);
      if (shiftModel.shiftke3 == true &&
          isTimeInShift(
              currentTime,
              MahasFormat.displayTime(shiftModel.jammulaike3),
              MahasFormat.displayTime(shiftModel.jamselesaike3))) {
        shift.value = "${shiftModel.nama!} (Shift 3)";
        jamMulaiShift.value = MahasFormat.displayTime(shiftModel.jammulaike3);
        jamSelesaiShift.value =
            MahasFormat.displayTime(shiftModel.jamselesaike3);
      } else if (shiftModel.shiftke2 == true &&
          isTimeInShift(
              currentTime,
              MahasFormat.displayTime(shiftModel.jammulaike2),
              MahasFormat.displayTime(shiftModel.jamselesaike2))) {
        shift.value = "${shiftModel.nama!} (Shift 2)";
        jamMulaiShift.value = MahasFormat.displayTime(shiftModel.jammulaike2);
        jamSelesaiShift.value =
            MahasFormat.displayTime(shiftModel.jamselesaike2);
      } else {
        shift.value = shiftModel.nama!;
        jamMulaiShift.value = MahasFormat.displayTime(shiftModel.jammulai);
        jamSelesaiShift.value = MahasFormat.displayTime(shiftModel.jamselesai);
      }
      return true;
    } else if (hasDataAbsen.value = false) {
      Helper.dialogWarning(
          r.message!.isEmpty ? "Anda belum memiliki jadwal!" : r.message);
      return false;
    } else if (r.statusCode == 404) {
      Helper.dialogWarning(
          "Tidak ada shift hari ini atau Anda tidak memiliki jadwal!");
      return false;
    } else {
      Helper.dialogWarning(r.message);
      return false;
    }
  }

  bool isTimeInShift(DateTime currentTime, String shiftStart, String shiftEnd) {
    TimeOfDay currentTimeOfDay = TimeOfDay.fromDateTime(currentTime);

    TimeOfDay shiftStartTime = TimeOfDay(
      hour: int.parse(shiftStart.split(":")[0]),
      minute: int.parse(shiftStart.split(":")[1]),
    );

    TimeOfDay shiftEndTime = TimeOfDay(
      hour: int.parse(shiftEnd.split(":")[0]),
      minute: int.parse(shiftEnd.split(":")[1]),
    );

    return isTimeOfDayInRange(currentTimeOfDay, shiftStartTime, shiftEndTime);
  }

  bool isTimeOfDayInRange(TimeOfDay target, TimeOfDay start, TimeOfDay end) {
    final int targetMinutes = target.hour * 60 + target.minute;
    final int startMinutes = start.hour * 60 + start.minute;
    final int endMinutes = end.hour * 60 + end.minute;

    if (startMinutes <= endMinutes) {
      // Shift doesn't cross midnight
      return targetMinutes >= startMinutes && targetMinutes < endMinutes;
    } else {
      // Shift crosses midnight
      return targetMinutes >= startMinutes || targetMinutes < endMinutes;
    }
  }

  getTime(startTime, endTime) {
    bool result = false;
    int startTimeInt = (startTime.hour * 60 + startTime.minute) * 60;
    int endTimeInt = (endTime.hour * 60 + endTime.minute) * 60;
    if (endTimeInt >= startTimeInt) {
      result = true;
    } else {
      result = false;
    }
    return result;
  }

  Future cekNotif() async {
    var r = await HttpApi.get('/api/Notifikasi');
    if (r.success) {
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      List<NotifikasiModel> list =
          model.datas!.map((e) => NotifikasiModel.fromDynamic(e)).toList();

      bool hasNotif = false;
      var j = 0;
      for (var i in list) {
        if (hasNotif == false) {
          if (i.dibaca == false) {
            notifikasi.value = true;
            hasNotif = true;
          }
        }
      }
    }
    if (!r.success) {
      handleError(r);
      return;
    }
  }

  void notifCreateJadwal() {
    if (DateTime.now().day ==
            MahasConfig.dinamisForm.notifikasi?.tanggalNotifJadwal &&
        (MahasConfig.atasan.isKadiv == true ||
            MahasConfig.atasan.isManager == true)) {
      Helper.dialogIcon(
        title: "Create Jadwal",
        message:
            "Jangan Lupa Untuk Membuatkan Jadwal dan Melakukan Finalisasi Jadwal Di Divisi Anda",
        icon: FontAwesomeIcons.calendarPlus,
      );
    }
  }

  void cekJadwalWithNotif() async {
    if (Platform.isAndroid) {
      if (await Permission.scheduleExactAlarm.isDenied) {
        Helper.dialogQuestion(
          message: "Izinkan aplikasi untuk mengirim notifikasi absensi?",
          textConfirm: "Izinkan",
          textCancel: "Tidak",
          confirmAction: () async {
            await Permission.scheduleExactAlarm.request();
          },
        );
      }
    }
    var r = await HttpApi.get('/api/Absensi/Jadwal');
    if (!r.success) {
      handleError(r);
      return;
    }

    AbsenJadwal absenJadwal = absenJadwalFromJson(r.body);
    List<Jadwal> jadwalBulanIni = absenJadwal.jadwal?.where((jadwal) {
          var now = DateTime.now();
          var nextMonth = now.month == 12 ? 1 : now.month + 1;
          var nextYear = now.month == 12 ? now.year + 1 : now.year;

          return (jadwal.tanggal!.month == now.month &&
                  jadwal.tanggal!.year == now.year) ||
              (jadwal.tanggal!.month == nextMonth &&
                  jadwal.tanggal!.year == nextYear);
        }).toList() ??
        [];

    if (jadwalBulanIni.isEmpty) return;

    var notificationService = LocalNotificationService();
    notificationService.cancelAllNotifications();

    for (var jadwal in jadwalBulanIni) {
      var tanggal = DateTime(
          DateTime.now().year, jadwal.tanggal!.month, jadwal.tanggal!.day);

      List<Map<String, dynamic>> waktuAbsensi = [
        {
          "mulai": jadwal.jamMulai,
          "idMulai": 0,
          "selesai": jadwal.jamSelesai,
          "idSelesai": 1
        },
        {
          "mulai": jadwal.jamMulaiKe2,
          "idMulai": 2,
          "selesai": jadwal.jamSelesaiKe2,
          "idSelesai": 3
        },
        {
          "mulai": jadwal.jamMulaiKe3,
          "idMulai": 2,
          "selesai": jadwal.jamSelesaiKe3,
          "idSelesai": 3
        },
      ];

      for (var waktu in waktuAbsensi) {
        if (waktu["mulai"] != null) {
          scheduleNotification(
              notificationService, waktu["idMulai"], tanggal, waktu["mulai"]!);
        }
        if (waktu["selesai"] != null) {
          var tanggalSelesai = tanggal;
          if (waktu["selesai"]!.hour < waktu["mulai"]!.hour ||
              (waktu["selesai"]!.hour == waktu["mulai"]!.hour &&
                  waktu["selesai"]!.minute < waktu["mulai"]!.minute)) {
            tanggalSelesai = tanggal.add(const Duration(days: 1));
          }
          scheduleNotification(notificationService, waktu["idSelesai"],
              tanggalSelesai, waktu["selesai"]!);
        }
      }
    }
  }

  void scheduleNotification(LocalNotificationService service, int id,
      DateTime tanggal, TimeOfDay waktu) {
    var jadwalNotif =
        tanggal.add(Duration(hours: waktu.hour, minutes: waktu.minute - 30));
    service.dateNotifications(nextInstanceOfTime(jadwalNotif), id);
  }

  tz.TZDateTime nextInstanceOfTime(DateTime theTime) {
    var location = tz.getLocation('Asia/Singapore');
    final now = tz.TZDateTime.from(theTime, location);
    // final now = tz.TZDateTime.now(location);
    tz.TZDateTime scheduledDate = tz.TZDateTime(location, now.year, now.month,
        now.day, now.hour, now.minute, now.second);
    // if (scheduledDate.isBefore(now)) {
    //   scheduledDate = scheduledDate.add(const Duration(days: 1));
    // }
    return scheduledDate;
  }

  Future<void> getAbsen(String masuk) async {
    var r = await HttpApi.get('/api/Absensi');
    if (r.success) {
      List<dynamic> datas = json.decode(r.body);
      if (listAbsensi.isNotEmpty) {
        listAbsensi.clear();
      }
      for (var obj in datas) {
        listAbsensi.add(Absen.fromJson(obj));
      }
      for (var i = 0; i < listAbsensi.length; i++) {
        shift.value = listAbsensi[i].lokasiAbsensi!;
        if (masuk == "true") {
          if (listAbsensi[i].status == "Masuk") {
            absenBerhasil.value = true;
            jammasuk.value =
                DateFormat("HH:mm").format(listAbsensi[i].tanggalJam!);
            jamMasukAbsensi = null;
          }
        } else if (masuk == "false") {
          if (listAbsensi[i].status == "Keluar") {
            absenBerhasil.value = true;
            jamkeluar.value =
                DateFormat("HH:mm").format(listAbsensi[i].tanggalJam!);
            jamKeluarAbsensi = null;
          }
        } else {
          if (listAbsensi[i].status == "Keluar") {
            absenBerhasil.value = true;
            jamKeluarAbsensi = listAbsensi[i].tanggalJam;
          } else if (listAbsensi[i].status == "Masuk") {
            absenBerhasil.value = true;
            jamMasukAbsensi = listAbsensi[i].tanggalJam;
          }
        }
      }
      getAbsenFaceRecognition();
    }
    if (!r.success) {
      handleError(r);
      return;
    }
  }

  Future<void> getDataAbsenLembur(String masuk) async {
    var r = await HttpApi.get('/api/Absensi/Lembur');
    if (r.success) {
      List<dynamic> datas = json.decode(r.body);
      if (listAbsensiLembur.isNotEmpty) {
        listAbsensiLembur.clear();
      }
      for (var obj in datas) {
        listAbsensiLembur.add(Absen.fromJson(obj));
      }
      for (var i = 0; i < listAbsensiLembur.length; i++) {
        shift.value = listAbsensiLembur[i].lokasiAbsensi!;
        if (masuk == "true") {
          if (listAbsensiLembur[i].status == "Masuk") {
            absenLemburBerhasil.value = true;
            jamMasukLembur.value =
                DateFormat("HH:mm").format(listAbsensiLembur[i].tanggalJam!);
            jamMasukAbsensiLembur = null;
          }
        } else if (masuk == "false") {
          if (listAbsensiLembur[i].status == "Keluar") {
            absenLemburBerhasil.value = true;
            jamKeluarLembur.value =
                DateFormat("HH:mm").format(listAbsensiLembur[i].tanggalJam!);
            jamKeluarAbsensiLembur = null;
          }
        } else {
          if (listAbsensiLembur[i].status == "Keluar") {
            absenLemburBerhasil.value = true;
            jamKeluarAbsensiLembur = listAbsensiLembur[i].tanggalJam;
            jamKeluarLembur.value =
                DateFormat("HH:mm").format(listAbsensiLembur[i].tanggalJam!);
          } else if (listAbsensiLembur[i].status == "Masuk") {
            absenLemburBerhasil.value = true;
            jamMasukAbsensiLembur = listAbsensiLembur[i].tanggalJam;
            jamMasukLembur.value =
                DateFormat("HH:mm").format(listAbsensiLembur[i].tanggalJam!);
          }
        }
      }
    }
    if (!r.success) {
      handleError(r);
      return;
    }
  }

  Future<void> getDataAbsensiIstirahat(String masuk) async {
    var r = await HttpApi.get('/api/Absensi/Istirahat');
    if (r.success) {
      List<dynamic> datas = json.decode(r.body);
      if (listAbsensiIstirahat.isNotEmpty) {
        listAbsensiIstirahat.clear();
      }
      for (var obj in datas) {
        listAbsensiIstirahat.add(Absen.fromJson(obj));
      }
      for (var i = 0; i < listAbsensiIstirahat.length; i++) {
        shift.value = listAbsensiIstirahat[i].lokasiAbsensi!;
        if (masuk == "true") {
          if (listAbsensiIstirahat[i].status == "Masuk") {
            absenIstirahatMasukBerhasil.value = true;
            jamMasukIstirahat.value =
                DateFormat("HH:mm").format(listAbsensiIstirahat[i].tanggalJam!);
          }
        } else if (masuk == "false") {
          if (listAbsensiIstirahat[i].status == "Keluar") {
            absenIstirahatKeluarBerhasil.value = true;
            jamKeluarIstirahat.value =
                DateFormat("HH:mm").format(listAbsensiIstirahat[i].tanggalJam!);
          }
        } else {
          if (listAbsensiIstirahat[i].status == "Keluar") {
            absenIstirahatKeluarBerhasil.value = true;
            jamKeluarIstirahat.value =
                DateFormat("HH:mm").format(listAbsensiIstirahat[i].tanggalJam!);
          } else if (listAbsensiIstirahat[i].status == "Masuk") {
            absenIstirahatMasukBerhasil.value = true;
            jamMasukIstirahat.value =
                DateFormat("HH:mm").format(listAbsensiIstirahat[i].tanggalJam!);
          }
        }
      }
    }
    if (!r.success) {
      return;
    }
  }

  void getAbsenFaceRecognition() async {
    var r = await HttpApi.get(
        '/api/Absensi/FaceRecognition?pageIndex=0&pageSize=15');
    if (r.success) {
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      if (listAbsensiFaceRecognition.isNotEmpty) {
        listAbsensiFaceRecognition.clear();
      }
      var listReversed = model.datas!.reversed.toList();
      for (var e in (listReversed)) {
        listAbsensiFaceRecognition
            .add(AbsensiFacereCognitionModel.fromDynamic(e));
      }
      for (var i = 0; i < listAbsensiFaceRecognition.length; i++) {
        if (listAbsensiFaceRecognition[i].status == "Keluar") {
          absenBerhasil.value = true;
          jamKeluarAbsensiFaceRecognition =
              listAbsensiFaceRecognition[i].tanggaljam;
        } else if (listAbsensiFaceRecognition[i].status == "Masuk") {
          absenBerhasil.value = true;
          jamMasukAbsensiFaceRecognition =
              listAbsensiFaceRecognition[i].tanggaljam;
        }
      }
    }
    perbandinganAbsen();
  }

  Future<void> getAbsenLembur() async {
    var r = await HttpApi.get('/api/PenugasanLembur/PegawaiAbsensi');
    if (r.success) {
      dataAbsensiLembur = AbsensiLemburModel.fromJson(r.body);
      if (dataAbsensiLembur.idPenugasanLembur != null) {
        hasDataAbsenLembur.value = true;
        getDataAbsenLembur("");
      } else {
        hasDataAbsenLembur.value = false;
        Helper.dialogWarning("Tidak ada data absensi lembur");
      }
    } else {
      hasDataAbsenLembur.value = false;
      Helper.dialogWarning("Tidak ada data absensi lembur");
    }
  }

  void absensiIstirahatOnPress(bool masuk) async {
    if (EasyLoading.isShow) return;
    EasyLoading.show();
    bool inRadius = await cekRadius();
    if (_position!.isMocked) {
      Helper.dialogWarning("Harap Matikan Mock Location Anda!");
    } else if (inRadius) {
      var r = await HttpApi.post(
        "/api/absensi/istirahat",
        body: {
          "latitude": _position!.latitude,
          "longitude": _position!.longitude,
          "masuk": masuk,
          "device": _device,
          "os": _os
        },
      );
      if (r.success) {
        if (EasyLoading.isShow) {
          EasyLoading.dismiss();
        }
        Helper.dialogSuccess(masuk == true
            ? "Absensi Mulai Istirahat Berhasil!"
            : "Absensi Selesai Istirahat Berhasil!");
        if (masuk == true) {
          absensi.value = "Masuk";
        } else if (masuk == false) {
          absensi.value = "Keluar";
        }
        getDataAbsensiIstirahat(masuk.toString());
      }
      if (!r.success) {
        handleError(r);
        return;
      }
    }
    EasyLoading.dismiss();
  }

  void perbandinganAbsen() async {
    if (jamKeluarAbsensi != null || jamKeluarAbsensiFaceRecognition != null) {
      jamkeluar.value = DateFormat("HH:mm").format(
        (jamKeluarAbsensi != null &&
                (jamKeluarAbsensiFaceRecognition == null ||
                    jamKeluarAbsensi!
                        .isAfter(jamKeluarAbsensiFaceRecognition!)))
            ? jamKeluarAbsensi!
            : jamKeluarAbsensiFaceRecognition!,
      );
    }

    if (jamMasukAbsensi != null || jamMasukAbsensiFaceRecognition != null) {
      jammasuk.value = DateFormat("HH:mm").format(
        (jamMasukAbsensi != null &&
                (jamMasukAbsensiFaceRecognition == null ||
                    jamMasukAbsensi!.isAfter(jamMasukAbsensiFaceRecognition!)))
            ? jamMasukAbsensi!
            : jamMasukAbsensiFaceRecognition!,
      );
    }
  }

  void getFromGallery() async {
    XFile? pickedFile = await ImagePicker().pickImage(
      source: ImageSource.gallery,
      imageQuality: 30,
    );
    if (pickedFile != null) {
      imageFile.value = await croppedImage(image: pickedFile);
    }
  }

  Future<File?> croppedImage({required XFile image}) async {
    CroppedFile? croppedImage = await ImageCropper().cropImage(
      sourcePath: image.path,
      aspectRatio: const CropAspectRatio(ratioX: 1.0, ratioY: 1.0),
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: 'Cropper',
          toolbarColor: MahasColors.primary,
          toolbarWidgetColor: Colors.white,
          activeControlsWidgetColor: MahasColors.primary,
          statusBarColor: MahasColors.primary,
          initAspectRatio: CropAspectRatioPreset.original,
          lockAspectRatio: true,
          aspectRatioPresets: [
            CropAspectRatioPreset.square,
          ],
        ),
        IOSUiSettings(
          title: 'Cropper',
          aspectRatioPresets: [
            CropAspectRatioPreset.square,
          ],
        ),
      ],
    );
    if (croppedImage == null) return null;
    return File(croppedImage.path);
  }

  void handleError(ApiResultModel r) {
    if (r.message!.contains(RegExp(r'A network error|failed host lookup'))) {
      Helper.dialogWarning(
          "Tidak ada koneksi internet, coba beberapa saat lagi!");
    } else if (r.message!.contains("unexpected end of stream")) {
      Helper.dialogWarning(
          "Koneksi internet Anda terlalu lambat, coba beberapa saat lagi!");
    } else if (r.statusCode == 500) {
      Helper.dialogWarning("Tidak ada data");
    } else {
      Helper.dialogWarning(r.message);
    }
  }

  void saveFotoProfile() async {
    EasyLoading.show();
    Uint8List imageBytes = await imageFile.value!.readAsBytes();
    String image = base64Encode(imageBytes);
    var r = await HttpApi.put(
      '/api/Profile/FotoProfile',
      body: {
        "id_Divisi": MahasConfig.selectedDivisi,
        "fotoProfile": image,
      },
    );
    if (r.success) {
      var idHistory = json.decode(r.body)['id'];
      EasyLoading.dismiss();
      Get.back();
      Get.toNamed(
        Routes.PROFILE_FOTO_HISTORY_SETUP,
        parameters: {
          'id': idHistory.toString(),
        },
      );
      imageFile.value = null;
      await onRefresh();
    } else {
      EasyLoading.dismiss();
      Helper.dialogWarning("Gagal mengubah foto");
    }
  }

  Future dialogFoto() async {
    await Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(MahasThemes.borderRadius),
          ),
        ),
        clipBehavior: Clip.antiAlias,
        title: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton.icon(
              onPressed: () {
                // go to history;
                Get.back();
                imageFile.value = null;
                Get.toNamed(Routes.PROFILE_FOTO_HISTORY);
              },
              icon: Icon(Icons.info, color: MahasColors.primary),
              label: Text(
                "History",
                style: TextStyle(color: MahasColors.primary),
              ),
            ),
          ],
        ),
        titlePadding:
            const EdgeInsets.only(bottom: 0, top: 0, right: 10, left: 0),
        contentPadding:
            const EdgeInsets.only(bottom: 0, top: 0, right: 15, left: 15),
        // actionsPadding: const EdgeInsets.all(0),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Center(
              child: profile.value!.urlphoto != null
                  ? Obx(
                      () => ClipRRect(
                        borderRadius: BorderRadius.circular(5),
                        child: imageFile.value != null
                            ? Image.file(imageFile.value!)
                            : Image.memory(
                                images.value!,
                              ),
                      ),
                    )
                  : Obx(
                      () => ClipRRect(
                        borderRadius: BorderRadius.circular(5),
                        child: imageFile.value != null
                            ? Image.file(imageFile.value!)
                            : Image.asset(EnvironmentConstant.imageLogo),
                      ),
                    ),
            ),
          ],
        ),
        actionsAlignment: MainAxisAlignment.spaceAround,
        actions: [
          TextButton(
            style: TextButton.styleFrom(minimumSize: const Size(30, 12)),
            onPressed: () {
              if (imageFile.value == null) {
                Helper.dialogWarning("Anda belum memilih foto");
              } else {
                saveFotoProfile();
              }
            },
            child: const Text("Save"),
          ),
          TextButton(
            style: TextButton.styleFrom(minimumSize: const Size(30, 12)),
            onPressed: () {
              getFromGallery();
            },
            child: const Text("Edit"),
          ),
          TextButton(
            style: TextButton.styleFrom(minimumSize: const Size(30, 12)),
            onPressed: () {
              Get.back();
              imageFile.value = null;
            },
            child: const Text("Cancel"),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  void submitKeteranganLembur() async {
    EasyLoading.show();
    var r = await HttpApi.put(
      '/api/PenugasanLembur/KeteranganLembur/${dataAbsensiLembur.idPenugasanLembur}',
      body: {
        "keteranganLembur": keteranganLemburCon.value,
      },
    );
    if (r.success) {
      EasyLoading.dismiss();
      keteranganLemburCon.value = '';
      Get.back();
      Get.snackbar(
        "Success",
        "Keterangan Lembur berhasil disubmit. Notifikasi telah dikirimkan ke Kepala Unit untuk persetujuan",
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.white,
        duration: const Duration(
          seconds: 5,
        ),
      );
    } else {
      EasyLoading.dismiss();
      Get.snackbar(
        "Failed",
        "Keterangan Lembur gagal disubmit.",
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.white,
        duration: const Duration(
          seconds: 5,
        ),
      );
    }
  }

  void cekCutiSetengahHari() async {
    var r = await HttpApi.post(
        '/api/PermintaanJadwal/CutiSetengahHari/PenguranganQuotaCuti');
  }

  void goToProfile() async {
    await InternetConnectionService.isInternet();
    if (MahasConfig.hasInternet == true) {
      Get.toNamed(Routes.PROFILE)!.then((value) => {
            cekNotif(),
          });
    } else {
      Helper.dialogWarning(
          "Tidak dapat melihat profile karena tidak ada koneksi internet");
    }
  }

  void goToJadwa() async {
    await InternetConnectionService.isInternet();
    Get.toNamed(Routes.JADWAL)!.then((value) => {
          cekNotif(),
        });
  }

  void goToETicket() async {
    await InternetConnectionService.isInternet();
    if (MahasConfig.hasInternet == true) {
      Get.toNamed(Routes.ETICKET_LIST)!.then((value) => {
            cekNotif(),
          });
    } else {
      Helper.dialogWarning(
          "Tidak dapat melihat E-Ticket karena tidak ada koneksi internet");
    }
  }

  void goToPermintaanJadwal() async {
    await InternetConnectionService.isInternet();
    if (MahasConfig.hasInternet == true) {
      Get.toNamed(Routes.PERMINTAAN_JADWAL)!.then((value) => {
            cekNotif(),
          });
    } else {
      Helper.dialogWarning(
          "Tidak dapat melihat Permintaan Jadwal karena tidak ada koneksi internet");
    }
  }

  void goToInformasi() async {
    await InternetConnectionService.isInternet();
    if (MahasConfig.hasInternet == true) {
      Get.toNamed(Routes.INFORMASI)!.then((value) => {
            cekNotif(),
          });
    } else {
      Helper.dialogWarning(
          "Tidak dapat melihat Informasi karena tidak ada koneksi internet");
    }
  }

  void goToNotifikasi() async {
    await InternetConnectionService.isInternet();
    if (MahasConfig.hasInternet == true) {
      Get.toNamed(Routes.NOTIFIKASI)!.then((value) => {
            cekNotif(),
          });
    } else {
      Helper.dialogWarning(
          "Tidak dapat melihat Notifikasi karena tidak ada koneksi internet");
    }
  }

  void notifikasiPermission() {
    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
        FlutterLocalNotificationsPlugin();
    flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.requestNotificationsPermission();
  }
}

class ButtonAbsen extends StatelessWidget {
  final IconData icon;
  final GestureTapCallback? onTap;
  final String subtitle;
  final Color? gantiWarna;
  final double? fontSize;

  const ButtonAbsen({
    super.key,
    required this.icon,
    required this.subtitle,
    this.onTap,
    this.gantiWarna,
    this.fontSize,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(MahasThemes.borderRadius),
        color: gantiWarna ?? MahasColors.primary,
      ),
      child: InkWell(
        onTap: onTap,
        child: Column(
          children: [
            Icon(
              icon,
              color: MahasColors.light,
            ),
            const SizedBox(
              height: 5,
            ),
            Text(
              subtitle,
              style: TextStyle(color: Colors.white, fontSize: fontSize ?? 16),
            ),
          ],
        ),
      ),
    );
  }
}
