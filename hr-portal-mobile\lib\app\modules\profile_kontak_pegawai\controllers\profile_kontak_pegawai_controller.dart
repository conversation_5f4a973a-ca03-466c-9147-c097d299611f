import 'package:get/get.dart';
import '../../../mahas/components/others/list_component.dart';
import '../../../models/profile_kontak_model.dart';
import '../../../routes/app_pages.dart';

class ProfileKontakPegawaiController extends GetxController {
  final listCon = ListComponentController<ProfilekontakModel>(
    urlApi: (index, filter) => '/api/PegawaiJenisKontak/List?pageIndex=$index',
    fromDynamic: ProfilekontakModel.fromDynamic,
    allowSearch: false,
  );

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PROFILE_KONTAK_PEGAWAI_SETUP,
      parameters: {
        'id': id.toString(),
      },
    )?.then((value) {
      listCon.refresh();
    });
  }

  void popupMenuButtonOnSelected(String v) async {
    if (v == 'Tambah') {
      Get.toNamed(Routes.PROFILE_KONTAK_PEGAWAI_SETUP)?.then(
        (value) {
          listCon.refresh();
        },
      );
    } else if (v == 'History') {
      Get.toNamed(
        Routes.PROFILE_KONTAK_PEGAWAI_HISTORY,
        parameters: {
          'showStatus': true.toString(),
        },
      )?.then(
        (value) {
          listCon.refresh();
        },
      );
    }
  }
}
