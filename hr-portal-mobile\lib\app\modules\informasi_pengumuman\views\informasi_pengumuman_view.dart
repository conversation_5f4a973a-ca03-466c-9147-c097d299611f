import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../models/pengumuman_model.dart';
import '../controllers/informasi_pengumuman_controller.dart';

class InformasiPengumumanView extends GetView<InformasiPengumumanController> {
  const InformasiPengumumanView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Pengumuman'),
        centerTitle: true,
      ),
      body: ListComponent(
        controller: controller.listCon,
        itemBuilder: (PengumumanModel e) {
          return InkWell(
            onTap: () => controller.itemOnTab(e.id!),
            child: Padding(
              padding:
                  const EdgeInsets.only(left: 12, right: 12, top: 8, bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 5),
                        Text(e.judul ?? ""),
                        const SizedBox(height: 5),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
