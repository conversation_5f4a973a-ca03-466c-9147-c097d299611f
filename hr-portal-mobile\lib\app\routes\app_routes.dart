// ignore_for_file: constant_identifier_names

part of 'app_pages.dart';
// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

abstract class Routes {
  Routes._();
  static const HOME = _Paths.HOME;
  static const LOGIN = _Paths.LOGIN;
  static const PROFILE = _Paths.PROFILE;
  static const UNAUTHORIZED = _Paths.UNAUTHORIZED;
  static const SPLASH_SCREEN = _Paths.SPLASH_SCREEN;
  static const JADWAL = _Paths.JADWAL;
  static const DATA_ABSEN = _Paths.DATA_ABSEN;
  static const PERMINTAAN_JADWAL = _Paths.PERMINTAAN_JADWAL;
  static const PERMINTAAN_JADWAL_TUKAR_JADWAL =
      _Paths.PERMINTAAN_JADWAL_TUKAR_JADWAL;
  static const PERMINTAAN_JADWAL_LEMBUR = _Paths.PERMINTAAN_JADWAL_LEMBUR;
  static const PERMINTAAN_JADWAL_CUTI_HAMIL =
      _Paths.PERMINTAAN_JADWAL_CUTI_HAMIL;
  static const PERMINTAAN_JADWAL_CUTI_SAKIT =
      _Paths.PERMINTAAN_JADWAL_CUTI_SAKIT;
  static const PERMINTAAN_JADWAL_CUTI_TAHUNAN =
      _Paths.PERMINTAAN_JADWAL_CUTI_TAHUNAN;
  static const PERMINTAAN_JADWAL_CUTI_TUKAR_LEMBUR =
      _Paths.PERMINTAAN_JADWAL_CUTI_TUKAR_LEMBUR;
  static const PERMINTAAN_JADWAL_IZIN = _Paths.PERMINTAAN_JADWAL_IZIN;
  static const INFORMASI = _Paths.INFORMASI;
  static const INFORMASI_QUOTA_CUTI_TAHUNAN =
      _Paths.INFORMASI_QUOTA_CUTI_TAHUNAN;
  static const NOTIFIKASI = _Paths.NOTIFIKASI;
  static const NOTIFIKASI_DETAIL = _Paths.NOTIFIKASI_DETAIL;
  static const PROFILE_DATA_PRIBADI = _Paths.PROFILE_DATA_PRIBADI;
  static const PROFILE_DIVISI = _Paths.PROFILE_DIVISI;
  static const APPROVAL = _Paths.APPROVAL;
  static const PERMINTAAN_JADWAL_CUTI_HAMIL_SETUP =
      _Paths.PERMINTAAN_JADWAL_CUTI_HAMIL_SETUP;
  static const PERMINTAAN_JADWAL_IZIN_SETUP =
      _Paths.PERMINTAAN_JADWAL_IZIN_SETUP;
  static const PERMINTAAN_JADWAL_CUTI_SAKIT_SETUP =
      _Paths.PERMINTAAN_JADWAL_CUTI_SAKIT_SETUP;
  static const PERMINTAAN_JADWAL_CUTI_TUKAR_LEMBUR_SETUP =
      _Paths.PERMINTAAN_JADWAL_CUTI_TUKAR_LEMBUR_SETUP;
  static const PERMINTAAN_JADWAL_LEMBUR_SETUP =
      _Paths.PERMINTAAN_JADWAL_LEMBUR_SETUP;
  static const PERMINTAAN_JADWAL_CUTI_TAHUNAN_SETUP =
      _Paths.PERMINTAAN_JADWAL_CUTI_TAHUNAN_SETUP;
  static const PERMINTAAN_JADWAL_TUKAR_JADWAL_SETUP =
      _Paths.PERMINTAAN_JADWAL_TUKAR_JADWAL_SETUP;
  static const INFORMASI_QUOTA_TUKAR_LEMBUR =
      _Paths.INFORMASI_QUOTA_TUKAR_LEMBUR;
  static const INFORMASI_ABSENSI_SESUAI_DIVISI =
      _Paths.INFORMASI_ABSENSI_SESUAI_DIVISI;
  static const INFORMASI_LEMBUR_GANTI_UANG = _Paths.INFORMASI_LEMBUR_GANTI_UANG;
  static const ABSEN_SURAT_TUGAS = _Paths.ABSEN_SURAT_TUGAS;
  static const ABSEN_SURAT_TUGAS_SETUP = _Paths.ABSEN_SURAT_TUGAS_SETUP;
  static const DATA_ABSENSI_LIST = _Paths.DATA_ABSENSI_LIST;
  static const ETICKET = _Paths.ETICKET;
  static const ETICKET_LIST = _Paths.ETICKET_LIST;
  static const GAJI = _Paths.GAJI;
  static const GAJI_SETUP = _Paths.GAJI_SETUP;
  static const PROFILE_JENIS_SERTIFIKASI = _Paths.PROFILE_JENIS_SERTIFIKASI;
  static const PROFILE_KELUARGA = _Paths.PROFILE_KELUARGA;
  static const PROFILE_RIWAYAT_KESEHATAN = _Paths.PROFILE_RIWAYAT_KESEHATAN;
  static const PROFILE_HOBI = _Paths.PROFILE_HOBI;
  static const PROFILE_PENGALAMAN = _Paths.PROFILE_PENGALAMAN;
  static const PROFILE_REKENING = _Paths.PROFILE_REKENING;
  static const PROFILE_RIWAYAT_PENDIDIKAN = _Paths.PROFILE_RIWAYAT_PENDIDIKAN;
  static const PROFILE_JENIS_SERTIFIKASI_SETUP =
      _Paths.PROFILE_JENIS_SERTIFIKASI_SETUP;
  static const PROFILE_KELUARGA_SETUP = _Paths.PROFILE_KELUARGA_SETUP;
  static const PROFILE_RIWAYAT_KESEHATAN_SETUP =
      _Paths.PROFILE_RIWAYAT_KESEHATAN_SETUP;
  static const PROFILE_HOBI_SETUP = _Paths.PROFILE_HOBI_SETUP;
  static const PROFILE_KONTAK_PEGAWAI = _Paths.PROFILE_KONTAK_PEGAWAI;
  static const PROFILE_KONTAK_PEGAWAI_SETUP =
      _Paths.PROFILE_KONTAK_PEGAWAI_SETUP;
  static const PROFILE_RIWAYAT_PENDIDIKAN_SETUP =
      _Paths.PROFILE_RIWAYAT_PENDIDIKAN_SETUP;
  static const PROFILE_PENGALAMAN_SETUP = _Paths.PROFILE_PENGALAMAN_SETUP;
  static const PROFILE_REKENING_SETUP = _Paths.PROFILE_REKENING_SETUP;
  static const PROFILE_SERAGAM = _Paths.PROFILE_SERAGAM;
  static const PROFILE_SERAGAM_SETUP = _Paths.PROFILE_SERAGAM_SETUP;
  static const PROFILE_DOKUMEN = _Paths.PROFILE_DOKUMEN;
  static const PROFILE_DOKUMEN_SETUP = _Paths.PROFILE_DOKUMEN_SETUP;
  static const PROFILE_DATA_PRIBADI_HISTORY =
      _Paths.PROFILE_DATA_PRIBADI_HISTORY;
  static const PROFILE_KONTRAK = _Paths.PROFILE_KONTRAK;
  static const PROFILE_KONTRAK_SETUP = _Paths.PROFILE_KONTRAK_SETUP;
  static const PROFILE_KONTAK_PEGAWAI_HISTORY =
      _Paths.PROFILE_KONTAK_PEGAWAI_HISTORY;
  static const PROFILE_KONTAK_PEGAWAI_HISTORY_SETUP =
      _Paths.PROFILE_KONTAK_PEGAWAI_HISTORY_SETUP;
  static const PROFILE_KELUARGA_HISTORY = _Paths.PROFILE_KELUARGA_HISTORY;
  static const PROFILE_KELUARGA_HISTORY_SETUP =
      _Paths.PROFILE_KELUARGA_HISTORY_SETUP;
  static const PROFILE_RIWAYAT_KESEHATAN_HISTORY =
      _Paths.PROFILE_RIWAYAT_KESEHATAN_HISTORY;
  static const PROFILE_RIWAYAT_KESEHATAN_HISTORY_SETUP =
      _Paths.PROFILE_RIWAYAT_KESEHATAN_HISTORY_SETUP;
  static const PROFILE_PEGAWAI_STR = _Paths.PROFILE_PEGAWAI_STR;
  static const PROFILE_PEGAWAI_STR_SETUP = _Paths.PROFILE_PEGAWAI_STR_SETUP;
  static const PROFILE_PEGAWAI_STR_HISTORY = _Paths.PROFILE_PEGAWAI_STR_HISTORY;
  static const PROFILE_PEGAWAI_STR_HISTORY_SETUP =
      _Paths.PROFILE_PEGAWAI_STR_HISTORY_SETUP;
  static const PROFILE_REKENING_HISTORY = _Paths.PROFILE_REKENING_HISTORY;
  static const PROFILE_REKENING_HISTORY_SETUP =
      _Paths.PROFILE_REKENING_HISTORY_SETUP;
  static const PROFILE_JENIS_SERTIFIKASI_HISTORY =
      _Paths.PROFILE_JENIS_SERTIFIKASI_HISTORY;
  static const PROFILE_JENIS_SERTIFIKASI_HISTORY_SETUP =
      _Paths.PROFILE_JENIS_SERTIFIKASI_HISTORY_SETUP;
  static const PROFILE_FOTO_HISTORY = _Paths.PROFILE_FOTO_HISTORY;
  static const PROFILE_FOTO_HISTORY_SETUP = _Paths.PROFILE_FOTO_HISTORY_SETUP;
  static const PROFILE_RIWAYAT_PENDIDIKAN_HISTORY =
      _Paths.PROFILE_RIWAYAT_PENDIDIKAN_HISTORY;
  static const PROFILE_RIWAYAT_PENDIDIKAN_HISTORY_SETUP =
      _Paths.PROFILE_RIWAYAT_PENDIDIKAN_HISTORY_SETUP;
  static const PROFILE_PEGAWAI_KGB = _Paths.PROFILE_PEGAWAI_KGB;
  static const PROFILE_PEGAWAI_KGB_SETUP = _Paths.PROFILE_PEGAWAI_KGB_SETUP;
  static const PROFILE_PEGAWAI_PANGKAT = _Paths.PROFILE_PEGAWAI_PANGKAT;
  static const PROFILE_PEGAWAI_PANGKAT_SETUP =
      _Paths.PROFILE_PEGAWAI_PANGKAT_SETUP;
  static const ABSENSI_DETAIL = _Paths.ABSENSI_DETAIL;
  static const INFORMASI_PERPUS = _Paths.INFORMASI_PERPUS;
  static const INFORMASI_PERPUS_SETUP = _Paths.INFORMASI_PERPUS_SETUP;
  static const INFORMASI_SERAH_TERIMA = _Paths.INFORMASI_SERAH_TERIMA;
  static const INFORMASI_SERAH_TERIMA_MUTASI_BARANG =
      _Paths.INFORMASI_SERAH_TERIMA_MUTASI_BARANG;
  static const INFORMASI_SERAH_TERIMA_MUTASI_BARANG_SETUP =
      _Paths.INFORMASI_SERAH_TERIMA_MUTASI_BARANG_SETUP;
  static const QR_CODE_SCANNER = _Paths.QR_CODE_SCANNER;
  static const PROFILE_KATEGORIBIOS = _Paths.PROFILE_KATEGORIBIOS;
  static const PERMINTAAN_JADWAL_LEMBUR_CUSTOM_KEPALA_UNIT_SETUP =
      _Paths.PERMINTAAN_JADWAL_LEMBUR_CUSTOM_KEPALA_UNIT_SETUP;
  static const PERMINTAAN_JADWAL_LEMBUR_CUSTOM_PEGAWAI_SETUP =
      _Paths.PERMINTAAN_JADWAL_LEMBUR_CUSTOM_PEGAWAI_SETUP;
  static const INFORMASI_QUOTA_IZIN = _Paths.INFORMASI_QUOTA_IZIN;
  static const LOGIN_PASSWORD = _Paths.LOGIN_PASSWORD;
  static const PERMINTAAN_JADWAL_CUTI_DAY_PAYMENT =
      _Paths.PERMINTAAN_JADWAL_CUTI_DAY_PAYMENT;
  static const PERMINTAAN_JADWAL_CUTI_DAY_PAYMENT_SETUP =
      _Paths.PERMINTAAN_JADWAL_CUTI_DAY_PAYMENT_SETUP;
  static const PERMINTAAN_JADWAL_CUTI_UNPAID_LEAVE =
      _Paths.PERMINTAAN_JADWAL_CUTI_UNPAID_LEAVE;
  static const PERMINTAAN_JADWAL_CUTI_UNPAID_LEAVE_SETUP =
      _Paths.PERMINTAAN_JADWAL_CUTI_UNPAID_LEAVE_SETUP;
  static const PERMINTAAN_JADWAL_CUTI_SETENGAH_HARI =
      _Paths.PERMINTAAN_JADWAL_CUTI_SETENGAH_HARI;
  static const PERMINTAAN_JADWAL_CUTI_SETENGAH_HARI_SETUP =
      _Paths.PERMINTAAN_JADWAL_CUTI_SETENGAH_HARI_SETUP;
  static const INFORMASI_ABSENSI_JADWAL_ANGGOTA_DIVISI =
      _Paths.INFORMASI_ABSENSI_JADWAL_ANGGOTA_DIVISI;
  static const INFORMASI_PENGUMUMAN = _Paths.INFORMASI_PENGUMUMAN;
  static const INFORMASI_PENGUMUMAN_SETUP = _Paths.INFORMASI_PENGUMUMAN_SETUP;
  static const INFORMASI_PERINGATAN = _Paths.INFORMASI_PERINGATAN;
  static const INFORMASI_PERINGATAN_SETUP = _Paths.INFORMASI_PERINGATAN_SETUP;
}

abstract class _Paths {
  _Paths._();
  static const HOME = '/home';
  static const LOGIN = '/login';
  static const PROFILE = '/profile';
  static const UNAUTHORIZED = '/unauthorized';
  static const SPLASH_SCREEN = '/splash-screen';
  static const JADWAL = '/jadwal';
  static const DATA_ABSEN = '/data-absen';
  static const PERMINTAAN_JADWAL = '/permintaan-jadwal';
  static const PERMINTAAN_JADWAL_TUKAR_JADWAL =
      '/permintaan-jadwal-tukar-jadwal';
  static const PERMINTAAN_JADWAL_LEMBUR = '/permintaan-jadwal-lembur';
  static const PERMINTAAN_JADWAL_CUTI_HAMIL = '/permintaan-jadwal-cuti-hamil';
  static const PERMINTAAN_JADWAL_CUTI_SAKIT = '/permintaan-jadwal-cuti-sakit';
  static const PERMINTAAN_JADWAL_CUTI_TAHUNAN =
      '/permintaan-jadwal-cuti-tahunan';
  static const PERMINTAAN_JADWAL_CUTI_TUKAR_LEMBUR =
      '/permintaan-jadwal-cuti-tukar-lembur';
  static const PERMINTAAN_JADWAL_IZIN = '/permintaan-jadwal-izin';
  static const INFORMASI = '/informasi';
  static const INFORMASI_QUOTA_CUTI_TAHUNAN = '/informasi-quota-cuti-tahunan';
  static const NOTIFIKASI = '/notifikasi';
  static const NOTIFIKASI_DETAIL = '/notifikasi-detail';
  static const PROFILE_DATA_PRIBADI = '/profile-data-pribadi';
  static const PROFILE_DIVISI = '/profile-divisi';
  static const APPROVAL = '/approval';
  static const PERMINTAAN_JADWAL_CUTI_HAMIL_SETUP =
      '/permintaan-jadwal-cuti-hamil-setup';
  static const PERMINTAAN_JADWAL_IZIN_SETUP = '/permintaan-jadwal-izin-setup';
  static const PERMINTAAN_JADWAL_CUTI_SAKIT_SETUP =
      '/permintaan-jadwal-cuti-sakit-setup';
  static const PERMINTAAN_JADWAL_CUTI_TUKAR_LEMBUR_SETUP =
      '/permintaan-jadwal-cuti-tukar-lembur-setup';
  static const PERMINTAAN_JADWAL_LEMBUR_SETUP =
      '/permintaan-jadwal-lembur-setup';
  static const PERMINTAAN_JADWAL_CUTI_TAHUNAN_SETUP =
      '/permintaan-jadwal-cuti-tahunan-setup';
  static const PERMINTAAN_JADWAL_TUKAR_JADWAL_SETUP =
      '/permintaan-jadwal-tukar-jadwal-setup';
  static const INFORMASI_QUOTA_TUKAR_LEMBUR = '/informasi-quota-tukar-lembur';
  static const INFORMASI_ABSENSI_SESUAI_DIVISI =
      '/informasi-absensi-sesuai-divisi';
  static const INFORMASI_LEMBUR_GANTI_UANG = '/informasi-lembur-ganti-uang';
  static const ABSEN_SURAT_TUGAS = '/absen-surat-tugas';
  static const ABSEN_SURAT_TUGAS_SETUP = '/absen-surat-tugas-setup';
  static const DATA_ABSENSI_LIST = '/data-absensi-list';
  static const ETICKET = '/eticket';
  static const ETICKET_LIST = '/eticket-list';
  static const GAJI = '/gaji';
  static const GAJI_SETUP = '/gaji-setup';
  static const PROFILE_JENIS_SERTIFIKASI = '/profile-jenis-sertifikasi';
  static const PROFILE_KELUARGA = '/profile-keluarga';
  static const PROFILE_RIWAYAT_KESEHATAN = '/profile-riwayat-kesehatan';
  static const PROFILE_HOBI = '/profile-hobi';
  static const PROFILE_PENGALAMAN = '/profile-pengalaman';
  static const PROFILE_REKENING = '/profile-rekening';
  static const PROFILE_RIWAYAT_PENDIDIKAN = '/profile-riwayat-pendidikan';
  static const PROFILE_JENIS_SERTIFIKASI_SETUP =
      '/profile-jenis-sertifikasi-setup';
  static const PROFILE_KELUARGA_SETUP = '/profile-keluarga-setup';
  static const PROFILE_RIWAYAT_KESEHATAN_SETUP =
      '/profile-riwayat-kesehatan-setup';
  static const PROFILE_HOBI_SETUP = '/profile-hobi-setup';
  static const PROFILE_KONTAK_PEGAWAI = '/profile-kontak-pegawai';
  static const PROFILE_KONTAK_PEGAWAI_SETUP = '/profile-kontak-pegawai-setup';
  static const PROFILE_RIWAYAT_PENDIDIKAN_SETUP =
      '/profile-riwayat-pendidikan-setup';
  static const PROFILE_PENGALAMAN_SETUP = '/profile-pengalaman-setup';
  static const PROFILE_REKENING_SETUP = '/profile-rekening-setup';
  static const PROFILE_SERAGAM = '/profile-seragam';
  static const PROFILE_SERAGAM_SETUP = '/profile-seragam-setup';
  static const PROFILE_DOKUMEN = '/profile-dokumen';
  static const PROFILE_DOKUMEN_SETUP = '/profile-dokumen-setup';
  static const PROFILE_DATA_PRIBADI_HISTORY = '/profile-data-pribadi-history';
  static const PROFILE_KONTRAK = '/profile-kontrak';
  static const PROFILE_KONTRAK_SETUP = '/profile-kontrak-setup';
  static const PROFILE_KONTAK_PEGAWAI_HISTORY =
      '/profile-kontak-pegawai-history';
  static const PROFILE_KONTAK_PEGAWAI_HISTORY_SETUP =
      '/profile-kontak-pegawai-history-setup';
  static const PROFILE_KELUARGA_HISTORY = '/profile-keluarga-history';
  static const PROFILE_KELUARGA_HISTORY_SETUP =
      '/profile-keluarga-history-setup';
  static const PROFILE_RIWAYAT_KESEHATAN_HISTORY =
      '/profile-riwayat-kesehatan-history';
  static const PROFILE_RIWAYAT_KESEHATAN_HISTORY_SETUP =
      '/profile-riwayat-kesehatan-history-setup';
  static const PROFILE_PEGAWAI_STR = '/profile-pegawai-str';
  static const PROFILE_PEGAWAI_STR_SETUP = '/profile-pegawai-str-setup';
  static const PROFILE_PEGAWAI_STR_HISTORY = '/profile-pegawai-str-history';
  static const PROFILE_PEGAWAI_STR_HISTORY_SETUP =
      '/profile-pegawai-str-history-setup';
  static const PROFILE_REKENING_HISTORY = '/profile-rekening-history';
  static const PROFILE_REKENING_HISTORY_SETUP =
      '/profile-rekening-history-setup';
  static const PROFILE_JENIS_SERTIFIKASI_HISTORY =
      '/profile-jenis-sertifikasi-history';
  static const PROFILE_JENIS_SERTIFIKASI_HISTORY_SETUP =
      '/profile-jenis-sertifikasi-history-setup';
  static const PROFILE_FOTO_HISTORY = '/profile-foto-history';
  static const PROFILE_FOTO_HISTORY_SETUP = '/profile-foto-history-setup';
  static const PROFILE_RIWAYAT_PENDIDIKAN_HISTORY =
      '/profile-riwayat-pendidikan-history';
  static const PROFILE_RIWAYAT_PENDIDIKAN_HISTORY_SETUP =
      '/profile-riwayat-pendidikan-history-setup';
  static const PROFILE_PEGAWAI_KGB = '/profile-pegawai-kgb';
  static const PROFILE_PEGAWAI_KGB_SETUP = '/profile-pegawai-kgb-setup';
  static const PROFILE_PEGAWAI_PANGKAT = '/profile-pegawai-pangkat';
  static const PROFILE_PEGAWAI_PANGKAT_SETUP = '/profile-pegawai-pangkat-setup';
  static const ABSENSI_DETAIL = '/absensi-detail';
  static const INFORMASI_PERPUS = '/informasi-perpus';
  static const INFORMASI_PERPUS_SETUP = '/informasi-perpus-setup';
  static const INFORMASI_SERAH_TERIMA = '/informasi-serah-terima';
  static const INFORMASI_SERAH_TERIMA_MUTASI_BARANG =
      '/informasi-serah-terima-mutasi-barang';
  static const INFORMASI_SERAH_TERIMA_MUTASI_BARANG_SETUP =
      '/informasi-serah-terima-mutasi-barang-setup';
  static const QR_CODE_SCANNER = '/qr-code-scanner';
  static const PROFILE_KATEGORIBIOS = '/profile-kategoribios';
  static const PERMINTAAN_JADWAL_LEMBUR_CUSTOM_KEPALA_UNIT_SETUP =
      '/permintaan-jadwal-lembur-custom-kepala-unit-setup';
  static const PERMINTAAN_JADWAL_LEMBUR_CUSTOM_PEGAWAI_SETUP =
      '/permintaan-jadwal-lembur-custom-pegawai-setup';
  static const INFORMASI_QUOTA_IZIN = '/informasi-quota-izin';
  static const LOGIN_PASSWORD = '/login-password';
  static const PERMINTAAN_JADWAL_CUTI_DAY_PAYMENT =
      '/permintaan-jadwal-cuti-day-payment';
  static const PERMINTAAN_JADWAL_CUTI_DAY_PAYMENT_SETUP =
      '/permintaan-jadwal-cuti-day-payment-setup';
  static const PERMINTAAN_JADWAL_CUTI_UNPAID_LEAVE =
      '/permintaan-jadwal-cuti-unpaid-leave';
  static const PERMINTAAN_JADWAL_CUTI_UNPAID_LEAVE_SETUP =
      '/permintaan-jadwal-cuti-unpaid-leave-setup';
  static const PERMINTAAN_JADWAL_CUTI_SETENGAH_HARI =
      '/permintaan-jadwal-cuti-setengah-hari';
  static const PERMINTAAN_JADWAL_CUTI_SETENGAH_HARI_SETUP =
      '/permintaan-jadwal-cuti-setengah-hari-setup';
  static const INFORMASI_ABSENSI_JADWAL_ANGGOTA_DIVISI =
      '/informasi-absensi-jadwal-anggota-divisi';
  static const INFORMASI_PENGUMUMAN = '/informasi-pengumuman';
  static const INFORMASI_PENGUMUMAN_SETUP = '/informasi-pengumuman-setup';
  static const INFORMASI_PERINGATAN = '/informasi-peringatan';
  static const INFORMASI_PERINGATAN_SETUP = '/informasi-peringatan-setup';
}
