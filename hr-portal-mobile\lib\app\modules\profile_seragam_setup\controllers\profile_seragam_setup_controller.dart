import 'dart:convert';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/models/api_list_resut_model.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../models/profile_seragam_model.dart';

class ProfileSeragamSetupController extends GetxController {
  late SetupPageController formCon;
  final jenisSeragamCon = InputDropdownController();
  final kodeInventoryCon = InputTextController();
  final ukuranCon = InputTextController();
  final jumlahCon = InputTextController(
    type: InputTextType.number,
  );
  final tglTerimaCon = InputDatetimeController();

  @override
  void onInit() {
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PegawaiSeragam/$id',
      urlApiPost: () => '/api/PegawaiSeragam',
      urlApiPut: (id) => '/api/PegawaiSeragam/$id',
      urlApiDelete: (id) => '/api/PegawaiSeragam/$id',
      bodyApi: (id) => {
        "id_Pegawai": MahasConfig.profile!.id,
        "id_Seragam": jenisSeragamCon.value,
        "kodeInventory": kodeInventoryCon.value,
        "ukuran": ukuranCon.value,
        "jumlah": jumlahCon.value,
        "tanggalTerima": MahasFormat.dateToString(tglTerimaCon.value)
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      apiToView: (json) {
        SeragamModel model = SeragamModel.fromJson(json);
        jenisSeragamCon.value = model.idSeragam;
        kodeInventoryCon.value = model.kodeinventory;
        ukuranCon.value = model.ukuran;
        jumlahCon.value = model.jumlah;
        tglTerimaCon.value = model.tanggalterima;
      },
      onBeforeSubmit: () {
        if (!jenisSeragamCon.isValid) return false;
        if (!kodeInventoryCon.isValid) return false;
        if (!ukuranCon.isValid) return false;
        if (!jumlahCon.isValid) return false;
        if (!tglTerimaCon.isValid) return false;
        return true;
      },
      onInit: () async {
        await getKontak();
      },
    );
    super.onInit();
  }

  Future<void> getKontak() async {
    if (EasyLoading.isShow) return;
    EasyLoading.show();
    var r = await HttpApi.get("/api/JenisSeragam");
    if (r.success) {
      RxList<JenisSeragamModel> listModel = RxList<JenisSeragamModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(JenisSeragamModel.fromDynamic(e));
      }
      if (r.body != null && jenisSeragamCon.items.isEmpty) {
        jenisSeragamCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      }
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }
}
