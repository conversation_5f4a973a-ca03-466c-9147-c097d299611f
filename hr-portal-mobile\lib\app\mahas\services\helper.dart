import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/constant/environment_constant.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_datetime_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_radio_component.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:uuid/uuid.dart';

import '../../mahas_complement/status_request.dart';
import '../../models/foto_profile_model.dart';
import '../../models/pegawai_approval.dart';
import '../mahas_colors.dart';
import '../components/mahas_themes.dart';
import '../mahas_service.dart';
import '../models/api_result_model.dart';
import 'http_api.dart';

enum HttpMethod {
  get,
  post,
  delete,
}

class Helper {
  static Future<bool?> dialogQuestion({
    String? message,
    IconData? icon,
    String? textConfirm,
    String? textCancel,
    Function()? confirmAction,
    Color? color,
  }) async {
    return await Get.dialog<bool?>(
      AlertDialog(
        shape: RoundedRectangleBorder(
            borderRadius:
                BorderRadius.all(Radius.circular(MahasThemes.borderRadius))),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon ?? FontAwesomeIcons.question,
              color: color ?? MahasColors.primary,
              size: 40,
            ),
            const Padding(padding: EdgeInsets.all(10)),
            Text(
              message ?? "",
              textAlign: TextAlign.center,
            ),
          ],
        ),
        contentPadding:
            const EdgeInsets.only(bottom: 0, top: 20, right: 20, left: 20),
        actionsPadding:
            const EdgeInsets.only(top: 10, bottom: 5, left: 20, right: 20),
        actions: [
          TextButton(
            child: Text(
              textCancel ?? "Close",
              style: const TextStyle(
                color: MahasColors.dark,
              ),
            ),
            onPressed: () {
              Get.back(result: false);
            },
          ),
          TextButton(
            child: Text(
              textConfirm ?? "OK",
              style: TextStyle(
                color: color ?? MahasColors.primary,
              ),
            ),
            onPressed: () {
              Get.back(result: true);
              confirmAction != null ? confirmAction() : null;
            },
          ),
        ],
      ),
    );
  }

  static Future<bool?> dialogUpdate({
    required bool harusUpdate,
    required String versiTerbaru,
  }) async {
    return await Get.dialog<bool?>(
      AlertDialog(
        shape: RoundedRectangleBorder(
            borderRadius:
                BorderRadius.all(Radius.circular(MahasThemes.borderRadius))),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              FontAwesomeIcons.question,
              color: MahasColors.primary,
              size: 40,
            ),
            const Padding(padding: EdgeInsets.all(10)),
            Text(
              "Versi $versiTerbaru sudah tersedia",
              textAlign: TextAlign.center,
            ),
          ],
        ),
        contentPadding:
            const EdgeInsets.only(bottom: 0, top: 20, right: 20, left: 20),
        actionsPadding:
            const EdgeInsets.only(top: 10, bottom: 5, left: 20, right: 20),
        actions: [
          TextButton(
            child: Text(
              harusUpdate ? "Tutup" : "Nanti",
              style: const TextStyle(
                color: MahasColors.dark,
              ),
            ),
            onPressed: () {
              if (harusUpdate) {
                if (Platform.isAndroid) {
                  SystemNavigator.pop();
                } else if (Platform.isIOS) {
                  exit(0);
                }
              } else {
                Get.back(result: false);
              }
            },
          ),
          TextButton(
            child: Text(
              "Unduh Sekarang",
              style: TextStyle(
                color: MahasColors.primary,
              ),
            ),
            onPressed: () {
              Get.back(result: true);
            },
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  static Future dialogWarning(
    String? message, {
    String? resMassage,
    String? noInternetMassage,
  }) async {
    final String contentMessage = resMassage != null
        ? MahasService.isInternetCausedError(resMassage)
            ? noInternetMassage ?? "Tidak ada koneksi internet"
            : resMassage
        : message ?? "-";

    await Get.dialog(
      AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              FontAwesomeIcons.triangleExclamation,
              color: MahasColors.warning,
              size: 40,
            ),
            const Padding(padding: EdgeInsets.all(7)),
            Text(
              textAlign: TextAlign.center,
              contentMessage,
              style: TextStyle(
                color: MahasColors.warning,
              ),
            ),
          ],
        ),
      ),
    );
  }

  static Future dialogSuccess(String? message) async {
    await Get.dialog(
      AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              FontAwesomeIcons.checkToSlot,
              color: MahasColors.primary,
              size: 40,
            ),
            const Padding(padding: EdgeInsets.all(7)),
            Text(
              textAlign: TextAlign.center,
              message ?? "-",
              style: TextStyle(
                color: MahasColors.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  static Future dialogBirthday(String? message) async {
    await Get.dialog(
      AlertDialog(
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(20.0))),
        content: Container(
          padding: const EdgeInsets.only(top: 50, bottom: 50),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                FontAwesomeIcons.cakeCandles,
                color: MahasColors.primary,
                size: 80,
              ),
              const SizedBox(
                height: 30,
              ),
              Text(
                textAlign: TextAlign.center,
                "Selamat Ulang Tahun!",
                style: TextStyle(
                  color: MahasColors.primary,
                ),
              ),
              const SizedBox(
                height: 3,
              ),
              Text(
                textAlign: TextAlign.center,
                message ?? "-",
                style: TextStyle(
                  fontSize: 30,
                  fontWeight: FontWeight.bold,
                  color: MahasColors.primary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  static Future dialogIcon({
    IconData? icon,
    String? title,
    String? message,
  }) async {
    await Get.dialog(
      AlertDialog(
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(20.0))),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon ?? FontAwesomeIcons.cakeCandles,
              color: MahasColors.primary,
            ),
            const SizedBox(
              height: 5,
            ),
            Text(
              textAlign: TextAlign.center,
              title ?? "-",
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: MahasColors.primary,
              ),
            ),
            const SizedBox(
              height: 5,
            ),
            Text(
              textAlign: TextAlign.center,
              message ?? "-",
              style: TextStyle(
                color: MahasColors.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  static Future fotoProfilOnTap(int idPegawai) async {
    if (EasyLoading.isShow) {
      EasyLoading.dismiss();
    }
    EasyLoading.show();

    var r = await HttpApi.get('/api/FotoProfile/$idPegawai');
    if (r.success) {
      FotoProfileModel fotoProfileModel = FotoProfileModel.fromJson(r.body);
      Helper.dialogFoto(
          fotoProfileModel.fotoprofile, EnvironmentConstant.imageLogo);
    } else {
      await Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }

  static Future dialogFoto(String? fotoUrl, String? fallbackFoto) async {
    await Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(MahasThemes.borderRadius),
          ),
        ),
        clipBehavior: Clip.antiAlias,
        contentPadding: const EdgeInsets.all(0),
        content: Container(
          width: Get.width * 0.6,
          padding: const EdgeInsets.all(15),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(Get.width),
            child: fotoUrl != null
                ? Image.memory(
                    base64Decode(fotoUrl),
                    fit: BoxFit.cover,
                  )
                : Image.asset(
                    fallbackFoto ?? EnvironmentConstant.imageLogo,
                  ),
          ),
        ),
      ),
    );
  }

  static Future<void> dialogFilter({
    required InputDatetimeController dariTanggalCon,
    required InputDatetimeController sampaiTanggalCon,
    Function()? todayFilterOnTap,
    Function()? periodFilterOnTap,
    List<Widget>? children,
  }) async {
    RxBool isPeriod = false.obs;
    final InputRadioController filterCon = InputRadioController(
      items: [
        RadioButtonItem(text: "Hari Ini", value: 1),
        RadioButtonItem(text: "Periode", value: 2),
      ],
    );
    filterCon.value = 1;
    filterCon.onChanged = (value) {
      isPeriod.value = value.value == 1 ? false : true;
    };
    Get.dialog(
      AlertDialog(
        clipBehavior: Clip.hardEdge,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(MahasThemes.borderRadius),
          ),
        ),
        contentPadding: const EdgeInsets.all(0),
        content: Obx(
          () => SizedBox(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  color: MahasColors.primary,
                  height: 50,
                  child: const Center(
                    child: Text(
                      "Filter",
                      style: TextStyle(
                        color: MahasColors.light,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.start,
                    ),
                  ),
                ),
                const Padding(padding: EdgeInsets.only(top: 10)),
                InputRadioComponent(
                  controller: filterCon,
                ),
                Visibility(
                  visible: isPeriod.value,
                  child: Padding(
                    padding: const EdgeInsets.all(10),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        InputDatetimeComponent(
                          label: "Dari Tanggal",
                          controller: dariTanggalCon,
                          required: true,
                        ),
                        InputDatetimeComponent(
                          label: "Sampai Tanggal",
                          controller: sampaiTanggalCon,
                          required: true,
                          marginBottom: 0,
                        ),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(10),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: children ?? [],
                  ),
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            style: ButtonStyle(
              backgroundColor:
                  WidgetStateColor.resolveWith((states) => MahasColors.primary),
            ),
            child: const Text(
              "Cari",
              style: TextStyle(
                color: MahasColors.light,
              ),
            ),
            onPressed: () async {
              if (filterCon.value == 2 &&
                  dariTanggalCon.isValid &&
                  sampaiTanggalCon.isValid) {
                periodFilterOnTap != null ? periodFilterOnTap() : null;
              } else if (filterCon.value == 1) {
                todayFilterOnTap != null ? todayFilterOnTap() : null;
              }
            },
          ),
        ],
      ),
    );
  }

  static Future<void> dialogFilterCustom({
    Function()? confirmAction,
    List<Widget>? children,
    bool? isClear,
    Function()? clearAction,
  }) async {
    Get.dialog(
      AlertDialog(
        clipBehavior: Clip.hardEdge,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(MahasThemes.borderRadius),
          ),
        ),
        contentPadding: const EdgeInsets.all(0),
        content: SizedBox(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                color: MahasColors.primary,
                height: 50,
                child: const Center(
                  child: Text(
                    "Filter",
                    style: TextStyle(
                      color: MahasColors.light,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.start,
                  ),
                ),
              ),
              const Padding(padding: EdgeInsets.only(top: 10)),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: children ?? [],
                ),
              ),
            ],
          ),
        ),
        actions: [
          Visibility(
            visible: isClear == true,
            child: TextButton(
              style: ButtonStyle(
                backgroundColor: WidgetStateColor.resolveWith(
                    (states) => MahasColors.primary),
              ),
              onPressed: () {
                clearAction != null ? clearAction() : null;
                Get.back();
              },
              child: const Text(
                "Clear",
                style: TextStyle(
                  color: MahasColors.light,
                ),
              ),
            ),
          ),
          TextButton(
            style: ButtonStyle(
              backgroundColor:
                  WidgetStateColor.resolveWith((states) => MahasColors.primary),
            ),
            onPressed: () {
              confirmAction != null ? confirmAction() : null;
              Get.back();
            },
            child: const Text(
              "Cari",
              style: TextStyle(
                color: MahasColors.light,
              ),
            ),
          ),
        ],
      ),
    );
  }

  static void fetchErrorMessage(ApiResultModel error,
      {HttpMethod httpMethod = HttpMethod.get}) {
    if (EasyLoading.isShow) EasyLoading.dismiss();
    String method;
    switch (httpMethod) {
      case HttpMethod.get:
        method = "memuat";
        break;
      case HttpMethod.post:
        method = "menyimpan";
        break;
      case HttpMethod.delete:
        method = "menghapus";
    }
    if (error.statusCode == 404) {
      Helper.dialogWarning("Data tidak ditemukan atau sudah dihapus!");
    }
    if (error.statusCode == 400) {
      Helper.dialogWarning(error.message);
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning("Gagal $method data, silahkan cek koneksi internet");
    } else if (error.message
        .toString()
        .contains(RegExp("kunci", caseSensitive: false))) {
      Helper.dialogWarning(error.message);
    } else {
      Helper.dialogWarning("Gagal $method data");
    }
  }

  static Future<ApiResultModel> approvalAction(
    String id,
    bool approve,
    String? alasanTolak,
    dynamic accKadiv,
    String urlKadiv,
    String urlManager, {
    bool? isDinamis,
    String? urlApprove,
    bool? isWithPengganti = false,
  }) async {
    if (EasyLoading.isShow) {
      EasyLoading.dismiss();
    }
    EasyLoading.show();
    final body = {
      'approve': approve,
      'alasanTolak': alasanTolak ?? '-',
    };
    ApiResultModel? r;
    if (isDinamis == true && accKadiv == null && isWithPengganti == true) {
      final url = urlKadiv + id;
      r = await HttpApi.put(
        url,
        body: body,
      );
    } else if (isDinamis == true && urlApprove != null) {
      final url = urlApprove + id;
      r = await HttpApi.put(
        url,
        body: body,
      );
    } else if (accKadiv == null) {
      final url = urlKadiv + id;
      r = await HttpApi.put(
        url,
        body: body,
      );
    } else if (accKadiv == true) {
      final url = urlManager + id;
      r = await HttpApi.put(
        url,
        body: body,
      );
    }
    EasyLoading.dismiss();
    return r!;
  }

  static Future<ApiResultModel> batalAction(
    String id,
    String alasanBatal,
    String urlManager,
  ) async {
    if (EasyLoading.isShow) {
      EasyLoading.dismiss();
    }
    EasyLoading.show();
    final body = {
      'alasanBatal': alasanBatal,
    };
    ApiResultModel? r;
    final url = urlManager + id;
    r = await HttpApi.put(
      url,
      body: body,
    );
    EasyLoading.dismiss();
    return r;
  }

  static void backOnPress({
    dynamic result,
    bool questionBack = true,
    bool editable = false,
    dynamic parametes,
  }) async {
    if (questionBack && editable) {
      final r = await Helper.dialogQuestion(
        message: 'Anda yakin ingin kembali ?',
        textConfirm: 'Ya',
      );
      if (r != true) return;
    }
    Get.back(result: result);
  }

  static String idGenerator() {
    const uuid = Uuid();
    var r = uuid.v4();
    return r;
  }

  static dynamic modalMenu(List<Widget> children) async {
    return await showCustomModalBottomSheet(
      context: Get.context!,
      builder: (context) => Container(
        margin: const EdgeInsets.only(bottom: 20, left: 20, right: 20),
        decoration: BoxDecoration(
          borderRadius:
              BorderRadius.all(Radius.circular(MahasThemes.borderRadius)),
          color: MahasColors.light,
        ),
        child: Column(
          children: children,
        ),
      ),
      containerWidget: (_, animation, child) => SafeArea(
        child: Column(
          children: [Expanded(child: Container()), child],
        ),
      ),
      expand: false,
    );
  }

  static List<DateTime> calculateDaysInterval(
      DateTime startDate, DateTime endDate) {
    List<DateTime> days = [];
    for (int i = 0; i <= endDate.difference(startDate).inDays; i++) {
      days.add(startDate.add(Duration(days: i)));
    }
    return days;
  }

  static Future statusApproval(
    String url, {
    String? alasanTolak,
    bool? approvePengganti,
  }) async {
    if (EasyLoading.isShow) {
      EasyLoading.dismiss();
    }
    EasyLoading.show();

    var r = await HttpApi.get(url);
    if (r.success) {
      var datas = json.decode(r.body);
      List<PegawaiApprovalModel> data = [];
      for (var e in datas['datas']) {
        data.add(PegawaiApprovalModel.fromDynamic(e));
      }
      List<PegawaiApprovalModel> listApproval = data
        ..sort((a, b) => a.tingkatan!.compareTo(b.tingkatan!));
      Helper.dialogApproval(
        listApproval,
        alasanTolak: alasanTolak,
        approvePengganti: approvePengganti,
      );
    } else if (r.statusCode == 404) {
      Helper.dialogWarning("Tidak Ada Data Pegawai Approval");
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }

  static Future dialogApproval(
    List<PegawaiApprovalModel> data, {
    String? alasanTolak,
    bool? approvePengganti,
  }) async {
    bool? isStop;
    if (data.isNotEmpty) {
      isStop = data.any((item) => item.approve == false);
      if (approvePengganti != null && approvePengganti == false) {
        isStop = true;
      }
    }

    return Get.bottomSheet(
      Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.0),
            topRight: Radius.circular(20.0),
          ),
        ),
        padding: const EdgeInsets.only(left: 12, right: 12, top: 4, bottom: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: <Widget>[
            Center(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Container(
                  height: 5,
                  width: 100,
                  decoration: const BoxDecoration(
                    color: MahasColors.grey,
                    borderRadius: BorderRadius.all(Radius.circular(5)),
                  ),
                ),
              ),
            ),
            const Text("Status Request"),
            const SizedBox(height: 12),
            Expanded(
              child: ListView.builder(
                itemCount: data.length,
                itemBuilder: (context, index) {
                  return CardStatus(
                    pangkatApproval: "Tingkat ${data[index].tingkatan}",
                    namaApproval: data[index].pegawaiapprovenama ?? "",
                    isAcc: data[index].approve,
                    isStop: isStop,
                    statusTolak: alasanTolak,
                  );
                },
              ),
            )
          ],
        ),
      ),
    );
  }

  static succesPengajuanMessage() {
    String message = '';

    if (MahasConfig.atasan.isManager!) {
      message = "Selamat, Pengajuan Anda Berhasil";
    } else {
      message =
          "Selamat, Pengajuan Anda Berhasil. Mohon Menunggu Persetujuan dari Atasan Anda";
    }

    return message;
  }
}
