import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../models/profile_hobi_model.dart';
import '../../../routes/app_pages.dart';

class ProfileHobiController extends GetxController {
  final listCon = ListComponentController<ProfilehobiModel>(
    urlApi: (index, filter) => '/api/PegawaiHobi/List?pageIndex=$index',
    fromDynamic: ProfilehobiModel.fromDynamic,
    allowSearch: false,
  );

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PROFILE_HOBI_SETUP,
      parameters: {
        'id': id.toString(),
      },
    )?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }

  void addOnPress() {
    Get.toNamed(Routes.PROFILE_HOBI_SETUP)?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }
}
