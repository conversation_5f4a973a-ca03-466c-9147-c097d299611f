import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_datetime_component.dart';

import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../controllers/profile_pengalaman_setup_controller.dart';

class ProfilePengalamanSetupView
    extends GetView<ProfilePengalamanSetupController> {
  const ProfilePengalamanSetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      controller: controller.formCon,
      title: 'Pengalaman',
      children: () => [
        InputTextComponent(
          label: "Pengalaman",
          controller: controller.pengalaman,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDatetimeComponent(
          label: '<PERSON><PERSON>',
          controller: controller.dariTanggal,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDatetimeComponent(
          controller: controller.sampaiTanggal,
          label: '<PERSON><PERSON><PERSON>',
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          controller: controller.keterangan,
          label: 'Keterangan',
          required: true,
          editable: controller.formCon.editable,
        ),
      ],
    );
  }
}
