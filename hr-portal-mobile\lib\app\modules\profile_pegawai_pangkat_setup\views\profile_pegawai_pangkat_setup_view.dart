import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_checkbox_component.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../controllers/profile_pegawai_pangkat_setup_controller.dart';

class ProfilePegawaiPangkatSetupView
    extends GetView<ProfilePegawaiPangkatSetupController> {
  const ProfilePegawaiPangkatSetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      controller: controller.formCon,
      title: 'Informasi',
      children: () => [
        InputTextComponent(
          label: "Pangkat",
          controller: controller.pangkatCon,
          editable: false,
        ),
        InputTextComponent(
          label: "Nomor KEP",
          controller: controller.nomorKepCon,
          editable: false,
        ),
        InputDatetimeComponent(
          label: 'Ter<PERSON><PERSON>lai',
          controller: controller.terhitungMulaiCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputCheckboxComponent(
          label: 'Aktif',
          controller: controller.aktifCon,
          editable: controller.formCon.editable,
        ),
      ],
    );
  }
}
