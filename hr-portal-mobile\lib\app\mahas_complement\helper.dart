import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/mahas_themes.dart';

class HelperComp {
  static Future dialogCustomWidget(List<Widget> children) async {
    await Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
            borderRadius:
                BorderRadius.all(Radius.circular(MahasThemes.borderRadius))),
        content: Column(mainAxisSize: MainAxisSize.min, children: children),
        contentPadding:
            const EdgeInsets.only(bottom: 0, top: 20, right: 10, left: 10),
        actionsPadding:
            const EdgeInsets.only(top: 0, bottom: 5, left: 20, right: 20),
        actions: [
          TextButton(
            child: const Text("Close"),
            onPressed: () {
              Get.back(result: false);
            },
          ),
        ],
      ),
    );
  }

  static Future dialogFullScreen(Widget child) async {
    await showDialog(
      context: Get.context!,
      builder: (context) {
        return Dialog(
          insetPadding: EdgeInsets.zero,
          child: SizedBox(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextButton(
                  child: const Text("Close"),
                  onPressed: () {
                    Get.back(result: false);
                  },
                ),
                child,
              ],
            ),
          ),
        );
      },
    );
  }

  static Future<List<int>> toByte(File file) async {
    List<int> byteData = await file.readAsBytes();
    return byteData;
  }
}
