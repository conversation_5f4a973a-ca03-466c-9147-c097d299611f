import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas_complement/status_request.dart';
import '../controllers/permintaan_jadwal_cuti_setengah_hari_setup_controller.dart';

class PermintaanJadwalCutiSetengahHariSetupView
    extends GetView<PermintaanJadwalCutiSetengahHariSetupController> {
  const PermintaanJadwalCutiSetengahHariSetupView({super.key});

  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      title: "<PERSON><PERSON>",
      controller: controller.formCon,
      childrenPadding: false,
      children: () => [
        Visibility(
          visible: controller.formCon.isState != SetupPageState.create,
          child: Column(
            children: [
              Obx(() => Text(
                  "Tanggal/Waktu pengajuan : ${controller.tglPengajuan.value}")),
              const SizedBox(
                height: 10,
              ),
            ],
          ),
        ),
        Visibility(
          visible: MahasConfig.dinamisForm.cutiTahunan?.quotaVisible == true &&
              controller.formCon.isState == SetupPageState.create,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(
                      width: 180,
                      child: InputDatetimeComponent(
                        label: "Hak Cuti",
                        controller: controller.tanggalMulaiCon,
                        editable: false,
                      ),
                    ),
                    SizedBox(
                      width: 180,
                      child: InputDatetimeComponent(
                        label: "Berakhir Pada",
                        controller: controller.tanggalSelesaiCon,
                        editable: false,
                      ),
                    ),
                  ],
                ),
                InputTextComponent(
                  label: "Quota Cuti",
                  controller: controller.jumlahCutiCon,
                  editable: false,
                ),
                Visibility(
                  visible: controller.sisaCutiCon.value != 0,
                  child: Column(
                    children: [
                      InputTextComponent(
                        label: "Sisa Cuti Sebelumnya",
                        controller: controller.sisaCutiCon,
                        editable: false,
                      ),
                      InputDatetimeComponent(
                        label: "Tanggal Berakhir Sisa Cuti Sebelumnya",
                        controller: controller.tanggalSisaSelesaiCon,
                        editable: false,
                      ),
                      InputTextComponent(
                        label: "Total Quota Cuti",
                        controller: controller.totalCutiCon,
                        editable: false,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        controller.isKadiv == true &&
                controller.formCon.isState == SetupPageState.detail
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: InputTextComponent(
                      label: "Pegawai Request",
                      controller: controller.pegawaiCutiCon,
                      editable: false,
                      marginBottom: 5,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: InkWell(
                      onTap: () async => await Helper.fotoProfilOnTap(
                          controller.idPegawaiRequest.value),
                      child: SizedBox(
                        height: 30,
                        child: Text(
                          "Lihat Foto Profil Pegawai",
                          style: MahasThemes.link,
                          textAlign: TextAlign.left,
                        ),
                      ),
                    ),
                  ),
                ],
              )
            : const SizedBox(),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SizedBox(
                    width: 180,
                    child: InputDatetimeComponent(
                      label: "Tanggal Cuti",
                      required: true,
                      controller: controller.tanggalCutiCon,
                      editable: controller.formCon.editable,
                    ),
                  ),
                  SizedBox(
                    width: 180,
                    child: InputDatetimeComponent(
                      type: InputDatetimeType.time,
                      label: "Waktu",
                      required: true,
                      controller: controller.jamCon,
                      editable: controller.formCon.editable,
                    ),
                  ),
                ],
              ),
              InputDropdownComponent(
                label: "Keterangan",
                controller: controller.jenisCon,
                required: true,
                editable: controller.formCon.editable,
              ),
              InputTextComponent(
                label: "Alasan",
                required: true,
                controller: controller.alasanCon,
                editable: controller.formCon.editable,
              ),
            ],
          ),
        ),
        Obx(() => ApprovalStatusContainer(
              padding: 10,
              isEditable: controller.formCon.editable,
              isVisible: controller.isVisible,
              isBatal: controller.batal,
              allowED: controller.allowED,
              isDinamisApprove: controller.isDinamisApprove,
              alasanTolakController: controller.alasanTolakCon,
              onTolakPressed: () {
                controller.approvalOnPress(false);
              },
              onTerimaPressed: () {
                controller.approvalOnPress(true);
              },
              batalOnPress: () {},
              pegawaiKadiv: controller.pegawaiKadiv.value,
              pegawaiManager: controller.pegawaiManager.value,
              accKadiv: controller.accKadiv.value,
              accManager: controller.accManager.value,
              statusTolak: controller.statusTolak.value,
              modelApproval: controller.modelApproval,
              isStop: controller.isStop.value,
            )),
      ],
    );
  }
}
