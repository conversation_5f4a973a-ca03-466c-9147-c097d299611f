import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class KontrakModel {
  int? id;
  int? idPegawai;
  int? idJabatan;
	String? namajabatan;
  DateTime? tanggalgabung;
  DateTime? tanggalberlaku;
  DateTime? tanggalberakhir;
  String? nosurat;
  String? nosk;
  DateTime? tanggalresign;
  bool? aktif;
  int? sisahariexpiredkontrak;
  int? lamawaktubekerja;
  int? sisahariexpiredpensiun;

  KontrakModel();

  static KontrakModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static KontrakModel fromDynamic(dynamic dynamicData) {
    final model = KontrakModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.idJabatan = MahasFormat.dynamicToInt(dynamicData['id_Jabatan']);
		model.namajabatan = dynamicData['namaJabatan'];
    model.tanggalgabung =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalGabung']);
    model.tanggalberlaku =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalBerlaku']);
    model.tanggalberakhir =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalBerakhir']);
    model.nosurat = dynamicData['noSurat'];
    model.nosk = dynamicData['noSK'];
    model.tanggalresign =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalResign']);
    model.aktif = MahasFormat.dynamicToBool(dynamicData['aktif']);
    model.sisahariexpiredkontrak =
        MahasFormat.dynamicToInt(dynamicData['sisaHariExpiredKontrak']);
    model.lamawaktubekerja =
        MahasFormat.dynamicToInt(dynamicData['lamaWaktuBekerja']);
    model.sisahariexpiredpensiun =
        MahasFormat.dynamicToInt(dynamicData['sisaHariExpiredPensiun']);

    return model;
  }
}
