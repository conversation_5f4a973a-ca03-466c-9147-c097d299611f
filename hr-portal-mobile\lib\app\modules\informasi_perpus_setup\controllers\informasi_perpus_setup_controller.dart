import 'dart:convert';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:path_provider/path_provider.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../models/perpus_model.dart';
import 'package:http/http.dart' as http;

class InformasiPerpusSetupController extends GetxController {
  late SetupPageController formCon;

  final namaSertifikasiCon = InputTextController();
  final kategoriCon = InputTextController();
  final nomorCon = InputTextController();
  final tahunCon = InputTextController();
  final tglDiundangkanCon = InputDatetimeController();
  final tglDiterbitkanCon = InputDatetimeController();
  final berkasPendukungCon = InputFileController(
    type: FileType.custom,
    extension: ['pdf'],
    tipe: InputFileType.pdfFile,
    urlImage: true,
  );

  RxInt idPegawaiSertifikasi = 0.obs;

  @override
  void onInit() {
    formCon = SetupPageController(
      allowDelete: false,
      allowEdit: false,
      urlApiGet: (id) => '/api/Perpus/$id',
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      apiToView: (json) {
        PerpusModel model = PerpusModel.fromJson(json);

        namaSertifikasiCon.value = model.tentang;
        kategoriCon.value = model.kategoriperpusnama;
        tglDiundangkanCon.value = model.tanggaldiundangkan;
        tglDiterbitkanCon.value = model.tanggaldokumenterbit;
        nomorCon.value = model.nomor;
        tahunCon.value = model.tahun;
      },
      onInit: () async {
        await getFile();
      },
    );

    super.onInit();
  }

  Future<void> getFile() async {
    var id = Get.parameters["id"];
    var response = await http
        .get(Uri.parse('${MahasConfig.urlApi}/api/Perpus/GetFile/$id'));
    if (response.statusCode == 200) {
      List<int> bytes = response.bodyBytes;
      final dir = await getTemporaryDirectory();
      final file = File('${dir.path}/temp.pdf');
      await file.writeAsBytes(bytes);
      berkasPendukungCon.value = file.path;
    } else {
      throw Exception('Failed to load PDF');
    }
  }
}
