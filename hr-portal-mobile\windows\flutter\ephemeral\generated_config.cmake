# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\Sanata-project\\hr-portal\\hr-portal-mobile" PROJECT_DIR)

set(FLUTTER_VERSION "4.0.14+33" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 4 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 14 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 33 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\flutter"
  "PROJECT_DIR=C:\\Sanata-project\\hr-portal\\hr-portal-mobile"
  "FLUTTER_ROOT=C:\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\Sanata-project\\hr-portal\\hr-portal-mobile\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\Sanata-project\\hr-portal\\hr-portal-mobile"
  "FLUTTER_TARGET=C:\\Sanata-project\\hr-portal\\hr-portal-mobile\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuMA==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049YmU2OThjNDhhNg==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049MTg4MTgwMDk0OQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjA="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\Sanata-project\\hr-portal\\hr-portal-mobile\\.dart_tool\\package_config.json"
  "FLAVOR=staging"
)
