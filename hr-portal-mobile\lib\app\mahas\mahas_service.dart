import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:hr_portal/app/mahas/mahas_storage.dart';
import 'package:hr_portal/app/mahas/models/color_theme_model.dart';
import 'package:hr_portal/app/models/menu_profile_model.dart';
import 'package:hr_portal/app/models/update_app_values_model.dart';
import 'package:overlay_support/overlay_support.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../../../firebase_options.dart' as prod;
import '../../../firebase_options_staging.dart' as staging;
import '../controllers/auth_controller.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../mahas_complement/jadwal_storage.dart';
import '../mahas_complement/profile_storage.dart';
import '../mahas_complement/service_data.dart';
import '../models/form_dinamis_model.dart';
import '../models/kadiv_manager_model.dart';
import '../services/connection_checker_service.dart';
import '../services/local_notification_service.dart';
import 'mahas_colors.dart';
import 'mahas_config.dart';
import 'package:timezone/data/latest.dart' as tz;

final authController = AuthController.instance;
final remoteConfig = FirebaseRemoteConfig.instance;
final auth = FirebaseAuth.instance;

/// MahasService handles all application initialization and services.
/// It follows a modular approach to initialization with clear separation
/// between critical and non-critical operations.
class MahasService {
  // Background message handler for Firebase Cloud Messaging
  static Future<void> backgroundHandler(RemoteMessage message) async {}

  /// Main initialization entry point
  /// Initializes critical components first, then triggers non-critical init
  static Future<void> init() async {
    try {
      await _initCriticalServices();
      _initNonCriticalServices();
    } catch (e) {
      // Error in initialization
    }
  }

  /// Initialize core services essential for app startup
  /// These must complete before the app can function properly
  static Future<void> _initCriticalServices() async {
    try {
      // Local storage for app settings
      await GetStorage.init();

      // Package info for version checks
      MahasConfig.packageInfo = await PackageInfo.fromPlatform();

      // UI configuration
      await _configureUI();

      // Check network connectivity
      await InternetConnectionService.isInternet();

      // Initialize local database
      await _initLocalDatabase();

      // Initialize Firebase and related services
      await initApp();

      MahasConfig.urlApi =
          //"https://apps.sanatasystem.net/hrportal-api-rsub";
          "https://d85b766d816f.ngrok-free.app";
      //"https://hrportalrsprapi-dev.sanatasystem.net";
      //"https://apps.sanatasystem.net/hrportal-api-rspb";
      //"https://apps.sanatasystem.net/hrportal-api-ramata";
      //"https://apps.sanatasystem.net/hrportal-api-adapotek";

      //MahasConfig.updateAppValues.mustUpdate = false;
      //MahasConfig.profileMenu.approvalDinamis = false;
    } catch (e) {
      // Critical services error, continuing as app might work in offline mode
    }
  }

  /// Configure UI elements like status bar and orientation
  static Future<void> _configureUI() async {
    try {
      // Transparent status bar
      SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
      ));

      // Set device orientation
      await SystemChrome.setPreferredOrientations(
        [DeviceOrientation.portraitUp],
      );
    } catch (e) {
      // UI config errors are not fatal
    }
  }

  /// Initialize services that are not needed immediately for app startup
  /// These run in the background after critical services are complete
  static Future<void> _initNonCriticalServices() async {
    try {
      // Start network monitoring
      InternetConnectionService.checkInternet();

      // Initialize notification services
      await _initNotificationServices();

      // Configure HTTP client
      HttpOverrides.global = MyHttpOverrides();
    } catch (e) {
      // Non-critical errors shouldn't crash the app
    }
  }

  /// Initialize local database storage systems
  static Future<void> _initLocalDatabase() async {
    try {
      // Initialize Hive database regardless of connectivity status
      await Hive.initFlutter();
      mainStorage = await Hive.openBox('mainStorage');

      // Only load cached data immediately in offline mode
      // In online mode, data should be refreshed from the server
      if (MahasConfig.hasInternet != true) {
        // Load data in parallel for better performance
        await Future.wait<void>([
          ProfileService.loadDataFromDB(),
          JadwalService.loadDataFromDB(),
        ]);
      } else {
        // In online mode, we can load this data asynchronously
        // to not block the UI, as fresh data will be fetched anyway
        Future.microtask(() async {
          await Future.wait<void>([
            ProfileService.loadDataFromDB(),
            JadwalService.loadDataFromDB(),
          ]);
        });
      }
    } catch (e) {
      // Database errors are handled gracefully
    }
  }

  /// Initialize notification services
  static Future<void> _initNotificationServices() async {
    try {
      // Local notification system
      LocalNotificationService().initialize();

      // Timezone data for scheduled notifications
      tz.initializeTimeZones();
    } catch (e) {
      // Notification errors shouldn't crash the app
    }
  }

  /// Check if an error is related to internet connectivity
  static bool isInternetCausedError(String error) {
    bool result = true;
    for (var e in MahasConfig.noInternetErrorMessage) {
      if (error.contains(RegExp(e, caseSensitive: false))) {
        result = true;
        break;
      } else {
        result = false;
      }
    }
    return result;
  }

  /// Initialize Firebase and related services
  static Future<void> initApp() async {
    if (MahasConfig.hasInternet == true) {
      try {
        await _initializeFirebase();
      } catch (e) {
        // If Firebase fails but we have AuthController, proceed
        if (!Get.isRegistered<AuthController>()) {
          Get.put(AuthController());
        }
      }
    } else {
      // Offline mode - directly initialize AuthController
      Get.put(AuthController());
    }
  }

  /// Initialize Firebase Core, Auth, and Remote Config
  static Future<void> _initializeFirebase() async {
    const flavor = String.fromEnvironment('FLAVOR', defaultValue: 'dev');

    // Initialize Firebase based on platform
    final Future<FirebaseApp> firebaseInitialization = Firebase.initializeApp(
      options: flavor == 'dev'
          ? staging.DefaultFirebaseOptions.currentPlatform
          : prod.DefaultFirebaseOptions.currentPlatform,
    );

    await firebaseInitialization.then((value) async {
      // Setup Firebase Cloud Messaging
      await _initializeFirebaseMessaging();

      // Setup and fetch Remote Config
      await _initializeRemoteConfig();

      // Initialize AuthController
      try {
        Get.put(AuthController());
      } catch (e) {
        // If AuthController fails, try to continue anyway
        if (!Get.isRegistered<AuthController>()) {
          Get.put(AuthController());
        }
      }
    });
  }

  /// Initialize Firebase Cloud Messaging for notifications
  static Future<void> _initializeFirebaseMessaging() async {
    try {
      // Register background message handler
      FirebaseMessaging.onBackgroundMessage(backgroundHandler);

      // Setup platform-specific notification handling
      if (!kIsWeb) {
        if (Platform.isAndroid || Platform.isIOS) {
          if (Platform.isAndroid) {
            await androidNotification();
          } else {
            await appleNotification();
          }
        }
      }
    } catch (e) {
      // FCM errors are non-fatal
    }
  }

  /// Initialize and fetch Firebase Remote Config
  static Future<void> _initializeRemoteConfig() async {
    try {
      await remoteConfig.setConfigSettings(
        RemoteConfigSettings(
          fetchTimeout: const Duration(seconds: 10),
          minimumFetchInterval: Duration.zero,
        ),
      );

      // Fetch remote config with error handling
      try {
        await remoteConfig.fetchAndActivate();
      } catch (e) {
        // Continue even if remote config fails
      }

      // Apply remote config values
      await _applyRemoteConfigValues();
    } catch (e) {
      // Remote config errors are handled gracefully
    }
  }

  /// Apply values from Remote Config to app configuration
  static Future<void> _applyRemoteConfigValues() async {
    try {
      // API URL
      if (MahasConfig.urlApi.isEmpty) {
        MahasConfig.urlApi = remoteConfig.getString('api');
      }

      // App name
      await _configureAppName();

      // Profile menu
      await _configureProfileMenu();

      // Kadiv manager
      await _configureKadivManager();

      // History profile
      await _configureHistoryProfile();

      // Theme colors
      await _configureThemeColors();

      // Update app values
      _configureUpdateValues();

      // Form dinamis
      _configureDinamisForm();

      // Internet error messages
      _configureInternetErrorMessages();
    } catch (e) {
      // Continue with defaults if remote config application fails
    }
  }

  /// Configure app name from cache or remote config
  static Future<void> _configureAppName() async {
    String? appName = MahasStorage.getAppName();
    if (appName != null) {
      MahasConfig.appName = appName;
    } else {
      String remoteAppName = remoteConfig.getString("app_name");
      if (remoteAppName.isNotEmpty) {
        MahasConfig.appName = remoteAppName;
        MahasStorage.setAppName(MahasConfig.appName);
      }
    }
  }

  /// Configure profile menu from cache or remote config
  static Future<void> _configureProfileMenu() async {
    try {
      // Always prefer remote data when online
      if (MahasConfig.hasInternet == true) {
        String profileMenu = remoteConfig.getString("profile_menu");
        if (profileMenu.isNotEmpty) {
          MahasConfig.profileMenu = ProfileMenuModel.fromString(profileMenu);
          // Cache the latest data
          MahasStorage.setProfileMenu(MahasConfig.profileMenu);
          return;
        }
      }

      // Fallback to cached data if remote failed or offline
      ProfileMenuModel? profileMenuModel = MahasStorage.getProfileMenu();
      if (profileMenuModel != null) {
        MahasConfig.profileMenu = profileMenuModel;
      }
    } catch (e) {
      // On error, try to load from cache if not already loaded
      ProfileMenuModel? profileMenuModel = MahasStorage.getProfileMenu();
      if (profileMenuModel != null) {
        MahasConfig.profileMenu = profileMenuModel;
      }
    }
  }

  /// Configure kadiv manager from cache or remote config
  static Future<void> _configureKadivManager() async {
    try {
      // Always prefer remote data when online
      if (MahasConfig.hasInternet == true) {
        dynamic kadivManager = remoteConfig.getString("kadiv_manager");
        if (kadivManager.isNotEmpty) {
          MahasConfig.kadivmanager = KadivmanagerModel.fromJson(kadivManager);
          // Cache the latest data
          MahasStorage.setKadivManagerMenu(MahasConfig.kadivmanager);
          return;
        }
      }

      // Fallback to cached data if remote failed or offline
      KadivmanagerModel? kadivmanagerModel = MahasStorage.getKadivManagerMenu();
      if (kadivmanagerModel != null) {
        MahasConfig.kadivmanager = kadivmanagerModel;
      }
    } catch (e) {
      // On error, try to load from cache if not already loaded
      KadivmanagerModel? kadivmanagerModel = MahasStorage.getKadivManagerMenu();
      if (kadivmanagerModel != null) {
        MahasConfig.kadivmanager = kadivmanagerModel;
      }
    }
  }

  /// Configure history profile setting from cache or remote config
  static Future<void> _configureHistoryProfile() async {
    bool? historyProfile = MahasStorage.getHistoryProfile();
    if (historyProfile != null) {
      MahasConfig.hasHistory = historyProfile;
    } else {
      MahasConfig.hasHistory = remoteConfig.getBool('history_profile');
      MahasStorage.setHistoryProfile(MahasConfig.hasHistory);
    }
  }

  /// Configure theme colors from cache or remote config
  static Future<void> _configureThemeColors() async {
    ColorThemeModel? colorThemeModel = MahasStorage.getColorTheme();
    if (colorThemeModel != null) {
      _applyColorTheme(colorThemeModel);
    } else {
      String colorThemeRemoteConfig = remoteConfig.getString("theme_color");
      if (colorThemeRemoteConfig.isNotEmpty) {
        ColorThemeModel colorTheme =
            ColorThemeModel.fromJson(colorThemeRemoteConfig);
        MahasStorage.setColorTheme(colorTheme);
        _applyColorTheme(colorTheme);
      }
    }
  }

  /// Apply color theme values to app colors
  static void _applyColorTheme(ColorThemeModel colorTheme) {
    if (colorTheme.primary != null) {
      MahasColors.primary = Color(int.parse(colorTheme.primary!));
    }
    if (colorTheme.danger != null) {
      MahasColors.danger = Color(int.parse(colorTheme.danger!));
    }
    if (colorTheme.warning != null) {
      MahasColors.warning = Color(int.parse(colorTheme.warning!));
    }
  }

  /// Configure update app values from remote config
  static void _configureUpdateValues() {
    String updateRemote = remoteConfig.getString("update_app_values");
    if (updateRemote.isNotEmpty) {
      MahasConfig.updateAppValues = UpdateappvaluesModel.fromJson(updateRemote);
    }
  }

  /// Configure form dinamis from remote config
  static void _configureDinamisForm() {
    String formDinamis = remoteConfig.getString("form_dinamis");
    if (formDinamis.isNotEmpty) {
      MahasConfig.dinamisForm = DinamisFormModel.fromJson(formDinamis);
    }
  }

  /// Configure internet error messages from remote config
  static void _configureInternetErrorMessages() {
    String noInternetRemoteConfig =
        remoteConfig.getString("no_internet_error_message");
    if (noInternetRemoteConfig.isNotEmpty) {
      try {
        List<dynamic> dataNoInternet = jsonDecode(noInternetRemoteConfig);
        if (dataNoInternet.isNotEmpty) {
          List<String> strlist = dataNoInternet.cast<String>();
          MahasConfig.noInternetErrorMessage.clear();
          MahasConfig.noInternetErrorMessage.addAll(strlist);
        }
      } catch (e) {
        // Use default error messages
      }
    }
  }

  /// Set up Android notifications
  static Future<void> androidNotification() async {
    try {
      //terminated state
      FirebaseMessaging.instance.getInitialMessage().then((message) {});

      //foreground state
      FirebaseMessaging.onMessage.listen((message) {
        LocalNotificationService.showNotificatiOnForeground(message);
      });

      //background state
      FirebaseMessaging.onMessageOpenedApp.listen((message) {
        LocalNotificationService.showNotificatiOnForeground(message);
      });
    } catch (e) {
      // Notification errors are non-fatal
    }
  }

  /// Set up iOS notifications
  static Future<void> appleNotification() async {
    try {
      late final FirebaseMessaging messaging = FirebaseMessaging.instance;
      NotificationSettings settings = await messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        // For handling the received notifications
        //terminated state
        FirebaseMessaging.instance.getInitialMessage().then((message) {
          //   showSimpleNotification(
          //     Text("${message?.notification!.title}"),
          //     subtitle: Text("${message?.notification!.body}"),
          //     background: MahasColors.lightBlue,
          //     duration: const Duration(seconds: 3),
          //  );
        });

        //foreground state
        await FirebaseMessaging.instance
            .setForegroundNotificationPresentationOptions(
          alert: true,
          badge: true,
          sound: true,
        );

        //foreground state
        FirebaseMessaging.onMessage.listen((message) {
          LocalNotificationService.showNotificatiOnForeground(message);
          showSimpleNotification(
            Text("${message.notification!.title}"),
            subtitle: Text("${message.notification!.body}"),
            background: MahasColors.primary,
            duration: const Duration(seconds: 3),
          );
        });

        //background state
        FirebaseMessaging.onMessageOpenedApp.listen((message) {
          LocalNotificationService.showNotificatiOnForeground(message);
        });
      }
    } catch (e) {
      // Notification errors are non-fatal
    }
  }
}

/// Custom HTTP client that accepts all certificates
class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}
