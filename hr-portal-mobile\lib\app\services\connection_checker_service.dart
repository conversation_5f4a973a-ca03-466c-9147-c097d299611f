// ignore_for_file: unused_local_variable

import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

import '../mahas/mahas_colors.dart';
import '../mahas/mahas_config.dart';
import '../mahas/mahas_service.dart';

class InternetConnectionService {
  static Future<bool> isConnectedToInternetUsingWeb() async {
    final connection = InternetConnection.createInstance(
      customCheckOptions: [
        InternetCheckOption(
          uri: Uri.parse('https://google.com'),
        ),
      ],
    );
    var hasInternet = await connection.hasInternetAccess;
    return hasInternet;
  }

  //cek koneksi internet
  static Future<void> isInternet() async {
    var connectivityResult = await Connectivity().checkConnectivity();
    bool hasInternet = false;

    for (var result in connectivityResult) {
      if (result == ConnectivityResult.mobile ||
          result == ConnectivityResult.wifi) {
        hasInternet = await isConnectedToInternetUsingWeb();
      } else if (result == ConnectivityResult.none) {
        hasInternet = false;
      }
    }

    MahasConfig.hasInternet = hasInternet;
  }

  //continous checking
  static Future<void> checkInternet() async {
    Timer? timer;
    bool isSnackBarShown = false;
    timer = Timer.periodic(const Duration(seconds: 3), (_) async {
      await isInternet();

      if (MahasConfig.urlApi == '' && MahasConfig.hasInternet == true) {
        EasyLoading.show();
        await MahasService.initApp();
        EasyLoading.dismiss();
      }

      if (MahasConfig.hasInternet == false && !isSnackBarShown) {
        isSnackBarShown = true;
        ScaffoldMessenger.of(Get.context!).showSnackBar(
          SnackBar(
            duration: const Duration(days: 365),
            elevation: 0,
            content: Container(
              padding: const EdgeInsets.all(5),
              child: Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: MahasColors.dark,
                  borderRadius: BorderRadius.circular(15),
                ),
                child: const Center(
                  child: Text(
                    'Tidak ada koneksi internet',
                    style: TextStyle(
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
            backgroundColor: Colors.transparent,
          ),
        );
      } else if (MahasConfig.hasInternet == true && isSnackBarShown) {
        isSnackBarShown = false;
        ScaffoldMessenger.of(Get.context!).hideCurrentSnackBar();
      }
    });
  }
}
