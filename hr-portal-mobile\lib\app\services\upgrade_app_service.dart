import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:get_storage/get_storage.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:url_launcher/url_launcher.dart';

import '../mahas/services/helper.dart';

class UpgradeAppService {
  static final storage = GetStorage();

  static Future<void> checkingUpdateApp() async {
    final updateLater = storage.read('update_later');
    final now = DateTime.now();
    final updateLaterDate =
        updateLater == null ? null : DateTime.parse(updateLater);
    String versi =
        "${MahasConfig.packageInfo!.version}+${MahasConfig.packageInfo!.buildNumber}";

    if ((!kIsWeb && updateLaterDate?.isAfter(now) == false) ||
        updateLater == null) {
      if (Platform.isIOS || Platform.isAndroid) {
        if (versi != MahasConfig.updateAppValues.version) {
          final r = await Helper.dialogUpdate(
              harusUpdate: MahasConfig.updateAppValues.mustUpdate ?? false,
              versiTerbaru: MahasConfig.updateAppValues.version ?? "");
          if (r == true) {
            await launchUrl(
                    Uri.parse(MahasConfig.updateAppValues.urlUpdate ?? ""),
                    mode: LaunchMode.externalApplication)
                .then((value) => {
                      if (Platform.isAndroid)
                        {
                          SystemNavigator.pop(),
                        }
                      else if (Platform.isIOS)
                        {
                          exit(0),
                        }
                    });
          } else {
            storage.write(
                'update_later',
                now
                    .add(Duration(
                        days: MahasConfig.updateAppValues.dismissDuration ?? 7))
                    .toString());
          }
        }
      }
    }
  }
}
