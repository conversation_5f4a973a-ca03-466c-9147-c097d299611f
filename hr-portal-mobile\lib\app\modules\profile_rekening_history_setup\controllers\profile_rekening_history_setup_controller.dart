import 'dart:convert';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_checkbox_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/models/api_list_resut_model.dart';
import '../../../mahas/models/api_result_model.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../models/bank_model.dart';
import '../../../models/profile_rekening_model.dart';
import '../../../routes/app_pages.dart';

class ProfileRekeningHistorySetupController extends GetxController {
  late SetupPageController formCon;
  late bool reqDelete;
  late bool showStatus;
  late bool withActionBack;

  final noRekeningCon = InputTextController();
  final namaBankCon = InputDropdownController();
  final atasNamaCon = InputTextController();
  final aktifCon = InputCheckboxController();
  final prioritasCon = InputCheckboxController();
  final alasanTolakCon = InputTextController();

  RxBool approvalButtonsVisible = false.obs;
  RxBool approvalFromNotif = false.obs;
  RxBool allowED = false.obs;
  late dynamic statusApprovalHRD = null.obs;
  RxString status = "".obs;
  RxString namaHRD = "".obs;
  RxString namaPegawaiRequest = "".obs;
  RxString alasanTolakHRD = "".obs;
  Rx<DateTime> tglPermintaan = DateTime.now().obs;
  RxInt idPegawaiRekening = 0.obs;

  @override
  void onInit() async {
    //init
    String notifParam = Get.parameters['approval'].toString();
    approvalFromNotif.value = notifParam == "true" ? true : false;
    String showStatusParam = Get.parameters['showStatus'].toString();
    showStatus = showStatusParam == "true" ? true : false;
    String withActionBackParam = Get.parameters['withActionBack'].toString();
    withActionBack = withActionBackParam == "true" ? true : false;

    //formcon
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PegawaiRekening/History/$id',
      urlApiPost: () => '/api/PegawaiRekening/History',
      urlApiPut: (id) => '/api/PegawaiRekening/History/$id',
      urlApiDelete: (id) =>
          '/api/PegawaiRekening/History?id=$id&idDivisi=${MahasConfig.selectedDivisi}',
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "noRekening": noRekeningCon.value,
        "Id_Bank": namaBankCon.value,
        "atasNama": atasNamaCon.value,
        "aktif": aktifCon.checked,
        "prioritas": prioritasCon.checked,
        "id_PegawaiRekening": idPegawaiRekening.value,
      },
      allowDelete: allowED.value,
      allowEdit: allowED.value,
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      apiToView: (json) {
        ProfileRekeningModel model = ProfileRekeningModel.fromJson(json);

        //mahasComponent
        noRekeningCon.value = model.norekening;
        namaBankCon.value = model.idBank;
        atasNamaCon.value = model.nama;
        if (model.aktif == true) {
          aktifCon.checked = true;
        } else {
          aktifCon.checked = false;
        }
        if (model.prioritas == true) {
          prioritasCon.checked = true;
        } else {
          prioritasCon.checked = false;
        }

        //display non mahas
        namaHRD.value = model.pegawaiapprovalnama ?? "";
        alasanTolakHRD.value = model.alasantolak ?? "";
        namaPegawaiRequest.value = model.pegawainama ?? "";
        statusApprovalHRD = model.approval;
        tglPermintaan.value = model.tglpermintaan ?? DateTime.now();
        idPegawaiRekening.value = model.id ?? 0;
        reqDelete = model.status == "DELETE" ? true : false;
        status.value = model.status!;

        //cek approval for allowedit or delete
        cekApproval();

        //approval
        if (approvalFromNotif.value && statusApprovalHRD == null) {
          approvalButtonsVisible.value = true;
        }
      },
      onBeforeSubmit: () {
        if (!noRekeningCon.isValid) return false;
        if (!namaBankCon.isValid) return false;
        if (!atasNamaCon.isValid) return false;

        return true;
      },
      onInit: () async {
        await getBank();
      },
    );

    super.onInit();
  }

  Future<void> getBank() async {
    if (EasyLoading.isShow) {
      EasyLoading.dismiss();
    }
    EasyLoading.show();
    var r = await HttpApi.get("/api/Bank");
    if (r.success) {
      RxList<BankModel> listModel = RxList<BankModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(BankModel.fromDynamic(e));
      }
      if (namaBankCon.items.isNotEmpty) {
        namaBankCon.items.clear();
      }
      if (listModel.isNotEmpty && namaBankCon.items.isEmpty) {
        namaBankCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      }
    }
    EasyLoading.dismiss();
  }

  //backAction
  Future<bool> _back() async {
    Get.until((route) => route.settings.name == Routes.PROFILE_REKENING);
    Get.toNamed(Routes.PROFILE_REKENING_HISTORY);
    return true;
  }

  //cek approval parameters come from notifikasi
  void cekApproval() {
    if (approvalFromNotif.value || statusApprovalHRD != null) {
      allowED.value = false;
    } else {
      allowED.value = true;
    }
    if (withActionBack) {
      formCon.backAction = () {
        return _back();
      };
      formCon.deleteOnTap = (id) {
        return _back();
      };
    }
    formCon.setState(() {
      reqDelete ? formCon.allowEdit = false : formCon.allowEdit = allowED.value;
      formCon.allowDelete = allowED.value;
      if (reqDelete) {
        formCon.deleteCaption = "Batal Hapus";
        formCon.deleteDescription =
            "Yakin akan membatalkan permintaan hapus data ini?";
      }
    });
  }

  Future<void> approvalAction(bool approve) async {
    if (EasyLoading.isShow) {
      EasyLoading.dismiss();
    }
    EasyLoading.show();
    String id = Get.parameters['id'].toString();
    final body = {
      'approve': approve,
      'alasanTolak': alasanTolakCon.value,
    };
    ApiResultModel? r;
    String url = '/api/PegawaiRekening/History/Approve/$id';
    r = await HttpApi.put(
      url,
      body: body,
    );
    if (r.success) {
      r = await HttpApi.get(
        '/api/PegawaiRekening/History/$id',
      );
      if (r.success) {
        ProfileRekeningModel model = ProfileRekeningModel.fromJson(r.body);
        statusApprovalHRD = model.approval;
        alasanTolakHRD.value = model.alasantolak ?? "";
        approvalButtonsVisible.value = false;
        update();
      }
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }
}
