import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/models/api_list_resut_model.dart';
import '../../../mahas/services/http_api.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../models/jenis_sertifikasi_model.dart';
import '../../../models/profile_sertifikasi_model.dart';
import '../../../routes/app_pages.dart';

class ProfileJenisSertifikasiSetupController extends GetxController {
  late SetupPageController formCon;

  final jenisSertifikasiCon = InputDropdownController();
  final namaSertifikasiCon = InputTextController();
  final noSertifikasiCon = InputTextController();
  final tanggalCon = InputDatetimeController();
  final berlakuSampaiCon = InputDatetimeController();
  final berkasPendukungCon = InputFileController(
    type: FileType.custom,
    extension: ['pdf'],
    tipe: InputFileType.pdf,
  );

  RxInt idPegawaiSertifikasi = 0.obs;

  @override
  void onInit() {
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PegawaiSertifikasi/$id',
      urlApiPost: () => '/api/PegawaiSertifikasi',
      urlApiPut: (id) => '/api/PegawaiSertifikasi/$id',
      urlApiDelete: (id) => '/api/PegawaiSertifikasi/$id',
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "id_JenisSertifikasi": jenisSertifikasiCon.value,
        "nama": namaSertifikasiCon.value,
        "noSertifikasi": noSertifikasiCon.value,
        "tanggal": MahasFormat.dateToString(tanggalCon.value),
        "berlakuSampai": MahasFormat.dateToString(berlakuSampaiCon.value),
        "berkasPendukung": berkasPendukungCon.value,
        "id_PegawaiSertifikasi": idPegawaiSertifikasi.value,
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (!jenisSertifikasiCon.isValid) return false;
        if (!namaSertifikasiCon.isValid) return false;
        if (!noSertifikasiCon.isValid) return false;
        if (!tanggalCon.isValid) return false;
        if (!berlakuSampaiCon.isValid) return false;
        if (!berkasPendukungCon.isValid) return false;

        return true;
      },
      apiToView: (json) {
        SertifikasiModel model = SertifikasiModel.fromJson(json);

        //mahascomponent
        jenisSertifikasiCon.value = model.idJenissertifikasi;
        namaSertifikasiCon.value = model.nama;
        noSertifikasiCon.value = model.nosertifikasi;
        tanggalCon.value = model.tanggal;
        berlakuSampaiCon.value = model.berlakusampai;
        berkasPendukungCon.value = model.berkaspendukung;

        //non mahas
        idPegawaiSertifikasi.value = model.id ?? 0;
      },
      onInit: () async {
        await getSertifikasi();
      },
    );

    if (MahasConfig.hasHistory) {
      formCon.onSuccessSubmit = (r) {
        var idHistory = json.decode(r.body)['id'];
        Get.toNamed(
          Routes.PROFILE_JENIS_SERTIFIKASI_HISTORY_SETUP,
          parameters: {
            'id': idHistory.toString(),
            'showStatus': true.toString(),
            'withActionBack': true.toString(),
          },
        );
      };
      formCon.deleteOnTap = (id) {
        Get.toNamed(
          Routes.PROFILE_JENIS_SERTIFIKASI_HISTORY_SETUP,
          parameters: {
            'id': id.toString(),
            'showStatus': true.toString(),
            'withActionBack': true.toString(),
          },
        );
      };
    }

    super.onInit();
  }

  Future<void> getSertifikasi() async {
    if (EasyLoading.isShow) {
      EasyLoading.dismiss();
    }
    EasyLoading.show();
    var r = await HttpApi.get("/api/PegawaiSertifikasi/List/JenisSertifikasi");
    if (r.success) {
      RxList<JenisSertifikasiModel> listModel = RxList<JenisSertifikasiModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(JenisSertifikasiModel.fromDynamic(e));
      }
      if (jenisSertifikasiCon.items.isNotEmpty) {
        jenisSertifikasiCon.items.clear();
      }
      if (listModel.isNotEmpty && jenisSertifikasiCon.items.isEmpty) {
        jenisSertifikasiCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      }
    }
    EasyLoading.dismiss();
  }
}
