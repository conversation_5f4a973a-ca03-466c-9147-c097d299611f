import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';
import 'package:intl/intl.dart';

List<AbsensiModel> absensiModelFromJson(String str) => List<AbsensiModel>.from(
    json.decode(str).map((x) => AbsensiModel.fromJson(x)));

class AbsensiModel {
  int? id;
  String? lokasiabsensi;
  DateTime? tanggaljam;
  String? status;
  double? latitude;
  double? longitude;

  AbsensiModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static AbsensiModel fromDynamic(dynamic dynamicData) {
    final model = AbsensiModel();

    model.id = dynamicData['id'];
    model.lokasiabsensi = dynamicData['lokasiAbsensi'];
    model.tanggaljam = MahasFormat.stringToDateTime(dynamicData['tanggalJam']);
    model.status = dynamicData['status'];
    model.latitude = dynamicData['latitude'];
    model.longitude = dynamicData['longitude'];

    return model;
  }
}

// To parse this JSON data, do
//
//     final absen = absenFromJson(jsonString);

List<Absen> absenFromJson(String str) =>
    List<Absen>.from(json.decode(str).map((x) => Absen.fromJson(x)));

class Absen {
  Absen({
    this.id,
    this.lokasiAbsensi,
    this.tanggalJam,
    this.status,
    this.latitude,
    this.longitude,
  });

  int? id;
  String? lokasiAbsensi;
  DateTime? tanggalJam;
  String? status;
  double? latitude;
  double? longitude;

  factory Absen.fromJson(Map<String, dynamic> json) => Absen(
        id: json["id"],
        lokasiAbsensi: json["lokasiAbsensi"],
        tanggalJam: DateTime.parse(json["tanggalJam"]),
        status: json["status"],
        latitude: json["latitude"],
        longitude: json["longitude"],
      );
}

class AbsensifotoModel {
  int? id;
  int? idPegawai;
  int? idLokasiabsensi;
  int? idUnitbisnis;
  DateTime? tanggaljam;
  String? status;
  double? latitude;
  double? longitude;
  String? device;
  String? os;
  String? pathfoto;
  String? namapegawai;

  AbsensifotoModel();

  static AbsensifotoModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static AbsensifotoModel fromDynamic(dynamic dynamicData) {
    final model = AbsensifotoModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.idLokasiabsensi =
        MahasFormat.dynamicToInt(dynamicData['id_LokasiAbsensi']);
    model.idUnitbisnis = MahasFormat.dynamicToInt(dynamicData['id_UnitBisnis']);
    model.tanggaljam = MahasFormat.dynamicToDateTime(dynamicData['tanggalJam']);
    model.status = dynamicData['status'];
    model.latitude = dynamicData['latitude'];
    model.longitude = dynamicData['longitude'];
    model.device = dynamicData['device'];
    model.os = dynamicData['os'];
    model.pathfoto = dynamicData['pathFoto'];
    model.namapegawai = dynamicData['namaPegawai'];

    return model;
  }
}

class AbsensiFacereCognitionModel {
  int? id;
  int? idUnitbisnis;
  int? idPegawai;
  DateTime? tanggaljam;
  String? kodemesin;
  String? status;
  String? urlfoto;

  AbsensiFacereCognitionModel();

  static AbsensiFacereCognitionModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static AbsensiFacereCognitionModel fromDynamic(dynamic dynamicData) {
    final model = AbsensiFacereCognitionModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idUnitbisnis = MahasFormat.dynamicToInt(dynamicData['id_UnitBisnis']);
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.tanggaljam = MahasFormat.dynamicToDateTime(dynamicData['tanggalJam']);
    model.kodemesin = dynamicData['kodeMesin'];
    model.status = dynamicData['status'];
    model.urlfoto = dynamicData['urlFoto'];

    return model;
  }
}

class AbsensiLemburModel {
  int? idPenugasanLembur;
  DateTime? tanggalLembur;
  TimeOfDay? jamLembur;
  int? lamaHari;
  int? lamaJam;
  int? lamaMenit;

  AbsensiLemburModel();

  static AbsensiLemburModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static AbsensiLemburModel fromDynamic(dynamic dynamicData) {
    final model = AbsensiLemburModel();

    model.idPenugasanLembur =
        MahasFormat.dynamicToInt(dynamicData['id_PenugasanLembur']);
    model.tanggalLembur =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalLembur']);
    model.jamLembur = MahasFormat.stringToTime(dynamicData['jamLembur']);
    model.lamaHari = dynamicData['lamaHari'];
    model.lamaJam = dynamicData['lamaJam'];
    model.lamaMenit = dynamicData['lamaMenit'];

    return model;
  }
}

class AbsensiIstirahatModel {
  double? latitude;
  double? longitude;
  bool? masuk;
  DateTime? tanggalJam;
  String? device;
  String? os;

  AbsensiIstirahatModel(
      {this.latitude,
      this.longitude,
      this.masuk,
      this.tanggalJam,
      this.device,
      this.os});

  static AbsensiIstirahatModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static AbsensiIstirahatModel fromDynamic(dynamic dynamicData) {
    return AbsensiIstirahatModel(
      latitude: dynamicData['latitude'],
      longitude: dynamicData['longitude'],
      masuk: dynamicData['masuk'],
      device: dynamicData['device'],
      os: dynamicData['os'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'masuk': masuk,
      'device': device,
      'os': os,
    };
  }
}

class AbsensiQrModel {
  String? absensi;
  DateTime? berlaku;

  AbsensiQrModel();

  static AbsensiQrModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static AbsensiQrModel fromDynamic(dynamic dynamicData) {
    DateFormat format = DateFormat("dd-MM-yyyy HH:mm:ss");
    final model = AbsensiQrModel();

    model.absensi = dynamicData['absensi'];
    model.berlaku = format.parse(dynamicData['qrberlaku']);

    return model;
  }
}
