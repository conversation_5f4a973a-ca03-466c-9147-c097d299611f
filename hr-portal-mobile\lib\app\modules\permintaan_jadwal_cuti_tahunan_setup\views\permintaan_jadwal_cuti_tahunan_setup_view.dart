import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_datetime_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_detail_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_detail_setup_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_text_component.dart';
import 'package:hr_portal/app/mahas/components/mahas_themes.dart';
import 'package:hr_portal/app/mahas/components/pages/setup_page_component.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:hr_portal/app/models/cuti_tahunan_model.dart';

import '../../../mahas/services/helper.dart';
import '../../../mahas_complement/status_request.dart';
import '../controllers/permintaan_jadwal_cuti_tahunan_setup_controller.dart';

class PermintaanJadwalCutiTahunanSetupView
    extends GetView<PermintaanJadwalCutiTahunanSetupController> {
  const PermintaanJadwalCutiTahunanSetupView({super.key});

  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      title: "Cuti Tahunan",
      controller: controller.formCon,
      childrenPadding: false,
      children: () => [
        Visibility(
          visible: controller.formCon.isState != SetupPageState.create,
          child: Column(
            children: [
              Obx(() => Text(
                  "Tanggal/Waktu pengajuan : ${controller.tglPengajuan.value}")),
              const SizedBox(
                height: 10,
              ),
            ],
          ),
        ),
        Visibility(
          visible: MahasConfig.dinamisForm.cutiTahunan?.quotaVisible == true &&
              controller.formCon.isState == SetupPageState.create,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(
                      width: 180,
                      child: InputDatetimeComponent(
                        label: "Hak Cuti",
                        controller: controller.tanggalMulaiCon,
                        editable: false,
                      ),
                    ),
                    SizedBox(
                      width: 180,
                      child: InputDatetimeComponent(
                        label: "Berakhir Pada",
                        controller: controller.tanggalSelesaiCon,
                        editable: false,
                      ),
                    ),
                  ],
                ),
                InputTextComponent(
                  label: "Quota Cuti",
                  controller: controller.jumlahCutiCon,
                  editable: false,
                ),
                Visibility(
                  visible: controller.sisaCutiCon.value != 0,
                  child: Column(
                    children: [
                      InputTextComponent(
                        label: "Sisa Cuti Sebelumnya",
                        controller: controller.sisaCutiCon,
                        editable: false,
                      ),
                      InputDatetimeComponent(
                        label: "Tanggal Berakhir Sisa Cuti Sebelumnya",
                        controller: controller.tanggalSisaSelesaiCon,
                        editable: false,
                      ),
                      InputTextComponent(
                        label: "Total Quota Cuti",
                        controller: controller.totalCutiCon,
                        editable: false,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        controller.isKadiv == true &&
                controller.formCon.isState == SetupPageState.detail
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: InputTextComponent(
                      label: "Pegawai Request",
                      controller: controller.pegawaiCutiCon,
                      editable: false,
                      marginBottom: 5,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: InkWell(
                      onTap: () async => await Helper.fotoProfilOnTap(
                          controller.idPegawaiRequest.value),
                      child: SizedBox(
                        height: 30,
                        child: Text(
                          "Lihat Foto Profil Pegawai",
                          style: MahasThemes.link,
                          textAlign: TextAlign.left,
                        ),
                      ),
                    ),
                  ),
                ],
              )
            : const SizedBox(),
        controller.formCon.isState == SetupPageState.detail
            ? InputDetailComponent(
                label: "Tanggal",
                editable: false,
                controller: controller.detailApproveCon,
              )
            : InputDetailSetupComponent<CutitahunanDetailModel>(
                label: "Tanggal",
                controller: controller.detailSetupCon,
                editable: controller.formCon.editable,
                required: true,
                formBuilder: (e) => Column(
                  children: [
                    // form detail
                    InputDatetimeComponent(
                      label: "Cuti Tanggal",
                      controller: controller.tanggalCon,
                      required: true,
                      editable: controller.formCon.editable,
                    ),
                  ],
                ),
              ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Column(
            children: [
              Obx(() => Visibility(
                    visible: controller.jumlahHari.value > 0,
                    child: InputTextComponent(
                      label: "Jumlah Hari",
                      required: false,
                      controller: controller.berapaHariCon,
                      editable: false,
                    ),
                  )),
              InputTextComponent(
                label: "Alasan",
                required: true,
                controller: controller.alasanCon,
                editable: controller.formCon.editable,
              ),
            ],
          ),
        ),
        Obx(() => ApprovalStatusContainer(
              padding: 10,
              isEditable: controller.formCon.editable,
              isVisible: controller.isVisible,
              isBatal: controller.batal,
              allowED: controller.allowED,
              isDinamisApprove: controller.isDinamisApprove,
              alasanTolakController: controller.alasanTolakCon,
              onTolakPressed: () {
                controller.approvalOnPress(false);
              },
              onTerimaPressed: () {
                controller.approvalOnPress(true);
              },
              batalOnPress: () {
                dialogBatal();
              },
              pegawaiKadiv: controller.pegawaiKadiv.value,
              pegawaiManager: controller.pegawaiManager.value,
              accKadiv: controller.accKadiv.value,
              accManager: controller.accManager.value,
              statusTolak: controller.statusTolak.value,
              modelApproval: controller.modelApproval,
              isStop: controller.isStop.value,
            )),
      ],
    );
  }

  Future<dynamic> dialogBatal() {
    return Get.dialog(
      AlertDialog(
        content: SizedBox(
          width: 1000,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              InputTextComponent(
                label: "Alasan",
                required: true,
                controller: controller.alasanBatalCon,
              ),
            ],
          ),
        ),
        contentPadding:
            const EdgeInsets.only(bottom: 0, top: 20, right: 10, left: 10),
        actionsPadding:
            const EdgeInsets.only(top: 0, bottom: 0, left: 10, right: 10),
        actions: [
          TextButton(
            child: Text(
              "Close",
              style: MahasThemes.muted,
            ),
            onPressed: () {
              Get.back(result: false);
            },
          ),
          TextButton(
            child: const Text(
              "Simpan",
            ),
            onPressed: () {
              controller.batalOnPress();
            },
          ),
        ],
      ),
    );
  }
}
