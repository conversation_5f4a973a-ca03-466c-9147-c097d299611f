import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/others/list_component.dart';
import 'package:hr_portal/app/models/approval_model.dart';
import 'package:hr_portal/app/routes/app_pages.dart';

import '../../../mahas/mahas_config.dart';

class MenuItemModel {
  final String title;
  final GestureTapCallback onTab;

  MenuItemModel(this.title, this.onTab);
}

class ApprovalController extends GetxController {
  final listCon = ListComponentController<ApprovalModel>(
    urlApi: (index, filter) =>
        '/api/PermintaanJadwal/Approval/List?pageIndex=$index&status=menunggu',
    fromDynamic: ApprovalModel.fromDynamic,
    allowSearch: false,
  );

  RxInt selectedIndex = 0.obs;
  final List<MenuItemModel> menus = [];
  RxBool isKadiv = MahasConfig.atasan.isKadiv?.obs ?? false.obs;
  var model = Rxn<ApprovalModel>();
  bool generated = false;

  @override
  void onInit() {
    menus.add(MenuItemModel('Menunggu', listMenunggu));
    menus.add(MenuItemModel('Diterima', listDiterima));
    menus.add(MenuItemModel('Ditolak', listDitolak));
    super.onInit();
  }

  void itemOnTab(int id, int jenis) {
    if (jenis == 0) {
      Get.toNamed(
        Routes.PERMINTAAN_JADWAL_TUKAR_JADWAL_SETUP,
        parameters: {
          'id': id.toString(),
          'approval': 'approval',
        },
      )?.then((value) {
        if (!value) {
          listCon.softRefresh();
        }
      });
    } else if (jenis == 1) {
      Get.toNamed(
        Routes.PERMINTAAN_JADWAL_CUTI_HAMIL_SETUP,
        parameters: {
          'id': id.toString(),
          'approval': 'approval',
        },
      )?.then((value) {
        if (!value) {
          listCon.softRefresh();
        }
      });
    } else if (jenis == 2) {
      Get.toNamed(
        Routes.PERMINTAAN_JADWAL_CUTI_SAKIT_SETUP,
        parameters: {
          'id': id.toString(),
          'approval': 'approval',
        },
      )?.then((value) {
        if (!value) {
          listCon.softRefresh();
        }
      });
    } else if (jenis == 3) {
      Get.toNamed(
        Routes.PERMINTAAN_JADWAL_CUTI_TAHUNAN_SETUP,
        parameters: {
          'id': id.toString(),
          'approval': 'approval',
          'isKadiv': isKadiv.value.toString(),
        },
      )?.then((value) {
        if (!value) {
          listCon.softRefresh();
        }
      });
    } else if (jenis == 4) {
      Get.toNamed(
        Routes.PERMINTAAN_JADWAL_CUTI_TUKAR_LEMBUR_SETUP,
        parameters: {
          'id': id.toString(),
          'approval': 'approval',
          'isKadiv': isKadiv.value.toString(),
        },
      )?.then((value) {
        if (!value) {
          listCon.softRefresh();
        }
      });
    } else if (jenis == 5) {
      Get.toNamed(
        Routes.PERMINTAAN_JADWAL_IZIN_SETUP,
        parameters: {
          'id': id.toString(),
          'approval': 'approval',
        },
      )?.then((value) {
        if (!value) {
          listCon.softRefresh();
        }
      });
    } else if (jenis == 6) {
      if (MahasConfig.profileMenu.customLemburPage) {
        Get.toNamed(Routes.PERMINTAAN_JADWAL_LEMBUR_CUSTOM_KEPALA_UNIT_SETUP,
            parameters: {
              'id': id.toString(),
              "approval": "approval",
            },
            arguments: {
              "pegawai": true
            })?.then((value) {
          if (!value) {
            listCon.softRefresh();
          }
        });
      } else {
        Get.toNamed(
          Routes.PERMINTAAN_JADWAL_LEMBUR_SETUP,
          parameters: {
            'id': id.toString(),
            'approval': 'approval',
          },
        )?.then((value) {
          if (!value) {
            listCon.softRefresh();
          }
        });
      }
    } else if (jenis == 7) {
      Get.toNamed(
        Routes.PERMINTAAN_JADWAL_CUTI_DAY_PAYMENT_SETUP,
        parameters: {
          'id': id.toString(),
          'approval': 'approval',
        },
      )?.then((value) {
        if (!value) {
          listCon.softRefresh();
        }
      });
    } else if (jenis == 8) {
      Get.toNamed(
        Routes.PERMINTAAN_JADWAL_CUTI_SETENGAH_HARI_SETUP,
        parameters: {
          'id': id.toString(),
          'approval': 'approval',
        },
      )?.then((value) {
        if (!value) {
          listCon.softRefresh();
        }
      });
    } else if (jenis == 9) {
      Get.toNamed(
        Routes.PERMINTAAN_JADWAL_CUTI_UNPAID_LEAVE_SETUP,
        parameters: {
          'id': id.toString(),
          'approval': 'approval',
        },
      )?.then((value) {
        if (!value) {
          listCon.softRefresh();
        }
      });
    }
  }

  displayJenis(int jenis) {
    String? data;
    if (jenis == 0) {
      data = "Permintaan Tukar Jadwal";
    } else if (jenis == 1) {
      data = MahasConfig.dinamisForm.cutiHamil?.tittle ?? "Cuti Hamil";
    } else if (jenis == 2) {
      data = MahasConfig.dinamisForm.cutiSakit?.tittle ?? "Cuti Sakit";
    } else if (jenis == 3) {
      data = "Cuti Tahunan";
    } else if (jenis == 4) {
      data = "Cuti Tukar Lembur";
    } else if (jenis == 5) {
      data = MahasConfig.dinamisForm.izin?.tittle ?? "Izin";
    } else if (jenis == 6) {
      data = "Lembur";
    } else if (jenis == 7) {
      data = "Day Paid";
    } else if (jenis == 8) {
      data = "Cuti Setengah Hari";
    } else if (jenis == 9) {
      data = "Unpaid Leave";
    }
    return data;
  }

  void listMenunggu() {
    listCon.urlApi = (index, filter) =>
        '/api/PermintaanJadwal/Approval/List?pageIndex=$index&status=menunggu';
    listCon.refresh();
  }

  void listDiterima() {
    listCon.urlApi = (index, filter) =>
        '/api/PermintaanJadwal/Approval/List?pageIndex=$index&status=diterima';
    listCon.refresh();
  }

  void listDitolak() {
    listCon.urlApi = (index, filter) =>
        '/api/PermintaanJadwal/Approval/List?pageIndex=$index&status=ditolak';
    listCon.refresh();
  }
}
