import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/others/list_component.dart';
import 'package:hr_portal/app/mahas/services/http_api.dart';
import 'package:hr_portal/app/models/notifikasi_model.dart';
import 'package:hr_portal/app/routes/app_pages.dart';

import '../../../mahas/mahas_config.dart';

class NotifikasiController extends GetxController {
  final listCon = ListComponentController<NotifikasiModel>(
    urlApi: (index, filter) => '/api/Notifikasi?pageIndex=$index',
    fromDynamic: NotifikasiModel.fromDynamic,
    allowSearch: false,
  );

  void addOnPress() {
    Get.toNamed(Routes.NOTIFIKASI_DETAIL)?.then((value) {
      if (value) {
        listCon.softRefresh();
      }
    });
  }

  void itemOnTab(int idMenu, String menu, int id, bool dibaca) async {
    final body = {};
    final url = '/api/Notifikasi/$id';
    await HttpApi.put(
      url,
      body: body,
    );
    if (menu == "Permintaan Cuti Hamil" ||
        menu == "Permintaan Istirahat Hamil") {
      Get.toNamed(
        Routes.PERMINTAAN_JADWAL_CUTI_HAMIL_SETUP,
        parameters: {
          'id': idMenu.toString(),
          'approval': 'approval',
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Permintaan Cuti Sakit" ||
        menu == "Permintaan Istirahat Sakit") {
      Get.toNamed(
        Routes.PERMINTAAN_JADWAL_CUTI_SAKIT_SETUP,
        parameters: {
          'id': idMenu.toString(),
          'approval': 'approval',
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Permintaan Cuti Tahunan") {
      Get.toNamed(
        Routes.PERMINTAAN_JADWAL_CUTI_TAHUNAN_SETUP,
        parameters: {
          'id': idMenu.toString(),
          'approval': 'approval',
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Permintaan Izin" || menu == "Permintaan Dispensasi") {
      Get.toNamed(
        Routes.PERMINTAAN_JADWAL_IZIN_SETUP,
        parameters: {
          'id': idMenu.toString(),
          'approval': 'approval',
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Permintaan Lembur") {
      Get.toNamed(
        Routes.PERMINTAAN_JADWAL_LEMBUR_SETUP,
        parameters: {
          'id': idMenu.toString(),
          'approval': 'approval',
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Permintaan Cuti Tukar Lembur") {
      Get.toNamed(
        Routes.PERMINTAAN_JADWAL_CUTI_TUKAR_LEMBUR_SETUP,
        parameters: {
          'id': idMenu.toString(),
          'approval': 'approval',
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Permintaan Tukar Jadwal") {
      Get.toNamed(
        Routes.PERMINTAAN_JADWAL_TUKAR_JADWAL_SETUP,
        parameters: {
          'id': idMenu.toString(),
          'approval': 'approval',
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Jadwal Final") {
      Get.toNamed(
        Routes.JADWAL,
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Absensi Tugas" || menu == "Presensi Tugas") {
      Get.toNamed(
        Routes.ABSEN_SURAT_TUGAS_SETUP,
        parameters: {
          'id': idMenu.toString(),
          'approval': 'approval',
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Jadwal Final") {
      Get.toNamed(
        Routes.JADWAL,
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "ETicket") {
      Get.toNamed(
        Routes.ETICKET,
        parameters: {
          'id': idMenu.toString(),
          'approval': 'approval',
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Slip Gaji") {
      Get.toNamed(
        Routes.GAJI_SETUP,
        parameters: {
          'id': idMenu.toString(),
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Permintaan Perubahan Data Pegawai") {
      Get.toNamed(
        Routes.PROFILE_DATA_PRIBADI_HISTORY,
        parameters: {
          'id': idMenu.toString(),
          'approval': true.toString(),
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Permintaan Perubahan Data Kontak Pegawai") {
      Get.toNamed(
        Routes.PROFILE_KONTAK_PEGAWAI_HISTORY_SETUP,
        parameters: {
          'id': idMenu.toString(),
          'approval': true.toString(),
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Permintaan Perubahan Data Keluarga Pegawai") {
      Get.toNamed(
        Routes.PROFILE_KELUARGA_HISTORY_SETUP,
        parameters: {
          'id': idMenu.toString(),
          'approval': true.toString(),
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Permintaan Perubahan Data Riwayat Kesehatan Pegawai") {
      Get.toNamed(
        Routes.PROFILE_RIWAYAT_KESEHATAN_HISTORY_SETUP,
        parameters: {
          'id': idMenu.toString(),
          'approval': true.toString(),
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Akhir Kontrak Kerja") {
      Get.toNamed(
        Routes.PROFILE_DATA_PRIBADI,
        parameters: {
          'akhirkontrak': true.toString(),
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Permintaan Perubahan Data Rekening Pegawai") {
      Get.toNamed(
        Routes.PROFILE_REKENING_HISTORY_SETUP,
        parameters: {
          'id': idMenu.toString(),
          'approval': true.toString(),
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Permintaan Perubahan Data Sertifikasi Pegawai") {
      Get.toNamed(
        Routes.PROFILE_JENIS_SERTIFIKASI_HISTORY_SETUP,
        parameters: {
          'id': idMenu.toString(),
          'approval': true.toString(),
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Permintaan Perubahan Foto Profile") {
      Get.toNamed(
        Routes.PROFILE_FOTO_HISTORY_SETUP,
        parameters: {
          'id': idMenu.toString(),
          'approval': true.toString(),
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Permintaan Perubahan Data Riwayat Pendidikan Pegawai") {
      Get.toNamed(
        Routes.PROFILE_RIWAYAT_PENDIDIKAN_HISTORY_SETUP,
        parameters: {
          'id': idMenu.toString(),
          'approval': true.toString(),
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Sertifikasi Pegawai Berakhir") {
      Get.toNamed(
        Routes.PROFILE_JENIS_SERTIFIKASI_SETUP,
        parameters: {
          'id': idMenu.toString(),
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Absensi") {
      Get.toNamed(
        Routes.ABSENSI_DETAIL,
        parameters: {
          'id': idMenu.toString(),
          'approval': true.toString(),
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Mutasi") {
      Get.toNamed(
        Routes.INFORMASI_SERAH_TERIMA_MUTASI_BARANG_SETUP,
        parameters: {
          'id': idMenu.toString(),
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Penugasan Lembur" || menu == "Lembur") {
      MahasConfig.profileMenu.customLemburPage
          ? Get.toNamed(
              Routes.PERMINTAAN_JADWAL_LEMBUR_CUSTOM_KEPALA_UNIT_SETUP,
              arguments: {"pegawai": true},
              parameters: {
                'id': idMenu.toString(),
                'approval': 'approval',
              },
            )?.then((value) {
              listCon.softRefresh();
            })
          : Get.toNamed(
              Routes.PERMINTAAN_JADWAL_LEMBUR_SETUP,
              parameters: {
                'id': idMenu.toString(),
              },
            )?.then((value) {
              if (value) {
                listCon.softRefresh();
              }
            });
    } else if (menu == "Permintaan Cuti Day Payment") {
      Get.toNamed(
        Routes.PERMINTAAN_JADWAL_CUTI_DAY_PAYMENT_SETUP,
        parameters: {
          'id': idMenu.toString(),
          'approval': 'approval',
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Permintaan Cuti Unpaid Leave" ||
        menu == "Permintaan Izin Tanpa Ditanggung Perusahaan") {
      Get.toNamed(
        Routes.PERMINTAAN_JADWAL_CUTI_UNPAID_LEAVE_SETUP,
        parameters: {
          'id': idMenu.toString(),
          'approval': 'approval',
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Permintaan Cuti Setengah Hari") {
      Get.toNamed(
        Routes.PERMINTAAN_JADWAL_CUTI_SETENGAH_HARI_SETUP,
        parameters: {
          'id': idMenu.toString(),
          'approval': 'approval',
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Pembuatan Jadwal") {
      Get.toNamed(Routes.NOTIFIKASI_DETAIL, parameters: {
        'message':
            "Jangan Lupa Untuk Membuatkan Jadwal dan Melakukan Finalisasi Jadwal Di Divisi Anda",
      })?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Akhir Kontrak Pegawai") {
      listCon.softRefresh();
    } else if (menu == "Pengumuman") {
      Get.toNamed(
        Routes.INFORMASI_PENGUMUMAN_SETUP,
        parameters: {
          'id': idMenu.toString(),
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else if (menu == "Peringatan") {
      Get.toNamed(
        Routes.INFORMASI_PERINGATAN_SETUP,
        parameters: {
          'id': idMenu.toString(),
        },
      )?.then((value) {
        listCon.softRefresh();
      });
    } else {
      Get.toNamed(
        Routes.NOTIFIKASI_DETAIL,
      )?.then((value) {
        listCon.softRefresh();
      });
    }
  }
}
