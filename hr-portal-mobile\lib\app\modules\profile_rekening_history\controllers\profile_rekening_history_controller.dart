import 'package:get/get.dart';
import 'package:hr_portal/app/models/profile_rekening_model.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../routes/app_pages.dart';

class ProfileRekeningHistoryController extends GetxController {
  final listCon = ListComponentController<ProfileRekeningModel>(
    urlApi: (index, filter) =>
        '/api/PegawaiRekening/History/List?pageIndex=$index',
    fromDynamic: ProfileRekeningModel.fromDynamic,
    allowSearch: false,
  );

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PROFILE_REKENING_HISTORY_SETUP,
      parameters: {
        'id': id.toString(),
        'showStatus': true.toString(),
      },
    )?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }
}
