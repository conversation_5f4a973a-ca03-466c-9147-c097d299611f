import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../models/profile_riwayat_kesehatan_model.dart';
import '../../../routes/app_pages.dart';

class ProfileRiwayatKesehatanController extends GetxController {
  final listCon = ListComponentController<RiwayatkesehatanModel>(
    urlApi: (index, filter) =>
        '/api/PegawaiRiwayatKesehatan/List?pageIndex=$index',
    fromDynamic: RiwayatkesehatanModel.fromDynamic,
    allowSearch: false,
  );

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PROFILE_RIWAYAT_KESEHATAN_SETUP,
      parameters: {'id': id.toString()},
    )?.then((value) {
      listCon.refresh();
    });
  }

  void popupMenuButtonOnSelected(String v) async {
    if (v == 'Tambah') {
      Get.toNamed(Routes.PROFILE_RIWAYAT_KESEHATAN_SETUP)?.then(
        (value) {
          listCon.refresh();
        },
      );
    } else if (v == 'History') {
      Get.toNamed(
        Routes.PROFILE_RIWAYAT_KESEHATAN_HISTORY,
        parameters: {
          'showStatus': true.toString(),
        },
      )?.then(
        (value) {
          listCon.refresh();
        },
      );
    }
  }
}
