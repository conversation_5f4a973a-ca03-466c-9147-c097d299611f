import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_datetime_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_dropdown_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_text_component.dart';
import 'package:hr_portal/app/mahas/components/pages/setup_page_component.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/models/lembur_model.dart';
import '../../../mahas/components/inputs/input_lookup_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/models/api_result_model.dart';
import '../../../mahas/services/http_api.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../models/pegawai_approval.dart';

class PermintaanJadwalLemburSetupController extends GetxController {
  late SetupPageController formCon;
  final staffLemburCon = InputLookupController<LookuplemburModel>(
    fromDynamic: (e) => LookuplemburModel.fromDynamic(e),
    itemText: (e) => e.nama ?? "",
    itemValue: (e) => e.id,
    urlApi: (index, filter) =>
        '/api/PermintaanJadwal/Lembur/Pegawai?pageIndex=$index&filter=$filter&id_divisi=${MahasConfig.selectedDivisi}',
  );
  final alasanCon = InputTextController(type: InputTextType.paragraf);
  final tanggalMulaiCon = InputDatetimeController();
  final jamMulaiCon = InputDatetimeController();
  final lamaJamCon = InputTextController(type: InputTextType.number);
  final jenisCon = InputDropdownController(items: [
    DropdownItem.init("Ganti Libur", 0),
    DropdownItem.init('Ganti Uang', 1)
  ]);

  final alasanTolakCon = InputTextController();
  final String nama = MahasConfig.profile!.nama!;
  late String approve;
  late String cekKadiv;
  late String statusApprove;
  late bool allowED;
  Rxn<bool> accKadiv = Rxn<bool>();
  Rxn<bool> accManager = Rxn<bool>();
  RxBool isVisible = false.obs;
  late RxString pegawaiManager = ''.obs;
  RxString alasanTolak = ''.obs;
  RxBool isBatal = false.obs;
  RxInt idPegawaiRequest = 0.obs;
  final isDinamisApprove = MahasConfig.profileMenu.approvalDinamis;
  RxBool isStop = false.obs;
  RxList<PegawaiApprovalModel> modelApproval = <PegawaiApprovalModel>[].obs;
  int? id;

  RxBool isKadiv = false.obs;
  void cekIsKadiv() {
    cekKadiv = Get.parameters['isKadiv'].toString();
    approve = Get.parameters['approval'].toString();
    statusApprove = Get.parameters['status'].toString();
    if (cekKadiv == "true" && statusApprove == "false" ||
        statusApprove == "true") {
      isKadiv.value = false;
    } else if (cekKadiv == "true" && statusApprove == "null") {
      isKadiv.value = true;
    } else if (cekKadiv == "false") {
      isKadiv.value = false;
    } else if (approve == "approval") {
      isKadiv.value = false;
    }
  }

  @override
  void onInit() {
    cekApproval();
    cekIsKadiv();
    staffLemburCon.onChanged =
        () => idPegawaiRequest.value = staffLemburCon.value!.id!;
    formCon = SetupPageController(
      allowEdit: isKadiv.value,
      allowDelete: isKadiv.value,
      urlApiGet: (id) => '/api/PermintaanJadwal/Lembur/$id',
      urlApiPost: () => '/api/PermintaanJadwal/Lembur',
      urlApiPut: (id) => '/api/PermintaanJadwal/Lembur/$id',
      urlApiDelete: (id) => '/api/PermintaanJadwal/Lembur/$id',
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "id_Pegawai": staffLemburCon.value!.id,
        "alasan": alasanCon.value,
        "tanggalJam":
            "${MahasFormat.dateTimeOfDayToString(tanggalMulaiCon.value, jamMulaiCon.value)}",
        "lamaJam": lamaJamCon.value,
        "jenis": jenisCon.value,
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (!staffLemburCon.isValid) return false;
        if (!tanggalMulaiCon.isValid) return false;
        if (!jamMulaiCon.isValid) return false;
        if (!lamaJamCon.isValid) return false;
        if (!jenisCon.isValid) return false;
        if (!alasanCon.isValid) return false;
        if (jenisCon.value == 0 && lamaJamCon.value > 8) {
          Helper.dialogWarning(
              "Lembur Ganti Libur Tidak Boleh Lebih Dari 8 Jam!");
          return false;
        }
        return true;
      },
      apiToView: (json) {
        LemburModel model = LemburModel.fromJson(json);
        id = model.id;
        staffLemburCon.value =
            LookuplemburModel.init(model.idPegawai, model.pegawai);
        alasanCon.value = model.alasan;
        tanggalMulaiCon.value = model.tanggaljam;
        jamMulaiCon.value = TimeOfDay.fromDateTime(model.tanggaljam!);
        lamaJamCon.value = model.lamajam;
        jenisCon.value = model.jenis == 'Ganti Libur' ? 0 : 1;
        pegawaiManager.value = model.pegawaimanager!;
        accManager.value = model.approvemanager;
        accKadiv.value = true;

        idPegawaiRequest.value = model.idPegawai!;
        pegawaiManager.value = model.pegawaimanager!;

        alasanTolak.value = model.alasanTolak ?? '';

        if (isDinamisApprove == false && allowED == false) {
          if ((model.approvemanager == null &&
              model.idPegawaiManager == MahasConfig.profile!.id)) {
            isVisible.value = true;
          }
        }

        // batal
        if (MahasConfig.dinamisForm.approval?.bisaBatal == true) {
          if (isDinamisApprove == false && allowED == false) {
            if (model.approvemanager == true &&
                model.idPegawaiManager == MahasConfig.profile!.id) {
              isBatal.value = true;
            }
          }
        }

        if (isDinamisApprove == true && allowED == false) {
          getDataApproval();
        }
      },
    );
    super.onInit();
  }

  void cekApproval() {
    approve = Get.parameters['approval'].toString();
    if (approve == "approval") {
      allowED = false;
    } else {
      allowED = true;
    }
  }

  Future<void> batalOnPress() async {
    var id = Get.parameters['id'].toString();
    String urlManager = '/api/PermintaanJadwal/Lembur/Batal/Manager/';
    var action = await Helper.batalAction(
      id,
      alasanTolakCon.value,
      urlManager,
    );
    if (action.success) {
      final url = '/api/PermintaanJadwal/Lembur/$id';
      ApiResultModel r;
      r = await HttpApi.get(url);
      if (action.success) {
        LemburModel model = LemburModel.fromJson(r.body);
        accManager.value = model.approvemanager;
        alasanTolak.value = model.alasanTolak ?? '';
        isBatal.value = false;
        update();
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan aksi, silahkan cek koneksi internet");
    } else if (!action.success) {
      Helper.dialogWarning(
        "Gagal melakukan aksi",
        resMassage: action.message,
        noInternetMassage:
            "Gagal melakukan aksi, silahkan cek koneksi internet",
      );
    }
  }

  Future<void> approvalOnPress(bool approve) async {
    var id = Get.parameters['id'].toString();
    String urlKadiv = '/api/PermintaanJadwal/Lembur/Approve/Kadiv/';
    String urlManager = '/api/PermintaanJadwal/Lembur/Approve/Manager/';
    String urlApprove = '/api/PermintaanJadwal/Lembur/Approve/';
    var action = await Helper.approvalAction(
      id,
      approve,
      alasanTolakCon.value,
      true,
      urlKadiv,
      urlManager,
      isDinamis: isDinamisApprove,
      urlApprove: urlApprove,
    );
    if (action.success) {
      final url = '/api/PermintaanJadwal/Lembur/$id';
      ApiResultModel r;
      r = await HttpApi.get(url);
      if (r.success) {
        LemburModel model = LemburModel.fromJson(r.body);
        accManager.value = model.approvemanager;
        alasanTolak.value = model.alasanTolak ?? '';
        if (isDinamisApprove) {
          getDataApproval();
        }
        isVisible.value = false;
        update();
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan approve, silahkan cek koneksi internet");
    } else if (!action.success) {
      Helper.dialogWarning(
        "Gagal melakukan approval",
        resMassage: action.message,
        noInternetMassage:
            "Gagal melakukan approval, silahkan cek koneksi internet",
      );
    }
  }

  Future<void> getDataApproval() async {
    EasyLoading.show();
    final url = '/api/PermintaanJadwal/Lembur/Approval/$id';
    final r = await HttpApi.get(url);
    modelApproval.clear();
    if (r.success) {
      if (r.body != null) {
        var data = json.decode(r.body);
        for (var e in data['datas']) {
          modelApproval.add(PegawaiApprovalModel.fromDynamic(e));
        }
        bool canApprove = modelApproval.any((item) =>
            item.idPegawaiapprove == MahasConfig.profile?.id &&
            item.approve == null);
        if (canApprove) {
          isVisible.value = true;
        }
        bool notApprove = modelApproval.any((item) => item.approve == false);
        if (notApprove) {
          isVisible.value = false;
        }
        isStop.value = modelApproval.any((item) => item.approve == false);
      } else {
        Helper.dialogWarning(r.message);
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan aksi, silahkan cek koneksi internet");
    } else if (r.statusCode == 404) {
      Helper.dialogWarning("Tidak Ada Data Pegawai Approval");
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }
}
