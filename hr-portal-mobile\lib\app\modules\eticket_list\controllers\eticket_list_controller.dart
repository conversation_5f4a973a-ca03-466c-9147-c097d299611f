import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../models/etikcet_model.dart';
import '../../../routes/app_pages.dart';

class EticketListController extends GetxController {
  final listCon = ListComponentController<EticketModel>(
    urlApi: (index, filter) => '/api/ETicket/List?pageIndex=$index',
    fromDynamic: EticketModel.fromDynamic,
    allowSearch: false,
  );

  void addOnPress() {
    Get.toNamed(Routes.ETICKET)?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.ETICKET,
      parameters: {
        'id': id.toString(),
      },
    )?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }
}
