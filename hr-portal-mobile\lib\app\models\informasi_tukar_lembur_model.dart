import 'dart:convert';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';

class InformasitukarlemburModel {
  int? id;
  int? idDivisi;
  int? idPegawai;
  int? idPegawaiKadiv;
  DateTime? tanggalinput;
  String? alasan;
  bool? approvemanager;
  DateTime? tanggaljam;
  int? lamajam;
  String? jenis;
  String? pegawai;
  String? pegawaikadiv;
  String? pegawaimanager;
  String? divisi;

  InformasitukarlemburModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static InformasitukarlemburModel fromDynamic(dynamic dynamicData) {
    final model = InformasitukarlemburModel();

    model.id = dynamicData['id'];
    model.idDivisi = dynamicData['id_Divisi'];
    model.idPegawai = dynamicData['id_Pegawai'];
    model.idPegawaiKadiv = dynamicData['id_Pegawai_Kadiv'];
    model.tanggalinput =
        MahasFormat.stringToDateTime(dynamicData['tanggalInput']);
    model.alasan = dynamicData['alasan'];
    model.approvemanager = dynamicData['approveManager'];
    model.tanggaljam = MahasFormat.stringToDateTime(dynamicData['tanggalJam']);
    model.lamajam = dynamicData['lamaJam'];
    model.jenis = dynamicData['jenis'];
    model.pegawai = dynamicData['pegawai'];
    model.pegawaikadiv = dynamicData['pegawaiKadiv'];
    model.pegawaimanager = dynamicData['pegawaiManager'];
    model.divisi = dynamicData['divisi'];

    return model;
  }
}
