{"project_info": {"project_number": "673110614921", "project_id": "hr-portal-sanata", "storage_bucket": "hr-portal-sanata.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:673110614921:android:03b6d410803aa817d1b758", "android_client_info": {"package_name": "com.sanata.hrportal"}}, "oauth_client": [{"client_id": "673110614921-6c4l7q5qhhpccvi3ah5bgtasefoqp4fn.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sanata.hrportal", "certificate_hash": "e14ea6a1df9219774f866acd1a3f67d4d93cc12c"}}, {"client_id": "673110614921-7np49o8isqj00rjih7i7umbgn25obptn.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sanata.hrportal", "certificate_hash": "5dd67a72a107d0566d7b79bda78d1451e1f81a1f"}}, {"client_id": "673110614921-b49f07si885ubnj7reemnfvu9hmoho3r.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sanata.hrportal", "certificate_hash": "0fed644a071059299e6eb8e2f037dad91c6c46dc"}}, {"client_id": "673110614921-fk8eo350kc2n8msn26sjre817jfe6ekj.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sanata.hrportal", "certificate_hash": "d18a4234b7aa10b2084b01020ff586d23c38daf3"}}, {"client_id": "673110614921-focisp7v63l7jhkfb9jmgur6elj1p86a.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sanata.hrportal", "certificate_hash": "0b493fc0ca2b0c2dc45a8786c0e82240ffb5963e"}}, {"client_id": "673110614921-hbpl5rkp3qt5vkhha3065fdm3iubveut.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sanata.hrportal", "certificate_hash": "8b5d2a581ca3cf97a5c68c954be5a000d583d7ae"}}, {"client_id": "673110614921-pm7s33m63i1b8a2178br9uupl5mv4ba4.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sanata.hrportal", "certificate_hash": "d66b098b2b99d0dca4fedcd8955251569e83ee15"}}, {"client_id": "673110614921-rvptsf0ln5jmt3f1mr2pvbauo8s8m2dn.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sanata.hrportal", "certificate_hash": "08284361b5d5369acc057f00bd7f1208f2c07edc"}}, {"client_id": "673110614921-t9vhq7se8bln9sdod9u5fj8cvec1p744.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sanata.hrportal", "certificate_hash": "7b628aa182e1f46ef769872098f9c02d9b379c3e"}}, {"client_id": "673110614921-temq19jmq5631jlrinm0dc94keem681q.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sanata.hrportal", "certificate_hash": "d3a82e3a3b61abffad880df6cef8e3b9e537c4d6"}}, {"client_id": "673110614921-5dsfthla59mq3ss9tl1020pfugpaf4ds.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCCoL4g-tuziDr59FVOf9ZXFSIqITaVPjs"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "673110614921-5dsfthla59mq3ss9tl1020pfugpaf4ds.apps.googleusercontent.com", "client_type": 3}, {"client_id": "673110614921-ue416g6106nnmra6n0uhl18q0ve2nai2.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.sanata.hrportal"}}]}}}], "configuration_version": "1"}