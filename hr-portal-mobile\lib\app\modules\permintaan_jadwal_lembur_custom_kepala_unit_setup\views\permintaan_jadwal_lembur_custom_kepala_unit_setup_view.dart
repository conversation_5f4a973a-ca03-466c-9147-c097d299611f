import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_datetime_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_lookup_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_text_component.dart';
import 'package:hr_portal/app/mahas/components/mahas_themes.dart';
import 'package:hr_portal/app/mahas/components/pages/setup_page_component.dart';
import 'package:hr_portal/app/mahas/mahas_colors.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';
import 'package:hr_portal/app/mahas_complement/mahas_colors.dart';
import 'package:hr_portal/app/mahas_complement/status_request.dart';

import '../controllers/permintaan_jadwal_lembur_custom_kepala_unit_setup_controller.dart';

class PermintaanJadwalLemburCustomKepalaUnitSetupView
    extends GetView<PermintaanJadwalLemburCustomKepalaUnitSetupController> {
  const PermintaanJadwalLemburCustomKepalaUnitSetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      title: "Lembur",
      crossAxisAlignmentChildren: CrossAxisAlignment.start,
      controller: controller.formCon,
      children: () => [
        InputLookupComponent(
          label: "Kepada Pegawai",
          required: true,
          controller: controller.staffLemburCon,
          editable: controller.formCon.isState == SetupPageState.create,
          marginBottom: 5,
        ),
        InkWell(
          onTap: () async {
            if (controller.idPegawaiRequest.value != 0) {
              await Helper.fotoProfilOnTap(controller.idPegawaiRequest.value);
            } else {
              Helper.dialogWarning("Pilih pegawai terlebih dahulu!");
            }
          },
          child: SizedBox(
            height: 30,
            child: Text(
              "Lihat Foto Profil Pegawai",
              style: MahasThemes.link,
              textAlign: TextAlign.left,
            ),
          ),
        ),
        InputDatetimeComponent(
          label: "Tanggal Lembur",
          required: true,
          controller: controller.tanggalMulaiCon,
          editable: controller.formCon.editable,
        ),
        InputDatetimeComponent(
          type: InputDatetimeType.time,
          label: "Waktu Mulai Lembur",
          required: true,
          controller: controller.jamMulaiCon,
          editable: controller.formCon.editable,
        ),
        Text(
          "Lama Penugasan Lembur",
          style: MahasThemes.title,
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 1,
              child: InputTextComponent(
                label: "Hari",
                controller: controller.lamaHariCon,
                required: true,
                editable: controller.formCon.editable,
              ),
            ),
            const SizedBox(width: 10),
            Expanded(
              flex: 1,
              child: InputTextComponent(
                label: "Jam",
                controller: controller.jamCon,
                required: true,
                editable: controller.formCon.editable,
              ),
            ),
            const SizedBox(width: 10),
            Expanded(
              flex: 1,
              child: InputTextComponent(
                label: "Menit",
                controller: controller.menitCon,
                required: true,
                editable: controller.formCon.editable,
              ),
            ),
          ],
        ),
        InputTextComponent(
          label: "Keterangan",
          controller: controller.keteranganLemburCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        if (controller.formCon.isState != SetupPageState.create &&
            controller.formCon.isState != SetupPageState.update) ...[
          Visibility(
            visible: MahasConfig.dinamisForm.lembur?.dokumenPenugasan ?? false,
            child: InkWell(
              onTap: controller.suratPenugasanOnTap,
              child: Container(
                width: Get.width,
                decoration: BoxDecoration(
                  border: Border.all(color: MahasColors.dark),
                  borderRadius: const BorderRadius.all(Radius.circular(10)),
                  color: MahasColors.dark.withValues(alpha: .05),
                ),
                padding: const EdgeInsets.all(10),
                child: const Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Icon(
                      FontAwesomeIcons.file,
                    ),
                    SizedBox(width: 10),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [Text("Surat Penugasan Lembur"), Text("PDF")],
                    )
                  ],
                ),
              ),
            ),
          ),
        ],
        const SizedBox(height: 10),
        controller.isViewPegawai
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Data Absensi Lembur",
                    style: MahasThemes.title,
                  ),
                  const SizedBox(height: 5),
                  Obx(() => Container(
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: MahasColors.grey,
                          ),
                          borderRadius: BorderRadius.all(
                            Radius.circular(MahasThemes.borderRadius),
                          ),
                        ),
                        child: ListTile(
                          visualDensity: VisualDensity.compact,
                          title: Text(
                            "Clock In",
                            style: MahasThemes.title,
                          ),
                          trailing: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                MahasFormat.displayDate(
                                    controller.jamMasukAbsensiLembur.value),
                                style: MahasColor.muted,
                              ),
                              controller.jamMasukAbsensiLembur.value != null
                                  ? Text(
                                      MahasFormat.displayTime(
                                          TimeOfDay.fromDateTime(controller
                                              .jamMasukAbsensiLembur.value!)),
                                      style: MahasColor.muted,
                                    )
                                  : const SizedBox(),
                            ],
                          ),
                        ),
                      )),
                  Obx(() => Container(
                        margin: const EdgeInsets.only(top: 10),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: MahasColors.grey,
                          ),
                          borderRadius: BorderRadius.all(
                            Radius.circular(MahasThemes.borderRadius),
                          ),
                        ),
                        child: ListTile(
                          visualDensity: VisualDensity.compact,
                          title: Text(
                            "Clock Out",
                            style: MahasThemes.title,
                          ),
                          trailing: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                MahasFormat.displayDate(
                                    controller.jamKeluarAbsensiLembur.value),
                                style: MahasColor.muted,
                              ),
                              controller.jamKeluarAbsensiLembur.value != null
                                  ? Text(
                                      MahasFormat.displayTime(
                                          TimeOfDay.fromDateTime(controller
                                              .jamKeluarAbsensiLembur.value!)),
                                      style: MahasColor.muted,
                                    )
                                  : const SizedBox(),
                            ],
                          ),
                        ),
                      ))
                ],
              )
            : const SizedBox(),
        const SizedBox(height: 10),
        InputTextComponent(
          label: "Keterangan Lembur",
          controller: controller.keteranganLemburPegawaiCon,
          editable: false,
        ),
        const SizedBox(height: 10),
        Obx(
          () => ApprovalStatusContainer(
            isEditable: controller.formCon.editable,
            isVisible: (controller.isKadiv.value &&
                    controller.formCon.isState == SetupPageState.detail &&
                    controller.isVisible.value)
                ? true.obs
                : false.obs,
            isBatal: controller.isBatal,
            allowED: controller.allowED,
            isDinamisApprove: false,
            alasanTolakController: controller.alasanTolakCon,
            onTolakPressed: () {
              controller.approvalOnPress(false);
            },
            onTerimaPressed: () {
              controller.approvalOnPress(true);
            },
            batalOnPress: () {
              var status = controller.isBatal.isTrue ? 'batal' : 'tolak';
              alert(status);
            },
            pegawaiKadiv: "",
            pegawaiManager: controller.pegawaiManager.value,
            titleCardManager: MahasConfig.kadivmanager.kadiv,
            accKadiv: controller.accKadiv.value,
            accManager: controller.accManager.value,
            statusTolak: controller.alasanTolak.value,
            modelApproval: controller.modelApproval,
            isStop: controller.isStop.value,
          ),
        ),
      ],
    );
  }

  Future<dynamic> alert(String status) {
    return Get.dialog(
      AlertDialog(
        content: SizedBox(
          width: 1000,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              InputTextComponent(
                label: status == "tolak" ? "Alasan Ditolak" : "Alasan Batal",
                controller: controller.alasanTolakCon,
                required: true,
              ),
            ],
          ),
        ),
        contentPadding:
            const EdgeInsets.only(bottom: 0, top: 20, right: 10, left: 10),
        actionsPadding:
            const EdgeInsets.only(top: 0, bottom: 0, left: 10, right: 10),
        actions: [
          TextButton(
            child: Text(
              "Close",
              style: MahasThemes.muted,
            ),
            onPressed: () {
              Get.back(result: false);
            },
          ),
          TextButton(
            child: const Text(
              "Simpan",
            ),
            onPressed: () {
              if (controller.alasanTolakCon.isValid) {
                if (status == "tolak") {
                  controller.approvalOnPress(false);
                } else if (status == "batal") {
                  controller.batalOnPress();
                }
                Get.back(result: true);
              }
            },
          ),
        ],
      ),
    );
  }
}
