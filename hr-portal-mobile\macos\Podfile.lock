PODS:
  - connectivity_plus (0.0.1):
    - FlutterMacOS
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - Firebase/Analytics (10.0.0):
    - Firebase/Core
  - Firebase/Auth (10.0.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 10.0.0)
  - Firebase/Core (10.0.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.0.0)
  - Firebase/CoreOnly (10.0.0):
    - FirebaseCore (= 10.0.0)
  - Firebase/Messaging (10.0.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.0.0)
  - Firebase/RemoteConfig (10.0.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 10.0.0)
  - firebase_analytics (10.0.4):
    - Firebase/Analytics (= 10.0.0)
    - firebase_core
    - FlutterMacOS
  - firebase_auth (4.1.1):
    - Firebase/Auth (~> 10.0.0)
    - Firebase/CoreOnly (~> 10.0.0)
    - firebase_core
    - FlutterMacOS
  - firebase_core (2.1.1):
    - Firebase/CoreOnly (~> 10.0.0)
    - FlutterMacOS
  - firebase_messaging (14.0.4):
    - Firebase/CoreOnly (~> 10.0.0)
    - Firebase/Messaging (~> 10.0.0)
    - firebase_core
    - FlutterMacOS
  - firebase_remote_config (3.0.4):
    - Firebase/CoreOnly (~> 10.0.0)
    - Firebase/RemoteConfig (~> 10.0.0)
    - firebase_core
    - FlutterMacOS
  - FirebaseABTesting (10.1.0):
    - FirebaseCore (~> 10.0)
  - FirebaseAnalytics (10.0.0):
    - FirebaseAnalytics/AdIdSupport (= 10.0.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/MethodSwizzler (~> 7.8)
    - GoogleUtilities/Network (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.0.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.0.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/MethodSwizzler (~> 7.8)
    - GoogleUtilities/Network (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAuth (10.0.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GTMSessionFetcher/Core (~> 2.1)
  - FirebaseCore (10.0.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Logger (~> 7.8)
  - FirebaseCoreInternal (10.1.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.1.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.0.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseRemoteConfig (10.0.0):
    - FirebaseABTesting (~> 10.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - flutter_local_notifications (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - geolocator_apple (1.2.0):
    - FlutterMacOS
  - GoogleAppMeasurement (10.0.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.0.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/MethodSwizzler (~> 7.8)
    - GoogleUtilities/Network (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.0.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.0.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/MethodSwizzler (~> 7.8)
    - GoogleUtilities/Network (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.0.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/MethodSwizzler (~> 7.8)
    - GoogleUtilities/Network (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleDataTransport (9.2.0):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.8.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.8.0):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.8.0):
    - GoogleUtilities/Environment
  - GoogleUtilities/MethodSwizzler (7.8.0):
    - GoogleUtilities/Logger
  - GoogleUtilities/Network (7.8.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.8.0)"
  - GoogleUtilities/Reachability (7.8.0):
    - GoogleUtilities/Logger
  - GoogleUtilities/UserDefaults (7.8.0):
    - GoogleUtilities/Logger
  - GTMSessionFetcher/Core (2.2.0)
  - nanopb (2.30909.0):
    - nanopb/decode (= 2.30909.0)
    - nanopb/encode (= 2.30909.0)
  - nanopb/decode (2.30909.0)
  - nanopb/encode (2.30909.0)
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_macos (0.0.1):
    - FlutterMacOS
  - PromisesObjC (2.1.1)
  - ReachabilitySwift (5.0.0)
  - sign_in_with_apple (0.0.1):
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS

DEPENDENCIES:
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos`)
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - firebase_analytics (from `Flutter/ephemeral/.symlinks/plugins/firebase_analytics/macos`)
  - firebase_auth (from `Flutter/ephemeral/.symlinks/plugins/firebase_auth/macos`)
  - firebase_core (from `Flutter/ephemeral/.symlinks/plugins/firebase_core/macos`)
  - firebase_messaging (from `Flutter/ephemeral/.symlinks/plugins/firebase_messaging/macos`)
  - firebase_remote_config (from `Flutter/ephemeral/.symlinks/plugins/firebase_remote_config/macos`)
  - flutter_local_notifications (from `Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - geolocator_apple (from `Flutter/ephemeral/.symlinks/plugins/geolocator_apple/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_macos (from `Flutter/ephemeral/.symlinks/plugins/path_provider_macos/macos`)
  - sign_in_with_apple (from `Flutter/ephemeral/.symlinks/plugins/sign_in_with_apple/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseAuth
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfig
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - GTMSessionFetcher
    - nanopb
    - PromisesObjC
    - ReachabilitySwift

EXTERNAL SOURCES:
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  firebase_analytics:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_analytics/macos
  firebase_auth:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_auth/macos
  firebase_core:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_core/macos
  firebase_messaging:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_messaging/macos
  firebase_remote_config:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_remote_config/macos
  flutter_local_notifications:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  geolocator_apple:
    :path: Flutter/ephemeral/.symlinks/plugins/geolocator_apple/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_macos/macos
  sign_in_with_apple:
    :path: Flutter/ephemeral/.symlinks/plugins/sign_in_with_apple/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos

SPEC CHECKSUMS:
  connectivity_plus: 18d3c32514c886e046de60e9c13895109866c747
  device_info_plus: 5401765fde0b8d062a2f8eb65510fb17e77cf07f
  Firebase: 1b810f3d0c0532e27a48f1961f8c0400a668a2cf
  firebase_analytics: 8facfd083149aeb7f1f06033891a23e8050ad9d1
  firebase_auth: 65dab2f61a160cab11d6bbbc4090ee8b1d8a17ed
  firebase_core: 2ad407221995257e47215d9ae92467db0dd48ced
  firebase_messaging: 730da0ddfc4ac41bfd66c4c2405ad7bb94fa0efa
  firebase_remote_config: 7c59ce88caaea8e4b3463a2f1e006bf2d774711c
  FirebaseABTesting: 8404d743de561c73570c8dbb7810035ffec10053
  FirebaseAnalytics: 9921a52739f4ab66099da31b6e0243db78a3ac0a
  FirebaseAuth: 493382cf533cc45e2862b00e9aa4cfe4c98daf71
  FirebaseCore: 97f48a3a567a72b8d4daa0f03c3aadb78df4e995
  FirebaseCoreInternal: 96d75228e10fd369564da51bd898414eb0f54df5
  FirebaseInstallations: 99d24bac0243cf8b0e96cf5426340d211f0bcc80
  FirebaseMessaging: 8916bf5edb1dbfac74665a181e4d1ab3a78a08a2
  FirebaseRemoteConfig: e4431ddba74ddf705e2aabd7d356a23d5b802853
  flutter_local_notifications: 3805ca215b2fb7f397d78b66db91f6a747af52e4
  FlutterMacOS: ae6af50a8ea7d6103d888583d46bd8328a7e9811
  geolocator_apple: 72a78ae3f3e4ec0db62117bd93e34523f5011d58
  GoogleAppMeasurement: 7e48a3249792ac35d6f18f107f63f199a7e9d0ce
  GoogleDataTransport: 1c8145da7117bd68bbbed00cf304edb6a24de00f
  GoogleUtilities: 1d20a6ad97ef46f67bbdec158ce00563a671ebb7
  GTMSessionFetcher: d62ffccea5108bb9e7762ee121ae63bbdd2c6303
  nanopb: b552cce312b6c8484180ef47159bc0f65a1f0431
  package_info_plus: 02d7a575e80f194102bef286361c6c326e4c29ce
  path_provider_macos: 3c0c3b4b0d4a76d2bf989a913c2de869c5641a19
  PromisesObjC: ab77feca74fa2823e7af4249b8326368e61014cb
  ReachabilitySwift: 985039c6f7b23a1da463388634119492ff86c825
  sign_in_with_apple: a9e97e744e8edc36aefc2723111f652102a7a727
  url_launcher_macos: 597e05b8e514239626bcf4a850fcf9ef5c856ec3

PODFILE CHECKSUM: a884f6dd3f7494f3892ee6c81feea3a3abbf9153

COCOAPODS: 1.11.3
