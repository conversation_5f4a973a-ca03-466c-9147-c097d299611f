import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../models/profile_pegawai_str_model.dart';
import '../../../routes/app_pages.dart';

class ProfilePegawaiStrController extends GetxController {
  final listCon = ListComponentController<PegawaistrModel>(
    urlApi: (index, filter) => '/api/PegawaiStr/List?pageIndex=$index',
    fromDynamic: PegawaistrModel.fromDynamic,
    allowSearch: false,
  );

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PROFILE_PEGAWAI_STR_SETUP,
      parameters: {
        'id': id.toString(),
      },
    )?.then((value) {
      listCon.refresh();
    });
  }

  void popupMenuButtonOnSelected(String v) async {
    if (v == 'Tambah') {
      Get.toNamed(Routes.PROFILE_PEGAWAI_STR_SETUP)?.then(
        (value) {
          listCon.refresh();
        },
      );
    } else if (v == 'History') {
      Get.toNamed(
        Routes.PROFILE_PEGAWAI_STR_HISTORY,
        parameters: {
          'showStatus': true.toString(),
        },
      )?.then(
        (value) {
          listCon.refresh();
        },
      );
    }
  }
}
