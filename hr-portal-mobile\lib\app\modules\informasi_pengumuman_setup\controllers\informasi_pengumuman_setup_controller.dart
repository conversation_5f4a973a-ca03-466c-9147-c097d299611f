import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../models/pengumuman_model.dart';
import 'package:path/path.dart' as p;

class InformasiPengumumanSetupController extends GetxController {
  late SetupPageController formCon;

  final judulCon = InputTextController();
  final keteranganCon = InputTextController(type: InputTextType.paragraf);
  final fileCon = InputFileController(
    urlImage: true,
    type: FileType.custom,
    tipe: InputFileType.pdf,
    extension: ["pdf", "jpg", "jpeg", "png"],
  );

  @override
  void onInit() {
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/Pengumuman/$id',
      urlApiPost: () => '/api/Pengumuman',
      urlApiPut: (id) => '/api/Pengumuman/$id',
      allowDelete: false,
      allowEdit: false,
      bodyApi: (id) => {},
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        return true;
      },
      apiToView: (json) {
        PengumumanModel model = PengumumanModel.fromJson(json);
        judulCon.value = model.judul;
        keteranganCon.value = model.keterangan;
        fileCon.value = model.pathfile;

        if (model.pathfile != null) {
          String extension = p.extension(model.pathfile!).toLowerCase();
          if (extension == ".pdf") {
            updateState(InputFileType.pdf);
          } else {
            updateState(InputFileType.image);
          }
        }
      },
    );
    if (EasyLoading.isShow) return;
    super.onInit();
  }

  void updateState(InputFileType tipe) {
    fileCon.tipe = tipe;
  }
}
