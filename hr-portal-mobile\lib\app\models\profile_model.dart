import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class ProfileModel {
  int? id;
  String? nama;
  DateTime? tanggalLahir;
  DateTime? tanggalmulaibekerja;
  DateTime? tanggalselesaibekerja;
  String? statusPegawai;
  String? email;
  int? notifikasi;
  String? tempatlahir;
  String? alamatktp;
  String? alamatdomisili;
  int? idAgama;
  String? statusperkawinan;
  String? noktp;
  String? nokk;
  String? nobpjskesehatan;
  String? nobpjstk;
  String? nonpwp;
  String? nip;
  String? jeniskelamin;
  String? urlphoto;
  int? idPangkat;
  int? idKategoriSdm;
  int? idGolongandarah;
  String? sukubangsa;
  String? masakerja;
  int? idGradestep;
  int? idPtkp;
  int? idJabatan;
  String? namagradestep;
  String? statusptkp;
  String? namajabatan;
  bool? melihatsemuaabsensi;
  List<ProfilDivisiModel>? divisi;

  ProfileModel();

  static ProfileModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static ProfileModel fromDynamic(dynamic dynamicData) {
    final model = ProfileModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.nama = dynamicData['nama'];
    model.tanggalLahir =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalLahir']);
    model.tanggalmulaibekerja =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalMulaiBekerja']);
    model.tanggalselesaibekerja =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalSelesaiBekerja']);
    model.statusPegawai = dynamicData['statusPegawai'];
    model.email = dynamicData['email'];
    model.notifikasi = MahasFormat.dynamicToInt(dynamicData['notifikasi']);
    model.tempatlahir = dynamicData['tempatLahir'];
    model.alamatktp = dynamicData['alamatKTP'];
    model.alamatdomisili = dynamicData['alamatDomisili'];
    model.idAgama = MahasFormat.dynamicToInt(dynamicData['id_Agama']);
    model.statusperkawinan = dynamicData['statusPerkawinan'];
    model.noktp = dynamicData['noKTP'];
    model.nokk = dynamicData['noKK'];
    model.nobpjskesehatan = dynamicData['noBPJSKesehatan'];
    model.nobpjstk = dynamicData['noBPJSTK'];
    model.nonpwp = dynamicData['noNPWP'];
    model.nip = dynamicData['nip'];
    model.jeniskelamin = dynamicData['jenisKelamin'];
    model.urlphoto = dynamicData['fotoProfile'];
    model.idPangkat = MahasFormat.dynamicToInt(dynamicData['id_Pangkat']);
    model.idKategoriSdm =
        MahasFormat.dynamicToInt(dynamicData['id_KategoriSdm']);
    model.idGolongandarah =
        MahasFormat.dynamicToInt(dynamicData['id_GolonganDarah']);
    model.sukubangsa = dynamicData['sukuBangsa'];
    model.masakerja = dynamicData['masaKerja'];
    model.idGradestep = MahasFormat.dynamicToInt(dynamicData['id_GradeStep']);
    model.idPtkp = MahasFormat.dynamicToInt(dynamicData['id_PTKP']);
    model.idJabatan = MahasFormat.dynamicToInt(dynamicData['id_Jabatan']);
    model.namagradestep = dynamicData['namaGradeStep'];
    model.statusptkp = dynamicData['statusPTKP'];
    model.namajabatan = dynamicData['namaJabatan'];
    model.melihatsemuaabsensi =
        MahasFormat.dynamicToBool(dynamicData['melihatSemuaAbsensi']);
    if (dynamicData['divisi'] != null) {
      final detailT = dynamicData['divisi'] as List;
      model.divisi = [];
      for (var i = 0; i < detailT.length; i++) {
        model.divisi!.add(ProfilDivisiModel.fromDynamic(detailT[i]));
      }
    }

    return model;
  }
}

class ProfilDivisiModel {
  int? idDivisi;
  String? divisi;
  bool? prioritas;
  bool? sebagaikadiv;
  bool? sebagaimanager;

  ProfilDivisiModel();

  static ProfilDivisiModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static ProfilDivisiModel fromDynamic(dynamic dynamicData) {
    final model = ProfilDivisiModel();

    model.idDivisi = MahasFormat.dynamicToInt(dynamicData['id_Divisi']);
    model.divisi = dynamicData['divisi'];
    model.prioritas = MahasFormat.dynamicToBool(dynamicData['prioritas']);
    model.sebagaikadiv = MahasFormat.dynamicToBool(dynamicData['sebagaiKadiv']);
    model.sebagaimanager =
        MahasFormat.dynamicToBool(dynamicData['sebagaiManager']);

    return model;
  }
}

class HistoryProfileModel {
  int? id;
  int? idPegawai;
  int? idAgama;
  String? nama;
  String? pegawainama;
  DateTime? tanggallahir;
  String? tempatlahir;
  String? alamatktp;
  String? alamatdomisili;
  String? statusperkawinan;
  String? noktp;
  String? nokk;
  String? nobpjskesehatan;
  String? nobpjstk;
  String? nonpwp;
  String? nip;
  int? idPangkat;
  int? idGolongandarah;
  String? jeniskelamin;
  String? fotoprofile;
  int? idPegawaiapproval;
  String? namapegawaiapproval;
  bool? approved;
  DateTime? tanggalrequest;
  DateTime? tanggalapproval;
  String? alasantolak;
  String? sukubangsa;
  int? idKategoriSdm;

  HistoryProfileModel();

  static HistoryProfileModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static HistoryProfileModel fromDynamic(dynamic dynamicData) {
    final model = HistoryProfileModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.idAgama = MahasFormat.dynamicToInt(dynamicData['id_Agama']);
    model.nama = dynamicData['nama'];
    model.pegawainama = dynamicData['namaPegawai'];
    model.tanggallahir =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalLahir']);
    model.tempatlahir = dynamicData['tempatLahir'];
    model.alamatktp = dynamicData['alamatKTP'];
    model.alamatdomisili = dynamicData['alamatDomisili'];
    model.statusperkawinan = dynamicData['statusPerkawinan'];
    model.noktp = dynamicData['noKTP'];
    model.nokk = dynamicData['noKK'];
    model.nobpjskesehatan = dynamicData['noBPJSKesehatan'];
    model.nobpjstk = dynamicData['noBPJSTK'];
    model.nonpwp = dynamicData['noNPWP'];
    model.nip = dynamicData['nip'];
    model.idPangkat = MahasFormat.dynamicToInt(dynamicData['id_Pangkat']);
    model.idGolongandarah =
        MahasFormat.dynamicToInt(dynamicData['id_GolonganDarah']);
    model.jeniskelamin = dynamicData['jenisKelamin'];
    model.idKategoriSdm =
        MahasFormat.dynamicToInt(dynamicData['id_KategoriSdm']);
    model.fotoprofile = dynamicData['fotoProfile'];
    model.idPegawaiapproval =
        MahasFormat.dynamicToInt(dynamicData['id_PegawaiApproval']);
    model.namapegawaiapproval = dynamicData['namaPegawaiApproval'];
    model.approved = MahasFormat.dynamicToBool(dynamicData['approved']);
    model.tanggalrequest =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalRequest']);
    model.tanggalapproval =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalApproval']);
    model.alasantolak = dynamicData['alasanTolak'];
    model.sukubangsa = dynamicData['sukuBangsa'];

    return model;
  }
}

class ErrorprofileModel {
  String? status;
  String? message;

  ErrorprofileModel();

  static ErrorprofileModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static ErrorprofileModel fromDynamic(dynamic dynamicData) {
    final model = ErrorprofileModel();

    model.status = dynamicData['status'];
    model.message = dynamicData['message'];

    return model;
  }
}
