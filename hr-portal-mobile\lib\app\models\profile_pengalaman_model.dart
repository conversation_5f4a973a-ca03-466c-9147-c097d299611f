import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class PengalamanModel {
  int? id;
  int? idPegawai;
  String? nama;
  DateTime? daritanggal;
  DateTime? sampaitanggal;
  String? keterangan;
  String? berkaspendukung;

  PengalamanModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static PengalamanModel fromDynamic(dynamic dynamicData) {
    final model = PengalamanModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.nama = dynamicData['nama'];
    model.daritanggal =
        MahasFormat.dynamicToDateTime(dynamicData['dariTanggal']);
    model.sampaitanggal =
        MahasFormat.dynamicToDateTime(dynamicData['sampaiTanggal']);
    model.keterangan = dynamicData['keterangan'];
    model.berkaspendukung = dynamicData['berkasPendukung'];

    return model;
  }
}
