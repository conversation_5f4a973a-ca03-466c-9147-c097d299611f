import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_dropdown_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_file_component.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../controllers/profile_jenis_sertifikasi_setup_controller.dart';

class ProfileJenisSertifikasiSetupView
    extends GetView<ProfileJenisSertifikasiSetupController> {
  const ProfileJenisSertifikasiSetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      controller: controller.formCon,
      title: 'Informasi',
      children: () => [
        InputDropdownComponent(
          label: "<PERSON><PERSON>",
          controller: controller.jenisSertifikasiCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: '<PERSON><PERSON>',
          controller: controller.namaSertifikasiCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: 'No Sertifikasi',
          controller: controller.noSertifikasiCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDatetimeComponent(
          controller: controller.tanggalCon,
          label: 'Tanggal',
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDatetimeComponent(
          controller: controller.berlakuSampaiCon,
          label: 'Berlaku Sampai',
          required: true,
          editable: controller.formCon.editable,
        ),
        InputFileComponent(
          controller: controller.berkasPendukungCon,
          required: true,
          label: 'Berkas Pendukung (*pdf)',
          editable: controller.formCon.editable,
        ),
      ],
    );
  }
}
