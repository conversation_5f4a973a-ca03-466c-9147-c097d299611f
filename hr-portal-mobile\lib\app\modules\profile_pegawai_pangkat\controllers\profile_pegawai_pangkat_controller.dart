import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../models/pangkat_history_model.dart';
import '../../../routes/app_pages.dart';

class ProfilePegawaiPangkatController extends GetxController {
  final listCon = ListComponentController<PangkathistoryModel>(
    urlApi: (index, filter) =>
        '/api/PegawaiPangkat/History/List?pageIndex=$index',
    fromDynamic: PangkathistoryModel.fromDynamic,
    allowSearch: false,
  );

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PROFILE_PEGAWAI_PANGKAT_SETUP,
      parameters: {
        'id': id.toString(),
      },
    )?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }
}
