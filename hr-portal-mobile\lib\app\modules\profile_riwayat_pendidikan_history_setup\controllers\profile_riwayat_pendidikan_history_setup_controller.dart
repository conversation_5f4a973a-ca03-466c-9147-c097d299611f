import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/models/profile_riwayat_pendidikan_model.dart';
import 'package:path/path.dart' as p;

import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/models/api_list_resut_model.dart';
import '../../../mahas/models/api_result_model.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../models/klasifikasi_pendidikan_model.dart';
import '../../../models/pendidikan_model.dart';
import '../../../routes/app_pages.dart';

class ProfileRiwayatPendidikanHistorySetupController extends GetxController {
  late SetupPageController formCon;
  late bool reqDelete;
  late bool showStatus;
  late bool withActionBack;

  final pendidikanCon = InputDropdownController();
  final klasifikasiPendidikanCon = InputDropdownController();
  final programStudiCon = InputTextController();
  final dariTahunCon = InputTextController(type: InputTextType.number);
  final sampaiTahunCon = InputTextController(type: InputTextType.number);
  final keteranganCon = InputTextController(type: InputTextType.paragraf);
  final nomorIjazahCon = InputTextController();
  final fileCon = InputFileController(
    urlImage: true,
    type: FileType.custom,
    tipe: InputFileType.pdf,
    extension: ["pdf", "jpg", "jpeg", "png"],
  );

  final alasanTolakCon = InputTextController();

  RxBool approvalButtonsVisible = false.obs;
  RxBool approvalFromNotif = false.obs;
  RxBool allowED = false.obs;
  late dynamic statusApprovalHRD = null.obs;
  RxString status = "".obs;
  RxString namaHRD = "".obs;
  RxString namaPegawaiRequest = "".obs;
  RxString alasanTolakHRD = "".obs;
  Rx<DateTime> tglPermintaan = DateTime.now().obs;
  RxInt idPegawaiRiwayatPendidikan = 0.obs;

  @override
  void onInit() async {
    //init
    String notifParam = Get.parameters['approval'].toString();
    approvalFromNotif.value = notifParam == "true" ? true : false;
    String showStatusParam = Get.parameters['showStatus'].toString();
    showStatus = showStatusParam == "true" ? true : false;
    String withActionBackParam = Get.parameters['withActionBack'].toString();
    withActionBack = withActionBackParam == "true" ? true : false;

    //formcon
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PegawaiRiwayatPendidikan/History/$id',
      urlApiPost: () => '/api/PegawaiRiwayatPendidikan/History',
      urlApiPut: (id) => '/api/PegawaiRiwayatPendidikan/History/$id',
      urlApiDelete: (id) =>
          '/api/PegawaiRiwayatPendidikan/History?id=$id&idDivisi=${MahasConfig.selectedDivisi}',
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "id_PegawaiRiwayatPendidikan": idPegawaiRiwayatPendidikan.value,
        "id_KlasifikasiPendidikan": klasifikasiPendidikanCon.value,
        "id_Pendidikan": pendidikanCon.value,
        "dariTahun": dariTahunCon.value,
        "sampaiTahun": sampaiTahunCon.value,
        "keterangan": keteranganCon.value,
        "filePhoto": fileCon.value,
        "fileName": fileCon.name,
        "programStudi": programStudiCon.value,
        "nomorIjazah": nomorIjazahCon.value,
      },
      allowDelete: allowED.value,
      allowEdit: allowED.value,
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      apiToView: (json) {
        RiwayatpendidikanModel model = RiwayatpendidikanModel.fromJson(json);

        //mahascomponent
        pendidikanCon.value = model.idPendidikan;
        klasifikasiPendidikanCon.value = model.idKlasifikasipendidikan;
        programStudiCon.value = model.programstudi;
        dariTahunCon.value = model.daritahun;
        sampaiTahunCon.value = model.sampaitahun;
        keteranganCon.value = model.keterangan;
        nomorIjazahCon.value = model.nomorijazah;
        fileCon.value = model.pathFoto;

        //display non mahas
        namaHRD.value = model.pegawaiapprovalnama ?? "";
        alasanTolakHRD.value = model.alasantolak ?? "";
        namaPegawaiRequest.value = model.pegawainama ?? "";
        statusApprovalHRD = model.approved;
        tglPermintaan.value = model.tanggalrequest ?? DateTime.now();
        idPegawaiRiwayatPendidikan.value = model.id ?? 0;
        reqDelete = model.status == "DELETE" ? true : false;
        status.value = model.status!;

        //cek approval for allowedit or delete
        cekApproval();

        //approval
        if (approvalFromNotif.value && statusApprovalHRD == null) {
          approvalButtonsVisible.value = true;
        }

        if (model.pathFoto != null) {
          String extension = p.extension(model.pathFoto!).toLowerCase();
          if (extension == ".pdf") {
            updateState(InputFileType.pdf);
          } else {
            updateState(InputFileType.image);
          }
        }
      },
      onBeforeSubmit: () {
        if (!pendidikanCon.isValid) return false;
        if (!klasifikasiPendidikanCon.isValid) return false;
        if (!dariTahunCon.isValid) return false;
        if (!sampaiTahunCon.isValid) return false;
        return true;
      },
      onInit: () async {
        await getPendidikan();
        await getKlasifikasiPendidikan();
      },
    );

    super.onInit();
  }

  void updateState(InputFileType tipe) {
    fileCon.tipe = tipe;
  }

  //backAction
  Future<bool> _back() async {
    Get.until(
        (route) => route.settings.name == Routes.PROFILE_RIWAYAT_PENDIDIKAN);
    Get.toNamed(Routes.PROFILE_RIWAYAT_PENDIDIKAN_HISTORY);
    return true;
  }

  //cek approval parameters come from notifikasi
  void cekApproval() {
    if (approvalFromNotif.value || statusApprovalHRD != null) {
      allowED.value = false;
    } else {
      allowED.value = true;
    }
    if (withActionBack) {
      formCon.backAction = () {
        return _back();
      };
      formCon.deleteOnTap = (id) {
        return _back();
      };
    }
    formCon.setState(() {
      reqDelete ? formCon.allowEdit = false : formCon.allowEdit = allowED.value;
      formCon.allowDelete = allowED.value;
      if (reqDelete) {
        formCon.deleteCaption = "Batal Hapus";
        formCon.deleteDescription =
            "Yakin akan membatalkan permintaan hapus data ini?";
      }
    });
  }

  Future<void> getPendidikan() async {
    if (EasyLoading.isShow) {
      EasyLoading.dismiss();
    }
    EasyLoading.show();
    var r = await HttpApi.get("/api/Pendidikan");
    if (r.success) {
      RxList<PendidikanModel> listModel = RxList<PendidikanModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(PendidikanModel.fromDynamic(e));
      }
      if (pendidikanCon.items.isNotEmpty) {
        pendidikanCon.items.clear();
      }
      if (listModel.isNotEmpty && pendidikanCon.items.isEmpty) {
        pendidikanCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      }
    } else {
      Helper.fetchErrorMessage(r);
    }
    EasyLoading.dismiss();
  }

  Future<void> getKlasifikasiPendidikan() async {
    if (EasyLoading.isShow) {
      EasyLoading.dismiss();
    }
    EasyLoading.show();
    var r = await HttpApi.get("/api/KlasifikasiPendidikan");
    if (r.success) {
      RxList<KlasifikasiPendidikanModel> listModel =
          RxList<KlasifikasiPendidikanModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(KlasifikasiPendidikanModel.fromDynamic(e));
      }
      if (klasifikasiPendidikanCon.items.isNotEmpty) {
        klasifikasiPendidikanCon.items.clear();
      }
      if (listModel.isNotEmpty && klasifikasiPendidikanCon.items.isEmpty) {
        klasifikasiPendidikanCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      }
    } else {
      Helper.fetchErrorMessage(r);
    }
    EasyLoading.dismiss();
  }

  Future<void> approvalAction(bool approve) async {
    if (EasyLoading.isShow) {
      EasyLoading.dismiss();
    }
    EasyLoading.show();
    String id = Get.parameters['id'].toString();
    final body = {
      'approve': approve,
      'alasanTolak': alasanTolakCon.value,
    };
    ApiResultModel? r;
    String url = '/api/PegawaiRiwayatPendidikan/History/Approve/$id';
    r = await HttpApi.put(
      url,
      body: body,
    );
    if (r.success) {
      r = await HttpApi.get(
        '/api/PegawaiRiwayatPendidikan/History/$id',
      );
      if (r.success) {
        RiwayatpendidikanModel model = RiwayatpendidikanModel.fromJson(r.body);
        statusApprovalHRD = model.approved;
        alasanTolakHRD.value = model.alasantolak ?? "";
        approvalButtonsVisible.value = false;
        update();
      } else {
        Helper.fetchErrorMessage(r);
      }
    } else {
      Helper.fetchErrorMessage(r, httpMethod: HttpMethod.post);
    }
    EasyLoading.dismiss();
  }
}
