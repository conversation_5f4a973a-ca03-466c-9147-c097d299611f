import 'package:get/get.dart';
import 'package:hr_portal/app/models/tukar_jadwal_model.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../routes/app_pages.dart';

class PermintaanJadwalTukarJadwalController extends GetxController {
  final listCon = ListComponentController<TukarJadwalModel>(
    urlApi: (index, filter) =>
        '/api/PermintaanJadwal/TukarJadwal/List?pageIndex=$index',
    fromDynamic: (e) => TukarJadwalModel.fromDynamic(e),
    allowSearch: false,
  );

  bool isDinamis = MahasConfig.profileMenu.approvalDinamis;

  void addOnPress() {
    Get.toNamed(Routes.PERMINTAAN_JADWAL_TUKAR_JADWAL_SETUP)?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PERMINTAAN_JADWAL_TUKAR_JADWAL_SETUP,
      parameters: {'id': id.toString(), 'buttonVisibility': 'false'},
    )?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }
}
