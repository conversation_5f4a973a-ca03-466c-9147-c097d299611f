import 'package:hr_portal/app/mahas_complement/service_data.dart';

class JadwalService {
  static dynamic jadwal = {};

  static saveToLocalStorage() async {
    await mainStorage.put("jadwal", jadwal);
  }

  static loadDataFromDB() async {
    jadwal = await mainStorage.get("jadwal");
  }

  static savedata() {
    saveToLocalStorage();
  }

  static void clearData() {
    // profile.clear();
    saveToLocalStorage();
  }
}
