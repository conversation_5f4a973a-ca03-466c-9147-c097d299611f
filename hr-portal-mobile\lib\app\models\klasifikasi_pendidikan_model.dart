import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class KlasifikasiPendidikanModel {
  int? id;
  String? nama;

  KlasifikasiPendidikanModel();

  static KlasifikasiPendidikanModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static KlasifikasiPendidikanModel fromDynamic(dynamic dynamicData) {
    final model = KlasifikasiPendidikanModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.nama = dynamicData['nama'];

    return model;
  }
}
