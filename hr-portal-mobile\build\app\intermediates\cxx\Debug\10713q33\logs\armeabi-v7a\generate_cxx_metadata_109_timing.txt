# C/C++ build system timings
generate_cxx_metadata
  execute-generate-process
    exec-configure 3796ms
    [gap of 26ms]
  execute-generate-process completed in 3829ms
  [gap of 43ms]
  write-metadata-json-to-file 21ms
generate_cxx_metadata completed in 3901ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 15ms
generate_cxx_metadata completed in 29ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 11ms]
  create-invalidation-state 23ms
generate_cxx_metadata completed in 39ms

