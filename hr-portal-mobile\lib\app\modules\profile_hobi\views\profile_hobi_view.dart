import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:get/get.dart';

import '../../../models/profile_hobi_model.dart';
import '../controllers/profile_hobi_controller.dart';
import '../../../mahas/components/others/list_component.dart';
import '../../../mahas/components/mahas_themes.dart';

class ProfileHobiView extends GetView<ProfileHobiController> {
  const ProfileHobiView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Hobi'),
        centerTitle: true,
        actions: <Widget>[
          Padding(
            padding: const EdgeInsets.only(right: 10),
            child: GestureDetector(
              onTap: controller.addOnPress,
              child: const Icon(
                FontAwesomeIcons.plus,
                size: 24,
              ),
            ),
          ),
        ],
      ),
      body: ListComponent(
        controller: controller.listCon,
        itemBuilder: (ProfilehobiModel e) {
          return ListTile(
            title: Text(e.nama ?? "-", style: MahasThemes.title),
            onTap: () => controller.itemOnTab(e.id!),
          );
        },
      ),
    );
  }
}
