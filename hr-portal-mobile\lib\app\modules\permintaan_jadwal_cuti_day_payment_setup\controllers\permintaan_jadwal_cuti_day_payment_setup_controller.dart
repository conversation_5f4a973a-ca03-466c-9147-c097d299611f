import 'dart:convert';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_detail_component.dart';
import '../../../mahas/components/inputs/input_detail_setup_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/models/api_result_model.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../models/cuti_day_payment_model.dart';
import '../../../models/pegawai_approval.dart';
import '../../../models/tukar_jadwal_model.dart';

class PermintaanJadwalCutiDayPaymentSetupController extends GetxController {
  late SetupPageController formCon;
  final tanggalCon = InputDatetimeController();
  final tanggalPenggantiCon = InputDatetimeController();
  late InputDetailSetupControler<CutiDayPaymentDetailModel> detailSetupCon;
  late InputDetailControler<CutiDayPaymentDetailModel> detailApproveCon;
  late InputDetailSetupControler<CutiDayPaymentDetailModel>
      detailPenggantiSetupCon;
  late InputDetailControler<CutiDayPaymentDetailModel>
      detailPenggantiApproveCon;
  final jenisCon = InputDropdownController(items: [
    DropdownItem.init("Pengganti Lembur", "Pengganti Lembur"),
    DropdownItem.init("Pengganti Libur", "Pengganti Libur"),
  ]);
  final keteranganCon = InputTextController(type: InputTextType.paragraf);
  final sampaiTanggalCon = InputDatetimeController();
  final dariTanggalCon = InputDatetimeController();
  final berapaHariCon = InputTextController(type: InputTextType.number);
  final berapaHariPenggantiCon =
      InputTextController(type: InputTextType.number);

  final pegawaiCutiCon = InputTextController();
  final alasanTolakCon = InputTextController();
  final String nama = MahasConfig.profile!.nama!;
  late String approve;
  late bool allowED;
  Rxn<bool> accKadiv = Rxn<bool>();
  Rxn<bool> accManager = Rxn<bool>();
  RxBool isVisible = false.obs;
  late RxString pegawaiKadiv = ''.obs;
  late RxString pegawaiManager = ''.obs;
  late String kadiv;
  late bool isKadiv;
  final isDinamisApprove = MahasConfig.profileMenu.approvalDinamis;
  RxBool isStop = false.obs;

  RxBool batal = false.obs;
  final shiftGantiCon = InputDropdownController();
  List<CutiDayPaymentDetailModel> modelDetail = [];
  List<CutiDayPaymentDetailModel> modelDetailPengganti = [];
  RxList<PegawaiApprovalModel> modelApproval = <PegawaiApprovalModel>[].obs;
  int? id;
  RxString statusTolak = ''.obs;
  RxInt idPegawaiRequest = 0.obs;
  RxString tglPengajuan = ''.obs;

  Rxn<TukarjadwalrequestModel> shiftBaseModel = Rxn<TukarjadwalrequestModel>();
  Rxn<TukarjadwalrequestModel> shiftModel = Rxn<TukarjadwalrequestModel>();
  Rxn<TukarjadwalrequestModel> shiftPenggantiModel =
      Rxn<TukarjadwalrequestModel>();

  @override
  void onInit() {
    detailApprove();
    detailPenggantiApprove();
    cekApproval();
    detailSetUp();
    detailPenggantiSetUp();
    cekKadiv();
    tanggalCon.onChanged = () async {
      await getDataRequest(tanggalCon.value, shiftModel);
    };
    tanggalCon.onClear = () {
      shiftModel.value = null;
    };
    tanggalPenggantiCon.onChanged = () async {
      await getDataRequest(tanggalPenggantiCon.value, shiftPenggantiModel);
    };
    tanggalPenggantiCon.onClear = () {
      shiftPenggantiModel.value = null;
    };
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PermintaanJadwal/CutiDayPayment/$id',
      urlApiPost: () => '/api/PermintaanJadwal/CutiDayPayment',
      urlApiPut: (id) => '/api/PermintaanJadwal/CutiDayPayment/$id',
      urlApiDelete: (id) => '/api/PermintaanJadwal/CutiDayPayment/$id',
      allowDelete: allowED,
      allowEdit: allowED,
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "jumlahHari": berapaHariCon.value,
        "jenis": jenisCon.value,
        "keterangan": keteranganCon.value,
        "id_Shift": shiftGantiCon.value,
        "detail": detailSetupCon.values
            .map((e) => {
                  'tanggal': MahasFormat.dateToString(e.value.tanggal),
                })
            .toList(),
        "detailPengganti": detailPenggantiSetupCon.values
            .map((e) => {
                  'tanggal': MahasFormat.dateToString(e.value.tanggal),
                })
            .toList()
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (!jenisCon.isValid) return false;
        if (!keteranganCon.isValid) return false;
        berapaHariCon.value = detailSetupCon.values.length;
        if (!shiftGantiCon.isValid) return false;
        if (!detailSetupCon.isValid) return false;
        if (!dateValidation()) return false;
        if (!detailPenggantiSetupCon.isValid) return false;
        if (detailPenggantiSetupCon.values.length != berapaHariCon.value) {
          Helper.dialogWarning(
              "Tanggal Pengganti tidak sesuai dengan jumlah hari : ${berapaHariCon.value}");
          return false;
        }
        return true;
      },
      successMessage: Helper.succesPengajuanMessage(),
      apiToView: (json) {
        CutiDayPaymentModel model = CutiDayPaymentModel.fromJson(json);
        id = model.id;
        detailSetupCon.clear();
        detailPenggantiSetupCon.clear();
        modelDetail.clear();
        modelDetailPengganti.clear();
        detailApproveCon.clear();
        detailPenggantiApproveCon.clear();
        for (var i = 0; i < model.detail!.length; i++) {
          detailSetupCon.addValue(CutiDayPaymentDetailModel.init(
              model.detail![i].id, model.detail![i].tanggal));
          modelDetail.add(model.detail![i]);
        }
        for (var i = 0; i < modelDetail.length; i++) {
          detailApproveCon.addValue(CutiDayPaymentDetailModel.init(
              model.detail![i].id, model.detail![i].tanggal));
        }
        for (var i = 0; i < model.detailPengganti!.length; i++) {
          detailPenggantiSetupCon.addValue(CutiDayPaymentDetailModel.init(
              model.detailPengganti![i].id, model.detailPengganti![i].tanggal));
          modelDetailPengganti.add(model.detailPengganti![i]);
        }
        for (var i = 0; i < modelDetailPengganti.length; i++) {
          detailPenggantiApproveCon.addValue(CutiDayPaymentDetailModel.init(
              model.detailPengganti![i].id, model.detailPengganti![i].tanggal));
        }
        accManager.value = model.approvemanager;
        accKadiv.value = model.approvekadiv;
        pegawaiKadiv.value = model.pegawaikadiv!;
        pegawaiManager.value = model.pegawaimanager!;

        pegawaiCutiCon.value = model.pegawairequest;
        statusTolak.value = model.alasantolak ?? '';
        idPegawaiRequest.value = model.idPegawaiRequest!;

        jenisCon.value = model.jenis!;
        keteranganCon.value = model.keterangan!;
        dariTanggalCon.value = model.daritanggal!;
        sampaiTanggalCon.value = model.sampaitanggal!;
        berapaHariCon.value = model.jumlahhari!;
        berapaHariPenggantiCon.value = detailPenggantiSetupCon.values.length;
        shiftGantiCon.value = model.idShift!;

        var format = DateFormat("dd MMMM yyyy HH:mm");
        tglPengajuan.value = format.format(model.tanggalinput!);

        // approval
        if (isDinamisApprove == false && allowED == false) {
          if ((model.approvekadiv == null &&
                  model.idPegawaiKadiv == MahasConfig.profile!.id) ||
              (model.approvekadiv == true &&
                  model.approvemanager == null &&
                  model.idPegawaiManager == MahasConfig.profile!.id)) {
            isVisible.value = true;
          }
        }

        if (isDinamisApprove == true && allowED == false) {
          getDataApproval();
        }
      },
      onInit: () async {
        await getShift();
      },
    );
    detailSetupCon.onChanged = () {
      berapaHariCon.value =
          detailSetupCon.values.isEmpty ? null : detailSetupCon.values.length;
    };
    detailSetupCon.onBeforeSubmit = () {
      if (!lookUpValidation('tanggalDp', detailSetupCon, shiftModel)) {
        return false;
      }
      return true;
    };
    detailPenggantiSetupCon.onChanged = () {
      berapaHariPenggantiCon.value = detailPenggantiSetupCon.values.isEmpty
          ? null
          : detailPenggantiSetupCon.values.length;
    };
    detailPenggantiSetupCon.onBeforeSubmit = () {
      if (!lookUpValidation(
          'tanggalPengganti', detailPenggantiSetupCon, shiftPenggantiModel)) {
        return false;
      }
      return true;
    };
    super.onInit();
  }

  void detailApprove() {
    detailApproveCon = InputDetailControler<CutiDayPaymentDetailModel>(
      itemKey: (e) => e.id,
      fromDynamic: (e) => CutiDayPaymentDetailModel.fromDynamic(e),
      itemText: (e) => MahasFormat.displayDate(e.tanggal),
      urlApi: (index, filter) => '/api/PermintaanJadwal/CutiDayPayment/$id',
    );
  }

  void detailSetUp() {
    detailSetupCon = InputDetailSetupControler<CutiDayPaymentDetailModel>(
      itemKey: (e) => e.id,
      fromDynamic: (e) => CutiDayPaymentDetailModel.fromDynamic(e),
      itemText: (e) => MahasFormat.dateToString(e.tanggal) ?? "",
      onOpenForm: (id, e) {
        tanggalCon.value = e?.tanggal;
      },
      onFormInsert: (id) {
        if (!tanggalCon.isValid) return null;
        var r = CutiDayPaymentDetailModel();
        r.id = id;
        r.tanggal = tanggalCon.value;
        return r;
      },
    );
  }

  void detailPenggantiApprove() {
    detailPenggantiApproveCon = InputDetailControler<CutiDayPaymentDetailModel>(
      itemKey: (e) => e.id,
      fromDynamic: (e) => CutiDayPaymentDetailModel.fromDynamic(e),
      itemText: (e) => MahasFormat.displayDate(e.tanggal),
      urlApi: (index, filter) => '/api/PermintaanJadwal/CutiDayPayment/$id',
    );
  }

  void detailPenggantiSetUp() {
    detailPenggantiSetupCon =
        InputDetailSetupControler<CutiDayPaymentDetailModel>(
      itemKey: (e) => e.id,
      fromDynamic: (e) => CutiDayPaymentDetailModel.fromDynamic(e),
      itemText: (e) => MahasFormat.dateToString(e.tanggal) ?? "",
      onOpenForm: (id, e) {
        tanggalPenggantiCon.value = e?.tanggal;
      },
      onFormInsert: (id) {
        if (!tanggalPenggantiCon.isValid) return null;
        var r = CutiDayPaymentDetailModel();
        r.id = id;
        r.tanggal = tanggalPenggantiCon.value;
        return r;
      },
    );
  }

  void cekApproval() {
    approve = Get.parameters['approval'].toString();
    if (approve == "approval") {
      allowED = false;
    } else {
      allowED = true;
    }
  }

  void cekKadiv() {
    kadiv = Get.parameters['isKadiv'].toString();
    if (kadiv == "false") {
      isKadiv = false;
    } else {
      isKadiv = true;
    }
  }

  Future<void> getShift() async {
    var r = await HttpApi.get(
        '/api/PermintaanJadwal/CutiDayPayment/Shift?Id_Divisi=${MahasConfig.selectedDivisi}');
    if (r.success) {
      RxList<ShiftDayPaymentModel> listModel = RxList<ShiftDayPaymentModel>();
      var model = jsonDecode(r.body);
      for (var e in (model ?? [])) {
        listModel.add(ShiftDayPaymentModel.fromDynamic(e));
      }
      if (r.body != null && shiftGantiCon.items.isEmpty) {
        shiftGantiCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem(
                text: e.nama ?? '',
                value: e.id,
              ),
            )
            .toList();
      }
    } else {
      Helper.fetchErrorMessage(r);
    }
    EasyLoading.dismiss();
  }

  Future<void> approvalOnPress(bool approve) async {
    String urlKadiv = '/api/PermintaanJadwal/CutiDayPayment/Approve/Kadiv/';
    String urlManager = '/api/PermintaanJadwal/CutiDayPayment/Approve/Manager/';
    String urlApprove = '/api/PermintaanJadwal/CutiDayPayment/Approve/';
    var action = await Helper.approvalAction(
      id.toString(),
      approve,
      alasanTolakCon.value,
      accKadiv.value,
      urlKadiv,
      urlManager,
      isDinamis: isDinamisApprove,
      urlApprove: urlApprove,
    );
    if (action.success) {
      final url = '/api/PermintaanJadwal/CutiDayPayment/$id';
      ApiResultModel r;
      r = await HttpApi.get(url);
      if (r.success) {
        CutiDayPaymentModel model = CutiDayPaymentModel.fromJson(r.body);
        accKadiv.value = model.approvekadiv;
        accManager.value = model.approvemanager;
        statusTolak.value = model.alasantolak ?? '';
        if (isDinamisApprove) {
          getDataApproval();
        }
        isVisible.value = false;
        update();
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan approve, silahkan cek koneksi internet");
    } else if (!action.success) {
      Helper.dialogWarning(
        "Gagal melakukan approval",
        resMassage: action.message,
        noInternetMassage:
            "Gagal melakukan approval, silahkan cek koneksi internet",
      );
    }
  }

  Future<void> getDataApproval() async {
    EasyLoading.show();
    final url = '/api/PermintaanJadwal/CutiDayPayment/Approval/$id';
    final r = await HttpApi.get(url);
    modelApproval.clear();
    if (r.success) {
      if (r.body != null) {
        var data = json.decode(r.body);
        for (var e in data['datas']) {
          modelApproval.add(PegawaiApprovalModel.fromDynamic(e));
        }
        bool canApprove = modelApproval.any((item) =>
            item.idPegawaiapprove == MahasConfig.profile?.id &&
            item.approve == null);
        if (canApprove) {
          isVisible.value = true;
        }
        bool notApprove = modelApproval.any((item) => item.approve == false);
        if (notApprove) {
          isVisible.value = false;
        }
        isStop.value = modelApproval.any((item) => item.approve == false);
      } else {
        Helper.dialogWarning(r.message);
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan aksi, silahkan cek koneksi internet");
    } else if (r.statusCode == 404) {
      Helper.dialogWarning("Tidak Ada Data Pegawai Approval");
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }

  getDataRequest(DateTime tanggal, Rxn<TukarjadwalrequestModel> shift) async {
    if (EasyLoading.isShow) return;
    EasyLoading.show();
    final date = MahasFormat.dateToString(tanggal);
    final url =
        "/api/PermintaanJadwal/TukarJadwal/JadwalTanggal/Request?Id_Divisi=${MahasConfig.selectedDivisi}&Tanggal=$date";
    final r = await HttpApi.get(url);
    if (r.success) {
      if (r.body != null) {
        TukarjadwalrequestModel model =
            TukarjadwalrequestModel.fromJson(r.body);
        shift.value = model;
        shiftBaseModel.value = model;
      }
    } else if (MahasConfig.hasInternet == false) {
      shift.value = null;
      shiftBaseModel.value = null;
      Helper.dialogWarning("Gagal memuat data, silahkan cek koneksi internet");
    } else {
      shift.value = null;
      shiftBaseModel.value = null;
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }

  bool lookUpValidation(
      String jenis,
      InputDetailSetupControler<CutiDayPaymentDetailModel> setupCon,
      Rxn<TukarjadwalrequestModel> shift) {
    if (jenisCon.value == null) {
      Helper.dialogWarning("Tentukan jenis sebelum memilih tanggal");
      return false;
    }
    if (shift.value == null) {
      Helper.dialogWarning(
          "Tidak bisa memilih tanggal karena tidak memiliki shift");
      return false;
    }
    if (jenisCon.value == "Pengganti Libur") {
      if (jenis == "tanggalDp") {
        if (shift.value?.jammulai != null) {
          Helper.dialogWarning(
              "Tanggal DP Harus Diisi Dengan Hari Anda Libur Pada Jenis Pengganti Libur");
          return false;
        }
      } else if (jenis == "tanggalPengganti") {
        if (shift.value?.jammulai == null) {
          Helper.dialogWarning(
              "Tanggal Pengganti Harus Diisi Dengan Hari Anda Bekerja");
          return false;
        }
      }
    }
    if (setupCon.values
        .map((e) => e.value.tanggal)
        .contains(tanggalCon.value)) {
      Helper.dialogWarning("Tanggal Pengganti tidak boleh sama");
      return false;
    }
    return true;
  }

  bool dateValidation() {
    if (detailSetupCon.values.map((e) => e.value.tanggal).toSet().length !=
        detailSetupCon.values.length) {
      Helper.dialogWarning("Tanggal DP tidak boleh sama");
      return false;
    }
    if (detailPenggantiSetupCon.values
            .map((e) => e.value.tanggal)
            .toSet()
            .length !=
        detailPenggantiSetupCon.values.length) {
      Helper.dialogWarning("Tanggal Pengganti tidak boleh sama");
      return false;
    }
    return true;
  }
}
