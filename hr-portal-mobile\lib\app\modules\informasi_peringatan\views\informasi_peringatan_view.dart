import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/models/peringatan_model.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../mahas_complement/mahas_colors.dart';
import '../controllers/informasi_peringatan_controller.dart';

class InformasiPeringatanView extends GetView<InformasiPeringatanController> {
  const InformasiPeringatanView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Peringatan'),
        centerTitle: true,
      ),
      body: ListComponent(
        controller: controller.listCon,
        itemBuilder: (PeringatanModel e) {
          return InkWell(
            onTap: () => controller.itemOnTab(e.id!),
            child: Padding(
              padding:
                  const EdgeInsets.only(left: 12, right: 12, top: 8, bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 5),
                        Text(e.judul ?? ""),
                        const SizedBox(height: 5),
                        Text(e.peringatan ?? ""),
                      ],
                    ),
                  ),
                  Text(
                    MahasFormat.displayDate(e.tanggal!),
                    style: MahasColor.muted,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
