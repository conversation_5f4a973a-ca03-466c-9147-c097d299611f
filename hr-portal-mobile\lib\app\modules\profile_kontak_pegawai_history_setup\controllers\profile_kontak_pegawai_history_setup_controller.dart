import 'dart:convert';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/models/api_list_resut_model.dart';
import '../../../mahas/models/api_result_model.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../models/jenis_kontak_model.dart';
import '../../../models/profile_kontak_model.dart';
import '../../../routes/app_pages.dart';

class ProfileKontakPegawaiHistorySetupController extends GetxController {
  late SetupPageController formCon;
  late bool reqDelete;
  late bool showStatus;
  late bool withActionBack;

  final jenisKontakCon = InputDropdownController();
  final namaCon = InputTextController();
  final alasanTolakCon = InputTextController();
  RxString namaLabel = 'Nama'.obs;

  RxBool approvalButtonsVisible = false.obs;
  RxBool approvalFromNotif = false.obs;
  RxBool allowED = false.obs;
  late dynamic statusApprovalHRD = null.obs;
  RxString status = "".obs;
  RxString namaHRD = "".obs;
  RxString namaPegawaiRequest = "".obs;
  RxString alasanTolakHRD = "".obs;
  Rx<DateTime> tglPermintaan = DateTime.now().obs;
  RxInt idPegawaiKontak = 0.obs;

  @override
  void onInit() async {
    //init
    String notifParam = Get.parameters['approval'].toString();
    approvalFromNotif.value = notifParam == "true" ? true : false;
    String showStatusParam = Get.parameters['showStatus'].toString();
    showStatus = showStatusParam == "true" ? true : false;
    String withActionBackParam = Get.parameters['withActionBack'].toString();
    withActionBack = withActionBackParam == "true" ? true : false;

    //formcon
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PegawaiJenisKontak/History/$id',
      urlApiPost: () => '/api/PegawaiJenisKontak/History',
      urlApiPut: (id) => '/api/PegawaiJenisKontak/History/$id',
      urlApiDelete: (id) =>
          '/api/PegawaiJenisKontak/History?id=$id&idDivisi=${MahasConfig.selectedDivisi}',
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "id_Kontak": jenisKontakCon.value,
        "nama": namaCon.value,
        "id_PegawaiKontak": idPegawaiKontak.value,
      },
      allowDelete: allowED.value,
      allowEdit: allowED.value,
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      apiToView: (json) {
        ProfilekontakModel model = ProfilekontakModel.fromJson(json);

        //mahascomponent
        jenisKontakCon.value = model.idJeniskontak;
        namaCon.value = model.nama;
        namaLabel.value = jenisKontakCon.label ?? "Nama";

        //display non mahas
        namaHRD.value = model.pegawaiapprovalnama ?? "";
        alasanTolakHRD.value = model.alasanTolak ?? "";
        namaPegawaiRequest.value = model.pegawainama ?? "";
        statusApprovalHRD = model.approval;
        tglPermintaan.value = model.tglpermintaan ?? DateTime.now();
        idPegawaiKontak.value = model.id ?? 0;
        reqDelete = model.status == "DELETE" ? true : false;
        status.value = model.status!;

        //cek approval for allowedit or delete
        cekApproval();

        //approval
        if (approvalFromNotif.value && statusApprovalHRD == null) {
          approvalButtonsVisible.value = true;
        }
      },
      onBeforeSubmit: () {
        if (!jenisKontakCon.isValid) return false;
        if (!namaCon.isValid) return false;
        return true;
      },
      onInit: () async {
        await getKontak();
      },
    );

    super.onInit();
  }

  Future<void> getKontak() async {
    if (EasyLoading.isShow) {
      EasyLoading.dismiss();
    }
    EasyLoading.show();
    var r = await HttpApi.get("/api/JenisKontak");
    if (r.success) {
      RxList<JenisKontakModel> listModel = RxList<JenisKontakModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(JenisKontakModel.fromDynamic(e));
      }
      if (jenisKontakCon.items.isNotEmpty) {
        jenisKontakCon.items.clear();
      }
      if (listModel.isNotEmpty && jenisKontakCon.items.isEmpty) {
        jenisKontakCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id, label: e.namalabel),
            )
            .toList();
      }
      jenisKontakCon.onChangedVoid = () {
        getLabel();
      };
    } else {
      Helper.fetchErrorMessage(r);
    }
    EasyLoading.dismiss();
  }

  void getLabel() {
    namaLabel.value = jenisKontakCon.label ?? "Nama";
  }

  //backAction
  Future<bool> _back() async {
    Get.until((route) => route.settings.name == Routes.PROFILE_KONTAK_PEGAWAI);
    Get.toNamed(Routes.PROFILE_KONTAK_PEGAWAI_HISTORY);
    return true;
  }

  //cek approval parameters come from notifikasi
  void cekApproval() {
    if (approvalFromNotif.value || statusApprovalHRD != null) {
      allowED.value = false;
    } else {
      allowED.value = true;
    }
    if (withActionBack) {
      formCon.backAction = () {
        return _back();
      };
      formCon.deleteOnTap = (id) {
        return _back();
      };
    }
    formCon.setState(() {
      reqDelete ? formCon.allowEdit = false : formCon.allowEdit = allowED.value;
      formCon.allowDelete = allowED.value;
      if (reqDelete) {
        formCon.deleteCaption = "Batal Hapus";
        formCon.deleteDescription =
            "Yakin akan membatalkan permintaan hapus data ini?";
      }
    });
  }

  Future<void> approvalAction(bool approve) async {
    if (EasyLoading.isShow) {
      EasyLoading.dismiss();
    }
    EasyLoading.show();
    String id = Get.parameters['id'].toString();
    final body = {
      'approve': approve,
      'alasanTolak': alasanTolakCon.value,
    };
    ApiResultModel? r;
    String url = '/api/PegawaiJenisKontak/History/Approve/$id';
    r = await HttpApi.put(
      url,
      body: body,
    );
    if (r.success) {
      r = await HttpApi.get(
        '/api/PegawaiJenisKontak/History/$id',
      );
      if (r.success) {
        ProfilekontakModel model = ProfilekontakModel.fromJson(r.body);
        statusApprovalHRD = model.approval;
        alasanTolakHRD.value = model.alasanTolak ?? "";
        approvalButtonsVisible.value = false;
        update();
      }
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }
}
