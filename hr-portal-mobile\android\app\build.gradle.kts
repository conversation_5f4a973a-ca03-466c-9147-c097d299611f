import java.util.Properties
import java.io.FileInputStream
import java.io.FileNotFoundException

plugins {
    id("com.android.application")
    id("kotlin-android")
    id("com.google.gms.google-services")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

fun loadKeystoreProperties(filePath: String): Properties {
    val keystoreProperties = Properties()
    val keystoreFile = File(filePath)
    if (keystoreFile.exists()) {
        FileInputStream(keystoreFile).use { keystoreProperties.load(it) }
    } else {
        throw FileNotFoundException("Keystore properties file not found at path: $filePath")
    }
    return keystoreProperties
}

val localProperties = Properties()
val localPropertiesFile = rootProject.file("local.properties")
if (localPropertiesFile.exists()) {
    FileInputStream(localPropertiesFile).use { localProperties.load(it) }
}
val flutterVersionName = localProperties.getProperty("flutter.versionName") ?: "1.0"

android {
    compileSdk = 35
    ndkVersion = "27.0.12077973"
    // ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
        isCoreLibraryDesugaringEnabled = true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17.toString()
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 23
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        multiDexEnabled = true
    }

    signingConfigs {
        create("sanata") {
            val keystoreProperties = loadKeystoreProperties("${rootProject.projectDir}/app/src/sanata/key.properties")
            keyAlias = keystoreProperties["keyAlias"] as String
            keyPassword = keystoreProperties["keyPassword"] as String
            storeFile = keystoreProperties["storeFile"]?.let { 
                File("${rootProject.projectDir}/app/src/sanata/$it")
            }
            storePassword = keystoreProperties["storePassword"] as String
        }

        getByName("debug") {
            keyAlias = "AndroidDebugKey"
            keyPassword = "android"
            storeFile = file("debug.keystore")
            storePassword = "android"
        }
    }

    flavorDimensions("app")
    productFlavors {
        create("staging") {
            dimension = "app"
            resValue("string", "app_name", "stg HR Portal")
            namespace = "com.sanata.hrportal.staging"
            applicationId = "com.sanata.hrportal.staging"
            versionName = flutterVersionName
            signingConfig = signingConfigs.getByName("debug")
        }
        create("devsanata") {
            dimension = "app"
            resValue("string", "app_name", "dev HR Portal Sanata")
            namespace = "com.sanata.hrportal.dev"
            applicationId = "com.sanata.hrportal.dev"
            versionName = flutterVersionName
            signingConfig = signingConfigs.getByName("debug")
        }
        create("sanata") {
            dimension = "app"
            resValue("string", "app_name", "HR Portal Sanata")
            namespace = "com.sanata.hrportal"
            applicationId = "com.sanata.hrportal"
            versionName = flutterVersionName
            signingConfig = signingConfigs.getByName("sanata")
            // uncomment signingconfig above and comment signingconfig below for release per flavor
            // signingConfig = signingConfigs.getByName("debug")
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    implementation("org.jetbrains.kotlin:kotlin-stdlib:1.7.10")
    implementation("com.android.support:multidex:1.0.3")
    implementation(platform("com.google.firebase:firebase-bom:30.2.0"))
    implementation("com.google.firebase:firebase-analytics")
    implementation("com.google.android.material:material:1.7.0")
    implementation("androidx.multidex:multidex:2.0.1")
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:1.2.2")
    implementation("androidx.window:window:1.0.0")
    implementation("androidx.window:window-java:1.0.0")
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.3")
}