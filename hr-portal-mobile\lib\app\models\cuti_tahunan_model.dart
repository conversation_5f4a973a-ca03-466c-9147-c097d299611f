import 'dart:convert';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';

class CutitahunanModel {
  int? id;
  int? idDivisi;
  DateTime? tanggalinput;
  bool? approvekadiv;
  bool? approvemanager;
  String? alasan;
  String? alasantolak;
  int? idPegawaiRequest;
  String? divisi;
  String? pegawairequest;
  String? pegawaikadiv;
  String? pegawaimanager;
  int? idPegawaiKadiv;
  int? idPegawaiManager;
  int? jumlahHari;
  List<CutitahunanDetailModel>? detail;

  CutitahunanModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static CutitahunanModel fromDynamic(dynamic dynamicData) {
    final model = CutitahunanModel();

    model.id = dynamicData['id'];
    model.idDivisi = dynamicData['id_Divisi'];
    model.tanggalinput =
        MahasFormat.stringToDateTime(dynamicData['tanggalInput']);
    model.approvekadiv = dynamicData['approveKadiv'];
    model.approvemanager = dynamicData['approveManager'];
    model.alasan = dynamicData['alasan'];
    model.alasantolak = dynamicData['alasanTolak'];
    model.idPegawaiRequest = dynamicData['id_Pegawai_Request'];
    model.jumlahHari = dynamicData['jumlahHari'];
    model.divisi = dynamicData['divisi'];
    model.pegawairequest = dynamicData['pegawaiRequest'];
    model.pegawaikadiv = dynamicData['pegawaiKadiv'];
    model.pegawaimanager = dynamicData['pegawaiManager'];
    model.idPegawaiKadiv = dynamicData['id_Pegawai_Kadiv'];
    model.idPegawaiManager = dynamicData['id_Pegawai_Manager'];
    if (dynamicData['detail'] != null) {
      final detailT = dynamicData['detail'] as List;
      model.detail = [];
      for (var i = 0; i < detailT.length; i++) {
        model.detail!.add(CutitahunanDetailModel.fromDynamic(detailT[i]));
      }
    }

    return model;
  }
}

class CutitahunanDetailModel {
  int? id;
  DateTime? tanggal;
  bool? batal;
  String? alasanbatal;

  CutitahunanDetailModel();

  CutitahunanDetailModel.init(this.id, this.tanggal);

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static CutitahunanDetailModel fromDynamic(dynamic dynamicData) {
    final model = CutitahunanDetailModel();

    model.id = dynamicData['id'];
    model.tanggal = MahasFormat.stringToDateTime(dynamicData['tanggal']);
    model.batal = dynamicData['batal'];
    model.alasanbatal = dynamicData['alasanBatal'];

    return model;
  }
}

List<LookupCutiTahunanShiftModel> lookupCutiTahunanShiftModelFromJson(
        String str) =>
    List<LookupCutiTahunanShiftModel>.from(
        json.decode(str).map((x) => LookupCutiTahunanShiftModel.fromJson(x)));

class LookupCutiTahunanShiftModel {
  int? id;
  String? nama;
  String? kode;

  LookupCutiTahunanShiftModel({this.id, this.nama, this.kode});

  LookupCutiTahunanShiftModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    nama = json['nama'];
    kode = json['kode'];
  }
}

class CutiTahunanApprovalModel {
  int? idPegawaiapprove;
  String? pegawaiapprovenama;
  int? tingkatan;
  bool? approve;

  CutiTahunanApprovalModel();

  static CutiTahunanApprovalModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static CutiTahunanApprovalModel fromDynamic(dynamic dynamicData) {
    final model = CutiTahunanApprovalModel();

    model.idPegawaiapprove =
        MahasFormat.dynamicToInt(dynamicData['id_PegawaiApprove']);
    model.pegawaiapprovenama = dynamicData['pegawaiApproveNama'];
    model.tingkatan = MahasFormat.dynamicToInt(dynamicData['tingkatan']);
    model.approve = MahasFormat.dynamicToBool(dynamicData['approve']);

    return model;
  }
}

class QuotaCutiTahunanModel {
  int? tahun;
  int? quota;
  int? terpakai;
  int? sisacuti;
  DateTime? tanggalberlaku;
  DateTime? tanggalselesai;
  int? jumlahcuti;
  int? sisacutisebelumnya;
  DateTime? tanggalsisaselesai;
  int? totalcuti;

  QuotaCutiTahunanModel();

  static QuotaCutiTahunanModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static QuotaCutiTahunanModel fromDynamic(dynamic dynamicData) {
    final model = QuotaCutiTahunanModel();

    model.tahun = MahasFormat.dynamicToInt(dynamicData['tahun']);
    model.quota = MahasFormat.dynamicToInt(dynamicData['quota']);
    model.terpakai = MahasFormat.dynamicToInt(dynamicData['terpakai']);
    model.sisacuti = MahasFormat.dynamicToInt(dynamicData['sisaCuti']);
    model.tanggalberlaku =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalBerlaku']);
    model.tanggalselesai =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalSelesai']);
    model.jumlahcuti = MahasFormat.dynamicToInt(dynamicData['jumlahCuti']);
    model.sisacutisebelumnya =
        MahasFormat.dynamicToInt(dynamicData['sisaCutiSebelumnya']);
    model.tanggalsisaselesai =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalSisaSelesai']);
    model.totalcuti = MahasFormat.dynamicToInt(dynamicData['totalCuti']);

    return model;
  }
}
