import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_radio_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_colors.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas_complement/mahas_colors.dart';
import '../controllers/profile_data_pribadi_history_controller.dart';

class ProfileDataPribadiHistoryView
    extends GetView<ProfileDataPribadiHistoryController> {
  const ProfileDataPribadiHistoryView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      title: "Informasi Data Pribadi",
      controller: controller.formCon,
      children: () => [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(controller.tanggalRequest ?? ""),
            Obx(
              () => Text(controller.accManager.value == null
                  ? "Menunggu"
                  : controller.accManager.value == true
                      ? "Disetujui"
                      : "Ditolak"),
            ),
          ],
        ),
        const SizedBox(
          height: 10,
        ),
        Obx(
          () => Visibility(
            visible: controller.accManager.value == null,
            child: Row(
              children: [
                Text("${controller.kepada ?? "Kepada"} :"),
                Text(" ${controller.pegawai ?? ""}"),
              ],
            ),
          ),
        ),
        const SizedBox(
          height: 10,
        ),
        InputTextComponent(
          label: "Nama",
          controller: controller.namaCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "NRP/NIP/NITK",
          controller: controller.nipCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        Visibility(
          visible: MahasConfig.dinamisForm.datapribadi?.pangkatvisible ?? true,
          child: InputDropdownComponent(
            label: "Pangkat",
            controller: controller.pangkatCon,
            editable: controller.formCon.editable,
          ),
        ),
        Visibility(
          visible:
              MahasConfig.dinamisForm.datapribadi?.kategorisdmvisible ?? true,
          child: InputDropdownComponent(
            label: "Kategori SDM",
            controller: controller.kategoriSdmCon,
            editable: controller.formCon.editable,
          ),
        ),
        InputTextComponent(
          label: "Tempat Lahir",
          controller: controller.tempatLahirCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDatetimeComponent(
          label: "Tanggal Lahir",
          controller: controller.tanggalLahirCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDropdownComponent(
          label: "Golongan Darah",
          controller: controller.golonganDarahCon,
          editable: controller.formCon.editable,
        ),
        InputRadioComponent(
          label: "Jenis Kelamin",
          controller: controller.jenisKelaminCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Suku Bangsa",
          controller: controller.sukuBangsaCon,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Alamat KTP",
          controller: controller.alamatKtpCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Obx(
              () => Switch(
                activeColor: MahasColors.primary,
                value: controller.alamatSama.value,
                onChanged: (v) {
                  if (controller.formCon.editable) {
                    controller.alamatSama.value = v;
                    if (controller.alamatSama.value) {
                      controller.alamatDomisiliCon.value =
                          controller.alamatKtpCon.value;
                    } else {
                      if (controller.alamatDomisiliCon.value != null) {
                        controller.alamatDomisiliCon.value = null;
                      }
                    }
                    controller.update();
                  }
                },
              ),
            ),
            Text(
              "Alamat Domisili sama dengan alamat KTP",
              style: MahasThemes.muted,
            ),
          ],
        ),
        const SizedBox(
          height: 10,
        ),
        Obx(
          () => Visibility(
            visible: !controller.alamatSama.value,
            child: InputTextComponent(
              label: "Alamat Domisili",
              controller: controller.alamatDomisiliCon,
              required: true,
              editable: controller.formCon.editable,
            ),
          ),
        ),
        InputDropdownComponent(
          label: "Agama",
          controller: controller.agamaCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDropdownComponent(
          label: "Status Perkawinan",
          controller: controller.statusPerkawinanCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Nomor KTP",
          controller: controller.noKtpCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Nomor KK",
          controller: controller.noKKCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Nomor BPJS Kesehatan",
          controller: controller.noBpjsKesCon,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Nomor BPJS Tenaga Kerja",
          controller: controller.noBpjsTKcon,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Nomor NPWP",
          controller: controller.noNpwpCon,
          editable: controller.formCon.editable,
        ),
        Visibility(
          visible: controller.isFromApproval!,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 15,
              ),
              const Text(
                'Status Request :',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(
                height: 5,
              ),
              Container(
                decoration: BoxDecoration(
                    border: Border.all(
                      color: MahasColor.primaryGrey,
                    ),
                    borderRadius:
                        BorderRadius.circular(MahasThemes.borderRadius)),
                padding: const EdgeInsets.all(10),
                child: Obx(
                  () => Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Manager',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(
                        height: 5,
                      ),
                      Text(controller.pegawaiManager ?? ''),
                      controller.accManager.value == null
                          ? const Row(
                              children: [
                                Icon(
                                  FontAwesomeIcons.clock,
                                  size: 12,
                                ),
                                Text(" Menunggu")
                              ],
                            )
                          : controller.accManager.value == true
                              ? const Row(
                                  children: [
                                    Icon(
                                      FontAwesomeIcons.check,
                                      size: 12,
                                      color: MahasColor.colorGreen,
                                    ),
                                    Text(
                                      ' Disetujui',
                                      style: TextStyle(
                                        color: MahasColor.colorGreen,
                                      ),
                                    ),
                                  ],
                                )
                              : const Row(
                                  children: [
                                    Icon(
                                      FontAwesomeIcons.circleXmark,
                                      size: 12,
                                      color: MahasColors.red,
                                    ),
                                    Text(
                                      " Ditolak",
                                      style: TextStyle(color: MahasColors.red),
                                    ),
                                  ],
                                ),
                      controller.accManager.value == false
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Obx(
                                  () => Text(
                                      'Alasan : ${controller.alasanTolak.value}'),
                                ),
                              ],
                            )
                          : const SizedBox(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        Obx(
          () {
            return Visibility(
              visible: controller.accManager.value == null &&
                  controller.isFromApproval!,
              child: Column(
                children: [
                  const SizedBox(
                    height: 15,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Expanded(
                        child: ElevatedButton(
                            onPressed: () {
                              alert("tolak");
                            },
                            style: ElevatedButton.styleFrom(
                                backgroundColor: MahasColors.red),
                            child: const Text("Tolak")),
                      ),
                      const SizedBox(
                        width: 20,
                      ),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            controller.terimaOnPress();
                          },
                          child: const Text("Terima"),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  Future<dynamic> alert(String status) {
    return Get.dialog(
      AlertDialog(
        content: SizedBox(
          width: 1000,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              InputTextComponent(
                label: status == "tolak" ? "Alasan Ditolak" : "Alasan Batal",
                controller: controller.alasanTolakCon,
              ),
            ],
          ),
        ),
        contentPadding:
            const EdgeInsets.only(bottom: 0, top: 20, right: 10, left: 10),
        actionsPadding:
            const EdgeInsets.only(top: 0, bottom: 0, left: 10, right: 10),
        actions: [
          TextButton(
            child: Text(
              "Close",
              style: MahasThemes.muted,
            ),
            onPressed: () {
              Get.back(result: false);
            },
          ),
          TextButton(
            child: const Text(
              "Simpan",
            ),
            onPressed: () {
              controller.tolakOnPress();
              Get.back(result: true);
            },
          ),
        ],
      ),
    );
  }
}
