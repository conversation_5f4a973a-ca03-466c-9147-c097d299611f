import 'dart:convert';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_datetime_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_detail_setup_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_dropdown_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_text_component.dart';
import 'package:hr_portal/app/mahas/components/pages/setup_page_component.dart';
import 'package:hr_portal/app/mahas/models/api_result_model.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/mahas/services/http_api.dart';
import 'package:hr_portal/app/models/cuti_tukar_lembur.dart';

import '../../../mahas/mahas_config.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../models/pegawai_approval.dart';

class PermintaanJadwalCutiTukarLemburSetupController extends GetxController {
  late SetupPageController formCon;
  final alasanCon = InputTextController(type: InputTextType.paragraf);
  final tanggalCon = InputDatetimeController();
  late InputDetailSetupControler<CutitukarlemburDetailModel> detailSetupCon;
  final alasanTolakCon = InputTextController();
  final String nama = MahasConfig.profile!.nama!;
  late String approve;
  late bool allowED;
  Rxn<bool> accKadiv = Rxn<bool>();
  Rxn<bool> accManager = Rxn<bool>();
  RxBool isVisible = false.obs;
  late RxString pegawaiKadiv = ''.obs;
  late RxString pegawaiManager = ''.obs;
  late String kadiv;
  late bool isKadiv;
  final pegawaiCutiCon = InputTextController();
  RxString statusTolak = ''.obs;
  RxList detail = [].obs;
  RxInt idPegawaiRequest = 0.obs;

  RxBool batal = false.obs;
  final shiftBatalCon = InputDropdownController();
  final tanggalBatalCon = InputDropdownController();
  List<CutitukarlemburDetailModel> modelDetail = [];
  final isDinamisApprove = MahasConfig.profileMenu.approvalDinamis;
  RxBool isStop = false.obs;
  RxList<PegawaiApprovalModel> modelApproval = <PegawaiApprovalModel>[].obs;
  int? id;

  @override
  void onInit() {
    cekApproval();
    detailSetUp();
    cekKadiv();
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PermintaanJadwal/CutiTukarLembur/$id',
      urlApiPost: () => '/api/PermintaanJadwal/CutiTukarLembur',
      urlApiPut: (id) => '/api/PermintaanJadwal/CutiTukarLembur/$id',
      urlApiDelete: (id) => '/api/PermintaanJadwal/CutiTukarLembur/$id',
      allowDelete: allowED,
      allowEdit: allowED,
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "alasan": alasanCon.value,
        "detail": detailSetupCon.values
            .map((e) => {
                  'tanggal': MahasFormat.dateToString(e.value.tanggal),
                })
            .toList(),
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (!detailSetupCon.isValid) return false;
        if (!alasanCon.isValid) return false;
        return true;
      },
      apiToView: (json) {
        CutitukarlemburModel model = CutitukarlemburModel.fromJson(json);
        id = model.id;
        alasanCon.value = model.alasan;
        detailSetupCon.clear();
        modelDetail.clear();
        for (var i = 0; i < model.detail!.length; i++) {
          detailSetupCon.addValue(CutitukarlemburDetailModel.init(
              model.detail![i].id, model.detail![i].tanggal));
          modelDetail.add(model.detail![i]);
        }

        tanggalBatalCon.items = modelDetail
            .map<DropdownItem>((e) =>
                DropdownItem.init(MahasFormat.displayDate(e.tanggal), e.id))
            .toList();

        accManager.value = model.approvemanager;
        accKadiv.value = model.approvekadiv;
        pegawaiKadiv.value = model.pegawaikadiv!;
        pegawaiManager.value = model.pegawaimanager!;
        idPegawaiRequest.value = model.idPegawaiRequest!;

        pegawaiCutiCon.value = model.pegawairequest;
        statusTolak.value = model.alasantolak ?? '';
        detail.value = model.detail!;

        // approval
        if (isDinamisApprove == false && allowED == false) {
          if ((model.approvekadiv == null &&
                  model.idPegawaiKadiv == MahasConfig.profile!.id) ||
              (model.approvekadiv == true &&
                  model.approvemanager == null &&
                  model.idPegawaiManager == MahasConfig.profile!.id)) {
            isVisible.value = true;
          } else if (model.approvekadiv == true &&
              model.approvemanager == true &&
              model.idPegawaiManager == MahasConfig.profile!.id) {
            if (MahasConfig.dinamisForm.approval?.bisaBatal == true) {
              batal.value = true;
            }
          }
        }

        if (isDinamisApprove == true && allowED == false) {
          getDataApproval();
        }
      },
    );
    super.onInit();
  }

  void detailSetUp() {
    detailSetupCon = InputDetailSetupControler<CutitukarlemburDetailModel>(
      itemKey: (e) => e.id,
      fromDynamic: (e) => CutitukarlemburDetailModel.fromDynamic(e),
      itemText: (e) => MahasFormat.dateToString(e.tanggal) ?? "",
      onOpenForm: (id, e) {
        tanggalCon.value = e?.tanggal;
      },
      onFormInsert: (id) {
        if (!tanggalCon.isValid) return null;
        var r = CutitukarlemburDetailModel();
        r.id = id;
        r.tanggal = tanggalCon.value;
        return r;
      },
    );
  }

  void cekApproval() {
    approve = Get.parameters['approval'].toString();
    if (approve == "approval") {
      allowED = false;
    } else {
      allowED = true;
    }
  }

  void cekKadiv() {
    kadiv = Get.parameters['isKadiv'].toString();
    if (kadiv == "false") {
      isKadiv = false;
    } else {
      isKadiv = true;
    }
  }

  Future getShiftBatal() async {
    if (EasyLoading.isShow) return;
    EasyLoading.show();
    final url =
        '/api/PermintaanJadwal/CutiTukarLembur/Shift?Id_Divisi=${MahasConfig.selectedDivisi}';
    final r = await HttpApi.get(url);
    if (r.success) {
      List<LookupCutiTukarLemburShiftModel> model =
          lookupCutiTukarLemburShiftModelFromJson(r.body);
      if (r.body != null) {
        if (shiftBatalCon.items.isEmpty) {
          shiftBatalCon.items = model
              .map<DropdownItem>((e) => DropdownItem.init(e.nama, e.id))
              .toList();
        }
      } else {
        Helper.dialogWarning(r.message);
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan aksi, silahkan cek koneksi internet");
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }

  batalOnPress() async {
    if (!tanggalBatalCon.isValid) return false;
    if (!shiftBatalCon.isValid) return false;
    if (EasyLoading.isShow) return;
    EasyLoading.show();
    var url =
        '/api/PermintaanJadwal/CutiTukarLembur/HapusTanggal?id_header=$id&id_detail=${tanggalBatalCon.value}&id_Shift=${shiftBatalCon.value}';
    var r = await HttpApi.put(
      url,
    );

    if (r.success) {
      final url = '/api/PermintaanJadwal/CutiTukarLembur/$id';
      ApiResultModel r;
      r = await HttpApi.get(url);
      if (r.success) {
        CutitukarlemburModel model = CutitukarlemburModel.fromJson(r.body);
        accKadiv.value = model.approvekadiv;
        accManager.value = model.approvemanager;
        statusTolak.value = model.alasantolak ?? '';
        batal.value = false;
        update();
        Get.back(result: true);
        Helper.dialogSuccess("Berhasil Batal!");
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan aksi, silahkan cek koneksi internet");
    } else if (!r.success) {
      Helper.dialogWarning(
        "Gagal melakukan aksi",
        resMassage: r.message,
        noInternetMassage:
            "Gagal melakukan aksi, silahkan cek koneksi internet",
      );
    }
    EasyLoading.dismiss();
  }

  Future<void> approvalOnPress(bool approve) async {
    var id = Get.parameters['id'].toString();
    String urlKadiv = '/api/PermintaanJadwal/CutiTukarLembur/Approve/Kadiv/';
    String urlManager =
        '/api/PermintaanJadwal/CutiTukarLembur/Approve/Manager/';
    String urlApprove = '/api/PermintaanJadwal/CutiTukarLembur/Approve/';
    var action = await Helper.approvalAction(
      id,
      approve,
      alasanTolakCon.value,
      accKadiv.value,
      urlKadiv,
      urlManager,
      isDinamis: isDinamisApprove,
      urlApprove: urlApprove,
    );
    if (action.success) {
      final url = '/api/PermintaanJadwal/CutiTukarLembur/$id';
      ApiResultModel r;
      r = await HttpApi.get(url);
      if (r.success) {
        CutitukarlemburModel model = CutitukarlemburModel.fromJson(r.body);
        accKadiv.value = model.approvekadiv;
        accManager.value = model.approvemanager;
        statusTolak.value = model.alasantolak ?? '';
        if (isDinamisApprove) {
          getDataApproval();
        }
        isVisible.value = false;
        update();
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan approve, silahkan cek koneksi internet");
    } else if (!action.success) {
      Helper.dialogWarning(
        "Gagal melakukan approval",
        resMassage: action.message,
        noInternetMassage:
            "Gagal melakukan approval, silahkan cek koneksi internet",
      );
    }
  }

  Future<void> getDataApproval() async {
    EasyLoading.show();
    final url = '/api/PermintaanJadwal/CutiTukarLembur/Approval/$id';
    final r = await HttpApi.get(url);
    modelApproval.clear();
    if (r.success) {
      if (r.body != null) {
        var data = json.decode(r.body);
        for (var e in data['datas']) {
          modelApproval.add(PegawaiApprovalModel.fromDynamic(e));
        }
        bool canApprove = modelApproval.any((item) =>
            item.idPegawaiapprove == MahasConfig.profile?.id &&
            item.approve == null);
        if (canApprove) {
          isVisible.value = true;
        }
        bool notApprove = modelApproval.any((item) => item.approve == false);
        if (notApprove) {
          isVisible.value = false;
        }
        isStop.value = modelApproval.any((item) => item.approve == false);
      } else {
        Helper.dialogWarning(r.message);
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan aksi, silahkan cek koneksi internet");
    } else if (r.statusCode == 404) {
      Helper.dialogWarning("Tidak Ada Data Pegawai Approval");
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }
}
