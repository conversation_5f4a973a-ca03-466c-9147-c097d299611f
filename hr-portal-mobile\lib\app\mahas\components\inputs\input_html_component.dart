import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import '../../mahas_colors.dart';
import '../mahas_themes.dart';
import 'input_box_component.dart';

class InputHtmlController extends ChangeNotifier {
  String _htmlContent = '';
  late Function(VoidCallback fn) setState;

  InputHtmlController();

  void _init(Function(VoidCallback fn) setStateX) {
    setState = setStateX;
  }

  String get value => _htmlContent;

  set value(String? htmlContent) {
    _htmlContent = htmlContent ?? '';
    if (setState != null) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    super.dispose();
  }
}

class InputHtmlComponent extends StatefulWidget {
  final InputHtmlController controller;
  final String? label;
  final double? marginBottom;
  final bool? visibility;
  final EdgeInsets? padding;
  final double? maxHeight;

  const InputHtmlComponent({
    super.key,
    required this.controller,
    this.label,
    this.marginBottom,
    this.visibility = true,
    this.padding,
    this.maxHeight,
  });

  @override
  State<InputHtmlComponent> createState() => _InputHtmlComponentState();
}

class _InputHtmlComponentState extends State<InputHtmlComponent> {
  @override
  void initState() {
    widget.controller._init((fn) {
      if (mounted) {
        setState(fn);
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: widget.visibility!,
      child: InputBoxComponent(
        label: widget.label,
        marginBottom: widget.marginBottom,
        childText: '', // HTML content tidak ditampilkan sebagai text
        isRequired: false,
        children: Container(
          width: double.infinity,
          constraints: widget.maxHeight != null
              ? BoxConstraints(maxHeight: widget.maxHeight!)
              : null,
          decoration: BoxDecoration(
            color: MahasColors.dark.withValues(alpha: 0.01),
            border: Border.all(
              color: MahasColors.dark.withValues(alpha: 0.1),
              width: 1,
            ),
            borderRadius: BorderRadius.circular(MahasThemes.borderRadius),
          ),
          child: widget.controller._htmlContent.isEmpty
              ? Padding(
                  padding: widget.padding ?? const EdgeInsets.all(12.0),
                  child: Text(
                    'Tidak ada konten',
                    style: TextStyle(
                      color: MahasColors.dark.withValues(alpha: 0.6),
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                )
              : SingleChildScrollView(
                  padding: widget.padding ?? const EdgeInsets.all(8.0),
                  child: Html(
                    data: widget.controller._htmlContent,
                    style: {
                      "body": Style(
                        margin: Margins.zero,
                        padding: HtmlPaddings.zero,
                        fontSize: FontSize(14),
                        color: MahasColors.dark,
                      ),
                      "p": Style(
                        margin: Margins.only(bottom: 8),
                      ),
                      "h1, h2, h3, h4, h5, h6": Style(
                        margin: Margins.only(bottom: 8, top: 8),
                        fontWeight: FontWeight.bold,
                      ),
                      "ul, ol": Style(
                        margin: Margins.only(bottom: 8),
                        padding: HtmlPaddings.only(left: 16),
                      ),
                      "li": Style(
                        margin: Margins.only(bottom: 4),
                      ),
                      "a": Style(
                        color: Colors.blue,
                        textDecoration: TextDecoration.underline,
                      ),
                      "strong, b": Style(
                        fontWeight: FontWeight.bold,
                      ),
                      "em, i": Style(
                        fontStyle: FontStyle.italic,
                      ),
                    },
                  ),
                ),
        ),
      ),
    );
  }
}
