// To parse this JSON data, do
//
//     final absensiSuratTugasModel = absensiSuratTugasModelFromJson(jsonString);

import 'dart:convert';

import 'package:hr_portal/app/mahas/services/mahas_format.dart';

List<AbsensiSuratTugasModel> absensiSuratTugasModelFromJson(String str) =>
    List<AbsensiSuratTugasModel>.from(
        json.decode(str).map((x) => AbsensiSuratTugasModel.fromJson(x)));

class AbsensiSuratTugasModel {
  int? id;
  int? idPegawaiManager;
  int? idDivisi;
  String? lokasi;
  String? keterangan;
  DateTime? tanggalJam;
  String? status;
  String? device;
  String? os;
  bool? approveManager;
  String? alasanTolak;
  String? pegawaiManager;
  String? divisi;

  AbsensiSuratTugasModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static AbsensiSuratTugasModel fromDynamic(dynamic dynamicData) {
    final model = AbsensiSuratTugasModel();

    model.id = dynamicData["id"];
    model.idPegawaiManager = dynamicData["id_Pegawai_Manager"];
    model.idDivisi = dynamicData["id_Divisi"];
    model.lokasi = dynamicData["lokasi"];
    model.keterangan = dynamicData["keterangan"];
    model.tanggalJam = DateTime.parse(dynamicData["tanggalJam"]);
    model.status = dynamicData["status"];
    model.device = dynamicData["device"];
    model.os = dynamicData["os"];
    model.approveManager = dynamicData["approveManager"];
    model.alasanTolak = dynamicData["alasanTolak"];
    model.pegawaiManager = dynamicData["pegawaiManager"];
    model.divisi = dynamicData["divisi"];

    return model;
  }
}

class AbsensitugasModel {
  int? id;
  int? idPegawaiManager;
  int? idDivisi;
  String? lokasi;
  String? keterangan;
  DateTime? tanggaljam;
  String? status;
  String? device;
  String? os;
  bool? approvemanager;
  String? alasantolak;
  String? pegawaimanager;
  String? divisi;
  String? pathfoto;

  AbsensitugasModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static AbsensitugasModel fromDynamic(dynamic dynamicData) {
    final model = AbsensitugasModel();

    model.id = dynamicData['id'];
    model.idPegawaiManager = dynamicData['id_Pegawai_Manager'];
    model.idDivisi = dynamicData['id_Divisi'];
    model.lokasi = dynamicData['lokasi'];
    model.keterangan = dynamicData['keterangan'];
    model.tanggaljam = MahasFormat.dynamicToDateTime(dynamicData['tanggalJam']);
    model.status = dynamicData['status'];
    model.device = dynamicData['device'];
    model.os = dynamicData['os'];
    model.approvemanager = dynamicData['approveManager'];
    model.alasantolak = dynamicData['alasanTolak'];
    model.pegawaimanager = dynamicData['pegawaiManager'];
    model.divisi = dynamicData['divisi'];
    model.pathfoto = dynamicData['pathFoto'];

    return model;
  }
}
