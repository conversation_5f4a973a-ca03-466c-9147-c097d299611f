import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/mahas_colors.dart';
import '../controllers/login_password_controller.dart';

class LoginPasswordView extends GetView<LoginPasswordController> {
  const LoginPasswordView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child: Container(
            height: Get.height,
            padding: const EdgeInsets.all(30),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(
                  height: 100,
                ),
                Obx(
                  () => Text(
                    controller.isLogin.value == true
                        ? 'Login'
                        : 'Create Account',
                    style: const TextStyle(fontSize: 30),
                  ),
                ),
                const Spacer(),
                InputTextComponent(
                  label: 'Email',
                  controller: controller.emailCon,
                  required: true,
                ),
                InputTextComponent(
                  label: 'Password',
                  controller: controller.passwordCon,
                  required: true,
                ),
                Obx(
                  () => Visibility(
                    visible: controller.isLogin.value == false,
                    child: InputTextComponent(
                      label: 'Password Confirm',
                      controller: controller.passwordConfirmCon,
                      required: true,
                    ),
                  ),
                ),
                ElevatedButton(
                  style: ButtonStyle(
                    backgroundColor:
                        WidgetStatePropertyAll(MahasColors.primary),
                  ),
                  onPressed: () {
                    controller.onSubmit();
                  },
                  child: Obx(
                    () => Text(controller.isLogin.value == true
                        ? 'Login'
                        : 'Create Account'),
                  ),
                ),
                const Spacer(),
                Center(
                  child: Obx(
                    () => Text(
                      controller.isLogin.value == true
                          ? 'Dont have an account?'
                          : 'Allready have an account?',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Center(
                  child: InkWell(
                    onTap: () {
                      controller.isLogin.toggle();
                    },
                    child: Obx(
                      () => Text(
                        controller.isLogin.value == true
                            ? 'Create Account'
                            : 'Login',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: MahasColors.primary,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 50,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
