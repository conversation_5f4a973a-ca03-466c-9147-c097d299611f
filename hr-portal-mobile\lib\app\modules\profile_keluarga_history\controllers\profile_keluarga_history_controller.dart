import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../models/profile_keluarga_model.dart';
import '../../../routes/app_pages.dart';

class ProfileKeluargaHistoryController extends GetxController {
  final listCon = ListComponentController<ProfilekeluargaModel>(
    urlApi: (index, filter) =>
        '/api/PegawaiKeluarga/History/List?pageIndex=$index',
    fromDynamic: ProfilekeluargaModel.fromDynamic,
    allowSearch: false,
  );

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PROFILE_KELUARGA_HISTORY_SETUP,
      parameters: {
        'id': id.toString(),
        'showStatus': true.toString(),
      },
    )?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }
}
