import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_datetime_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_dropdown_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_file_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_lookup_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_text_component.dart';
import 'package:hr_portal/app/mahas/components/mahas_themes.dart';
import 'package:hr_portal/app/mahas/components/pages/setup_page_component.dart';

import '../../../mahas/services/helper.dart';
import '../../../mahas_complement/status_request.dart';
import '../controllers/permintaan_jadwal_izin_setup_controller.dart';

class PermintaanJadwalIzinSetupView
    extends GetView<PermintaanJadwalIzinSetupController> {
  const PermintaanJadwalIzinSetupView({super.key});

  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      controller: controller.formCon,
      title: controller.tittle,
      children: () => [
        Visibility(
          visible: controller.isManagement == true || controller.isKadiv == true
              ? true
              : false,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InputLookupComponent(
                label: 'Pegawai ${controller.tittle}',
                controller: controller.pegawaiIzinCon,
                required: true,
                editable: controller.formCon.editable,
                marginBottom: 5,
              ),
              InkWell(
                onTap: () async {
                  if (controller.idPegawaiRequest.value != 0) {
                    await Helper.fotoProfilOnTap(
                        controller.idPegawaiRequest.value);
                  } else {
                    Helper.dialogWarning("Pilih pegawai terlebih dahulu!");
                  }
                },
                child: SizedBox(
                  height: 30,
                  child: Text(
                    "Lihat Foto Profil Pegawai",
                    style: MahasThemes.link,
                    textAlign: TextAlign.left,
                  ),
                ),
              ),
            ],
          ),
        ),
        InputDatetimeComponent(
          controller: controller.dariTglCon,
          label: 'Dari Tanggal',
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDatetimeComponent(
          label: "Sampai Tanggal",
          controller: controller.sampaiTanggalCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Jumlah Hari",
          required: false,
          controller: controller.berapaHariCon,
          editable: false,
        ),
        Visibility(
            visible: controller.jenisIzin == true ? true : false,
            child: InputDropdownComponent(
              label: "Jenis ${controller.tittle}",
              controller: controller.jenisIzinCon,
              required: true,
              editable: controller.formCon.editable,
            )),
        Visibility(
          visible: controller.jenisIzin == true &&
                  controller.formCon.isState != SetupPageState.detail
              ? true
              : false,
          child: InputTextComponent(
            label: controller.tittleQuotaIzin,
            controller: controller.harikCon,
            required: false,
            editable: false,
          ),
        ),
        Obx(() => Visibility(
              visible: controller.showAlasan.value,
              child: InputTextComponent(
                label: 'Alasan',
                controller: controller.alasanCon,
                required: true,
                editable: controller.formCon.editable,
              ),
            )),
        Visibility(
          visible: controller.wajibDokumen.value,
          child: Obx(
            () => InputFileComponent(
              controller: controller.berkasPendukungCon,
              required: controller.wajibDokumen.value,
              label: 'Berkas Pendukung (*pdf)',
              editable: controller.formCon.editable,
            ),
          ),
        ),
        Obx(
          () => ApprovalStatusContainer(
            padding: 0,
            isEditable: controller.formCon.editable,
            isVisible: controller.isVisible,
            isBatal: controller.isBatal,
            allowED: controller.allowED,
            isDinamisApprove: controller.isDinamisApprove,
            alasanTolakController: controller.alasanTolakCon,
            onTolakPressed: () {
              controller.approvalOnPress(false);
            },
            onTerimaPressed: () {
              controller.approvalOnPress(true);
            },
            batalOnPress: () {
              var status = controller.isBatal.isTrue ? 'batal' : 'tolak';
              alert(status);
            },
            pegawaiKadiv: controller.pegawaiKadiv.value,
            pegawaiManager: controller.pegawaiManager.value,
            accKadiv: controller.accKadiv.value,
            accManager: controller.accManager.value,
            statusTolak: controller.statusTolak.value,
            modelApproval: controller.modelApproval,
            isStop: controller.isStop.value,
          ),
        ),
      ],
    );
  }

  Future<dynamic> alert(String status) {
    return Get.dialog(
      AlertDialog(
        content: SizedBox(
          width: 1000,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              InputTextComponent(
                label: status == "tolak" ? "Alasan Ditolak" : "Alasan Batal",
                controller: controller.alasanTolakCon,
                required: true,
              ),
            ],
          ),
        ),
        contentPadding:
            const EdgeInsets.only(bottom: 0, top: 20, right: 10, left: 10),
        actionsPadding:
            const EdgeInsets.only(top: 0, bottom: 0, left: 10, right: 10),
        actions: [
          TextButton(
            child: Text(
              "Close",
              style: MahasThemes.muted,
            ),
            onPressed: () {
              Get.back(result: false);
            },
          ),
          TextButton(
            child: const Text(
              "Simpan",
            ),
            onPressed: () {
              if (controller.alasanTolakCon.isValid) {
                if (status == "tolak") {
                  controller.approvalOnPress(false);
                } else if (status == "batal") {
                  controller.batalOnPress();
                }
                Get.back(result: true);
              }
            },
          ),
        ],
      ),
    );
  }
}
