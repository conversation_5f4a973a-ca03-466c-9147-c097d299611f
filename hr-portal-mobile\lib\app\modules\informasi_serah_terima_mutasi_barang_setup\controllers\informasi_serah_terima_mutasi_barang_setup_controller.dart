import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/mahas_colors.dart';
import 'package:hr_portal/app/mahas/mahas_service.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/mahas/services/http_api.dart';
import 'package:hr_portal/app/models/mutasi_barang_model.dart';
import 'package:hr_portal/app/routes/app_pages.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:qr_flutter/qr_flutter.dart';

class InformasiSerahTerimaMutasiBarangSetupController extends GetxController {
  RxString id = "".obs;
  Rx<MutasiBarangModel> mutasiBarangModel = MutasiBarangModel().obs;
  RxBool isLoadingData = true.obs;
  RxBool buttonApproveVisible = true.obs;

  @override
  void onInit() async {
    id.value = Get.parameters["id"] ?? "";
    await getMutasi();

    super.onInit();
  }

  Future<bool> backAction() async {
    Get.offNamedUntil(
      Routes.INFORMASI_SERAH_TERIMA_MUTASI_BARANG,
      ModalRoute.withName(Routes.INFORMASI_SERAH_TERIMA),
    );
    return true;
  }

  Future<void> getMutasi() async {
    final r = await HttpApi.get('/api/Mutasi/${id.value}');
    if (r.success) {
      mutasiBarangModel.value = MutasiBarangModel.fromJson(r.body);
      buttonApproveVisible.value =
          mutasiBarangModel.value.tanggalApprove == null;
      isLoadingData.value = false;
    } else {
      isLoadingData.value = false;
      bool noInternet =
          MahasService.isInternetCausedError(r.message.toString());
      if (!noInternet) {
        if (r.statusCode == 404) {
          Helper.dialogWarning("Data tidak ditemukan atau sudah dihapus!");
        } else {
          Helper.dialogWarning(r.message);
        }
      } else {
        Helper.dialogWarning(
            "Gagal memuat data, silahkan cek koneksi internet");
      }
    }
  }

  Future<void> approveOnTap() async {
    Helper.dialogQuestion(
      message: "Anda yakin menerima\n Mutasi Barang ini?",
      textConfirm: "Ya",
      confirmAction: () async {
        EasyLoading.show();
        final r =
            await HttpApi.put('/api/Mutasi/${mutasiBarangModel.value.noBukti}');
        if (r.success) {
          EasyLoading.dismiss();
          buttonApproveVisible.value = false;
          Get.snackbar(
            "Succes",
            "Berhasil menerima Mutasi Barang",
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: MahasColors.primary.withValues(alpha: 0.8),
            colorText: MahasColors.light,
          );
        } else {
          EasyLoading.dismiss();
          buttonApproveVisible.value = true;
          bool noInternet =
              MahasService.isInternetCausedError(r.message.toString());
          if (!noInternet) {
            if (r.statusCode == 404) {
              Helper.dialogWarning("Data tidak ditemukan atau sudah dihapus!");
            } else {
              Helper.dialogWarning(r.message);
            }
          } else {
            Helper.dialogWarning(
                "Gagal memuat data, silahkan cek koneksi internet");
          }
        }
      },
    );
  }

  void showQRCode() {
    if (mutasiBarangModel.value.id != null) {
      showMaterialModalBottomSheet(
        context: Get.context!,
        builder: (context) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('Mutasi Barang'),
              centerTitle: true,
            ),
            body: Container(
              width: Get.width,
              padding: const EdgeInsets.all(15),
              child: Center(
                child: QrImageView(
                  data: mutasiBarangModel.value.id.toString(),
                  version: QrVersions.auto,
                ),
              ),
            ),
          );
        },
      );
    } else {
      Helper.dialogWarning(
          "Tidak dapat menampilkan QR Code, Nomor Bukti tidak ditemukan!");
    }
  }
}
