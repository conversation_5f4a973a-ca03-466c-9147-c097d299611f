import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../models/profile_model.dart';
import '../../../routes/app_pages.dart';

class ProfileFotoHistoryController extends GetxController {
  final listCon = ListComponentController<HistoryProfileModel>(
    urlApi: (index, filter) => '/api/HistoryFotoProfile/List?pageIndex=$index',
    fromDynamic: HistoryProfileModel.fromDynamic,
    allowSearch: false,
  );

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PROFILE_FOTO_HISTORY_SETUP,
      parameters: {
        'id': id.toString(),
        'showStatus': true.toString(),
      },
    )?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }
}
