import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';

import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/others/list_component.dart';
import '../../../models/profile_dokumen_model.dart';
import '../controllers/profile_dokumen_controller.dart';

class ProfileDokumenView extends GetView<ProfileDokumenController> {
  const ProfileDokumenView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dokumen Pegawai'),
        centerTitle: true,
        actions: <Widget>[
          Padding(
            padding: const EdgeInsets.only(right: 10),
            child: GestureDetector(
              onTap: controller.addOnPress,
              child: const Icon(
                FontAwesomeIcons.plus,
                size: 24,
              ),
            ),
          ),
        ],
      ),
      body: ListComponent(
        controller: controller.listCon,
        itemBuilder: (DokumenModel e) {
          return ListTile(
            title: Row(
              children: [
                const Text("Nama Dokumen : "),
                Text(e.namadokumen ?? "-", style: MahasThemes.title),
              ],
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text("Nomor : "),
                    Text(e.nomor ?? "-", style: MahasThemes.muted),
                  ],
                ),
              ],
            ),
            onTap: () => controller.itemOnTab(e.id!),
          );
        },
      ),
    );
  }
}
