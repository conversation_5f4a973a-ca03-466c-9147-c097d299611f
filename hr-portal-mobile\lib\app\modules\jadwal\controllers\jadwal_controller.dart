import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/mahas/services/http_api.dart';
import 'package:hr_portal/app/models/jadwal.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas_complement/jadwal_storage.dart';

class JadwalController extends GetxController {
  var focusDate = DateTime.now().obs;
  JadwalModel? data;
  RxList<JadwalDetailModel> listData = <JadwalDetailModel>[].obs;

  void refreshData(DateTime date) async {
    if (EasyLoading.isShow) {
      return;
    }
    EasyLoading.show();
    var r = await HttpApi.get(
        "/api/Jadwal?Bulan=${date.month}&Tahun=${date.year}&Id_Divisi=${MahasConfig.selectedDivisi}");
    if (r.success) {
      JadwalService.jadwal = r.body;
      JadwalService.savedata();
      data = JadwalModel.fromJson(r.body);
      focusDate.value = date;
    } else {
      focusDate.value = DateTime.now();
      Helper.dialogWarning("Tidak Ada Jadwal Final");
    }
    EasyLoading.dismiss();
  }

  void onPageChanged(DateTime focusedDay) {
    refreshData(focusedDay);
  }

  void onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    focusDate.value = selectedDay;
    listData.clear();
    listData.value =
        data?.detail?.where((e) => e.tanggal == selectedDay.day).toList() ?? [];
  }

  bool selectedDayPredicate(DateTime date) {
    return focusDate.value == date;
  }

  void itemOnTab(List<Widget> children) {
    showCustomModalBottomSheet(
      context: Get.context!,
      builder: (context) => Container(
        margin: const EdgeInsets.only(bottom: 20, left: 20, right: 20),
        decoration: BoxDecoration(
          borderRadius:
              BorderRadius.all(Radius.circular(MahasThemes.borderRadius)),
          color: Colors.white,
        ),
        child: Column(
          children: children,
        ),
      ),
      containerWidget: (_, animation, child) => SafeArea(
        child: Column(
          children: [Expanded(child: Container()), child],
        ),
      ),
      expand: false,
    );
  }

  @override
  void onInit() async {
    if (MahasConfig.hasInternet == true) {
      refreshData(focusDate.value);
    } else {
      if (JadwalService.jadwal != null) {
        data = JadwalModel.fromJson(JadwalService.jadwal);
      }
    }
    super.onInit();
  }
}
