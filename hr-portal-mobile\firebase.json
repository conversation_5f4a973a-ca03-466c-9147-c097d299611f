{"flutter": {"platforms": {"android": {"default": {"projectId": "hr-portal-staging", "appId": "1:171917817792:android:8b63c9687c32a38e37fa25", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "hr-portal-staging", "appId": "1:171917817792:ios:bf088a84ad3e38ed37fa25", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "macos": {"default": {"projectId": "hr-portal-staging", "appId": "1:171917817792:ios:bf088a84ad3e38ed37fa25", "uploadDebugSymbols": false, "fileOutput": "macos/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "hr-portal-staging", "configurations": {"android": "1:171917817792:android:8b63c9687c32a38e37fa25"}}, "lib/firebase_options_staging.dart": {"projectId": "hr-portal-staging", "configurations": {"android": "1:171917817792:android:8b63c9687c32a38e37fa25", "ios": "1:171917817792:ios:bf088a84ad3e38ed37fa25", "macos": "1:171917817792:ios:bf088a84ad3e38ed37fa25", "web": "1:171917817792:web:f6dcb73da4433fe637fa25", "windows": "1:171917817792:web:d379b3c4dcb0dd6237fa25"}}}}}}