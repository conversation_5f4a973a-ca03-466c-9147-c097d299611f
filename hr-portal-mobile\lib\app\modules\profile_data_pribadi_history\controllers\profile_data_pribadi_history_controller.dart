import 'dart:convert';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_radio_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/models/api_list_resut_model.dart';
import '../../../mahas/models/api_result_model.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../models/agama_model.dart';
import '../../../models/golongan_darah_model.dart';
import '../../../models/kategori_sdm_model.dart';
import '../../../models/pangkat_model.dart';
import '../../../models/profile_model.dart';

class ProfileDataPribadiHistoryController extends GetxController {
  late SetupPageController formCon;
  final namaCon = InputTextController();
  final nipCon = InputTextController();
  final tempatLahirCon = InputTextController();
  final tanggalLahirCon = InputDatetimeController();
  final alamatKtpCon = InputTextController(type: InputTextType.paragraf);
  final alamatDomisiliCon = InputTextController(type: InputTextType.paragraf);
  final agamaCon = InputDropdownController();
  final statusPerkawinanCon = InputDropdownController(
    items: [
      DropdownItem.init("Kawin", 1),
      DropdownItem.init("Belum Kawin", 2),
    ],
  );
  final noKtpCon = InputTextController();
  final noKKCon = InputTextController();
  final noBpjsKesCon = InputTextController();
  final noBpjsTKcon = InputTextController();
  final noNpwpCon = InputTextController();
  final emailCon = InputTextController(type: InputTextType.email);
  final mulaiBekerjaCon = InputDatetimeController();
  final statusPegawaiCon = InputTextController();
  final selesaiBekerjaCon = InputDatetimeController();
  final pangkatCon = InputDropdownController();
  final kategoriSdmCon = InputDropdownController();
  final golonganDarahCon = InputDropdownController();
  final sukuBangsaCon = InputTextController();
  final jenisKelaminCon = InputRadioController(
    items: [
      RadioButtonItem(text: "Laki-laki", value: 1),
      RadioButtonItem(text: "Perempuan", value: 2),
    ],
  );
  String? tanggalRequest;
  String? status;
  String? pegawai;
  String? pegawaiManager;
  String? kepada;
  RxString alasanTolak = "-".obs;
  bool? isFromApproval;
  Rxn<bool> accManager = Rxn<bool>();
  final alasanTolakCon = InputTextController();

  RxBool alamatSama = false.obs;

  @override
  void onInit() async {
    isFromApproval = Get.parameters['approval'] == 'true';
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/HistoryDataPegawai/$id',
      urlApiPut: (id) => '/api/HistoryDataPegawai/$id',
      urlApiDelete: (id) => '/api/HistoryDataPegawai/$id',
      allowDelete: !isFromApproval!,
      allowEdit: !isFromApproval!,
      itemKey: (e) => e['id'],
      bodyApi: (id) => {
        "id_Pegawai": MahasConfig.profile!.id,
        "id_Divisi": MahasConfig.selectedDivisi,
        "nama": namaCon.value,
        "tanggalLahir": MahasFormat.dateToString(tanggalLahirCon.value),
        "tempatLahir": tempatLahirCon.value,
        "alamatKTP": alamatKtpCon.value,
        "alamatDomisili": alamatDomisiliCon.value,
        "id_Agama": agamaCon.value,
        "statusPerkawinan":
            statusPerkawinanCon.value == 1 ? "Kawin" : "Belum Kawin",
        "noKTP": noKtpCon.value,
        "noKK": noKKCon.value,
        "noBPJSKesehatan": noBpjsKesCon.value,
        "noBPJSTK": noBpjsTKcon.value,
        "noNPWP": noNpwpCon.value,
        "nip": nipCon.value,
        "id_Pangkat": pangkatCon.value,
        "id_GolonganDarah": golonganDarahCon.value,
        "id_KategoriSdm": kategoriSdmCon.value,
        "sukuBangsa": sukuBangsaCon.value,
        "jenisKelamin": jenisKelaminCon.value == 1 ? "Laki-laki" : "Perempuan",
      },
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (!namaCon.isValid) return false;
        if (!tanggalLahirCon.isValid) return false;
        if (!tempatLahirCon.isValid) return false;
        if (!alamatKtpCon.isValid) return false;
        if (!alamatDomisiliCon.isValid) return false;
        if (!agamaCon.isValid) return false;
        if (!statusPerkawinanCon.isValid) return false;
        if (!noKtpCon.isValid) return false;
        if (!noKKCon.isValid) return false;
        if (!noBpjsKesCon.isValid) return false;
        if (!noBpjsTKcon.isValid) return false;
        if (!noNpwpCon.isValid) return false;
        if (!nipCon.isValid) return false;
        if (!pangkatCon.isValid) return false;
        if (!golonganDarahCon.isValid) return false;
        if (!sukuBangsaCon.isValid) return false;
        if (!jenisKelaminCon.isValid) return false;

        return true;
      },
      apiToView: (json) {
        HistoryProfileModel model = HistoryProfileModel.fromJson(json);
        if (model.alamatktp!.isNotEmpty &&
            model.alamatdomisili!.isNotEmpty &&
            model.alamatktp == model.alamatdomisili) {
          alamatSama.value = true;
          update();
        }
        namaCon.value = model.nama;
        nipCon.value = model.nip;
        tempatLahirCon.value = model.tempatlahir;
        tanggalLahirCon.value = model.tanggallahir;
        alamatKtpCon.value = model.alamatktp;
        alamatDomisiliCon.value = model.alamatdomisili;
        agamaCon.value = model.idAgama;
        statusPerkawinanCon.value = null;
        if (model.statusperkawinan!.isNotEmpty) {
          statusPerkawinanCon.value = model.statusperkawinan == "Kawin" ? 1 : 2;
        }
        noKtpCon.value = model.noktp;
        noKKCon.value = model.nokk;
        noBpjsKesCon.value = model.nobpjskesehatan;
        noBpjsTKcon.value = model.nobpjstk;
        noNpwpCon.value = model.nonpwp;
        pangkatCon.value = model.idPangkat;
        golonganDarahCon.value = model.idGolongandarah;
        sukuBangsaCon.value = model.sukubangsa;
        kategoriSdmCon.value = model.idKategoriSdm;
        jenisKelaminCon.value = null;
        if (model.jeniskelamin!.isNotEmpty) {
          jenisKelaminCon.value = model.jeniskelamin == "Laki-laki" ? 1 : 2;
        }
        tanggalRequest = MahasFormat.dateToString(model.tanggalrequest);
        if (MahasConfig.profile!.id! == model.idPegawaiapproval &&
            isFromApproval == true) {
          pegawai = model.nama;
        } else {
          pegawai = model.namapegawaiapproval;
        }
        status = model.approved == null
            ? "Menunggu"
            : model.approved == true
                ? "Disetujui"
                : "Ditolak";
        cekIsHrd(model.idPegawaiapproval!);
        accManager.value = model.approved;
        pegawaiManager = model.namapegawaiapproval;
        alasanTolak.value = model.alasantolak ?? "-";
      },
      onInit: () async {
        await getAgama();
      },
    );
    super.onInit();
  }

  //start get data procedure (must inline)
  Future<void> getAgama() async {
    EasyLoading.show();
    var r = await HttpApi.get("/api/Agama");
    if (r.success) {
      RxList<AgamaModel> listModel = RxList<AgamaModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(AgamaModel.fromDynamic(e));
      }
      if (agamaCon.items.isNotEmpty) {
        agamaCon.items.clear();
      }
      if (r.body != null && agamaCon.items.isEmpty) {
        agamaCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      }
    } else {
      if (EasyLoading.isShow) {
        EasyLoading.dismiss();
      }
    }
    await getPangkat();
  }

  Future<void> getPangkat() async {
    var r = await HttpApi.get("/api/Pangkat");
    if (r.success) {
      RxList<PangkatModel> listModel = RxList<PangkatModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(PangkatModel.fromDynamic(e));
      }
      if (pangkatCon.items.isNotEmpty) {
        pangkatCon.items.clear();
      }
      if (r.body != null && pangkatCon.items.isEmpty) {
        pangkatCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      } else {
        if (EasyLoading.isShow) {
          EasyLoading.dismiss();
        }
      }
    }
    await getGolonganDarah();
  }

  Future<void> getGolonganDarah() async {
    var r = await HttpApi.get("/api/GolonganDarah");
    if (r.success) {
      RxList<GolonganDarahModel> listModel = RxList<GolonganDarahModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(GolonganDarahModel.fromDynamic(e));
      }
      if (golonganDarahCon.items.isNotEmpty) {
        golonganDarahCon.items.clear();
      }
      if (r.body != null && golonganDarahCon.items.isEmpty) {
        golonganDarahCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      } else {
        if (EasyLoading.isShow) {
          EasyLoading.dismiss();
        }
      }
    }
    await getKategoriSdm();
  }

  Future<void> getKategoriSdm() async {
    var r = await HttpApi.get("/api/KategoriSdm");
    if (r.success) {
      RxList<KategoriSdmModel> listModel = RxList<KategoriSdmModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(KategoriSdmModel.fromDynamic(e));
      }
      if (kategoriSdmCon.items.isNotEmpty) {
        kategoriSdmCon.items.clear();
      }
      if (r.body != null && kategoriSdmCon.items.isEmpty) {
        kategoriSdmCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      } else {
        if (EasyLoading.isShow) {
          EasyLoading.dismiss();
        }
      }
    }
    EasyLoading.dismiss();
  }
  //end get data procedure (must inline)

  void cekIsHrd(int id) {
    if (MahasConfig.profile!.id! == id && isFromApproval == true) {
      kepada = "Oleh";
    } else {
      kepada = "Kepada";
    }
  }

  void tolakOnPress() {
    var id = Get.parameters['id'].toString();
    tolak(id, false);
  }

  void terimaOnPress() {
    var id = Get.parameters['id'].toString();
    terima(id, true);
  }

  void terima(String id, bool approve) async {
    if (EasyLoading.isShow) return;
    EasyLoading.show();
    final body = {
      'approve': approve,
      'alasanTolak': alasanTolakCon.value ?? '-',
    };
    ApiResultModel? r;
    final url = '/api/HistoryDataPegawai/Approval/$id';
    r = await HttpApi.put(
      url,
      body: body,
    );
    if (r.success == true) {
      accManager.value = true;
    } else if (r.success == false) {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }

  void tolak(String id, bool approve) async {
    if (EasyLoading.isShow) return;
    EasyLoading.show();
    final body = {
      'approve': approve,
      'alasanTolak': alasanTolakCon.value ?? '-',
    };
    ApiResultModel? r;
    final url = '/api/HistoryDataPegawai/Approval/$id';
    r = await HttpApi.put(
      url,
      body: body,
    );
    if (r.success == true) {
      accManager.value = false;
      alasanTolak.value = alasanTolakCon.value ?? "-";
    } else if (r.success == false) {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }
}
