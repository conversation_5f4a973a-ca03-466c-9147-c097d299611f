import 'package:get/get.dart';
import '../../../controllers/auth_controller.dart';
import '../../../mahas/mahas_service.dart';
import '../../../routes/app_pages.dart';

class LoginController extends GetxController {
  AuthController authCon = AuthController.instance;
  var demo = false.obs;

  void appleLoginOnPress() async {
    await authCon.signInWithApple();
  }

  void googleLoginOnPress() async {
    await authCon.signInWithGoogle();
  }

  void emailLoginOnPress() async {
    Get.toNamed(Routes.LOGIN_PASSWORD);
  }

  void demoOnPress() async {
    await authCon.signInWithPassword('<EMAIL>', '123456');
  }

  @override
  void onInit() async {
    demo.value = remoteConfig.getBool('demo');
    super.onInit();
  }
}
