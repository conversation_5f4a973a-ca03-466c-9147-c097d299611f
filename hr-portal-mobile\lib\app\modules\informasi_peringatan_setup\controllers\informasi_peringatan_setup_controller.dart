import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/models/peringatan_model.dart';
import 'package:path/path.dart' as p;

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';

class InformasiPeringatanSetupController extends GetxController {
  late SetupPageController formCon;

  final judulCon = InputTextController();
  final peringatanCon = InputTextController();
  final keteranganCon = InputTextController(type: InputTextType.paragraf);
  final tanggalCon = InputDatetimeController();
  final fileCon = InputFileController(
    urlImage: true,
    type: FileType.custom,
    tipe: InputFileType.pdf,
    extension: ["pdf", "jpg", "jpeg", "png"],
  );

  @override
  void onInit() {
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/Peringatan/$id',
      urlApiPost: () => '/api/Peringatan',
      urlApiPut: (id) => '/api/Peringatan/$id',
      allowDelete: false,
      allowEdit: false,
      bodyApi: (id) => {},
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        return true;
      },
      apiToView: (json) {
        PeringatanModel model = PeringatanModel.fromJson(json);
        peringatanCon.value = model.peringatan;
        judulCon.value = model.judul;
        keteranganCon.value = model.keterangan;
        tanggalCon.value = model.tanggal;
        fileCon.value = model.pathfile;

        if (model.pathfile != null) {
          String extension = p.extension(model.pathfile!).toLowerCase();
          if (extension == ".pdf") {
            updateState(InputFileType.pdf);
          } else {
            updateState(InputFileType.image);
          }
        }
      },
    );
    if (EasyLoading.isShow) return;
    super.onInit();
  }

  void updateState(InputFileType tipe) {
    fileCon.tipe = tipe;
  }
}
