import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/constant/environment_constant.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:hr_portal/app/routes/app_pages.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

class LocalNotificationService {
  static final _notificationsPlugin = FlutterLocalNotificationsPlugin();

  void initialize() async {
    var androidInitialization =
        AndroidInitializationSettings(EnvironmentConstant.localNotifImage);
    var iOSInitialization = const DarwinInitializationSettings();
    final InitializationSettings initializationSettings =
        InitializationSettings(
            android: androidInitialization, iOS: iOSInitialization);

    final NotificationAppLaunchDetails? notificationAppLaunchDetails =
        await _notificationsPlugin.getNotificationAppLaunchDetails();

    final didNotificationLaunchApp =
        notificationAppLaunchDetails?.didNotificationLaunchApp ?? false;
    if (didNotificationLaunchApp) {
      NotificationResponse payload =
          notificationAppLaunchDetails!.notificationResponse!;
      notificationTapBackground(payload);
    } else {
      _notificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: notificationTapBackground,
        // onDidReceiveBackgroundNotificationResponse: notificationTapBackground,
      );
    }

    tz.initializeTimeZones();
  }

  void notificationTapBackground(NotificationResponse notificationResponse) {
    if (notificationResponse.payload == 'Jadwal Final') {
      Get.toNamed(Routes.JADWAL);
    }
  }

  static void showNotificatiOnForeground(RemoteMessage message) {
    NotificationDetails notificationDetails = NotificationDetails(
      android: AndroidNotificationDetails(
        EnvironmentConstant.localNotifChannelId,
        "hr_portal",
        importance: Importance.max,
        priority: Priority.high,
        channelShowBadge: true,
        number: 1,
      ),
      iOS: const DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentBanner: true,
        presentList: true,
        presentSound: true,
        badgeNumber: 1,
      ),
    );
    _notificationsPlugin.show(
        DateTime.now().microsecond,
        message.notification!.title,
        message.notification!.body,
        notificationDetails,
        payload: message.notification!.title);
  }

  // tz.TZDateTime nextInstanceOfTime() {
  //   var location = tz.getLocation('Asia/Singapore');
  //   final tz.TZDateTime now = tz.TZDateTime.now(location);
  //   tz.TZDateTime scheduledDate =
  //       tz.TZDateTime(location, now.year, now.month, now.day, 8, 30);
  //   // if (scheduledDate.isBefore(now)) {
  //   //   scheduledDate = scheduledDate.add(const Duration(days: 1));
  //   // }
  //   return scheduledDate;
  // }

  Future<void> dateNotifications(tz.TZDateTime? theTime, int id) async {
    NotificationDetails notificationDetails = NotificationDetails(
      android: AndroidNotificationDetails(
        EnvironmentConstant.localNotifChannelId,
        "hr_portal",
        importance: Importance.max,
        priority: Priority.max,
      ),
    );
    await _notificationsPlugin.zonedSchedule(
      id,
      'Absensi ${MahasConfig.appName}',
      'Jangan lupa untuk input absensi',
      theTime!,
      notificationDetails,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
      matchDateTimeComponents: DateTimeComponents.time,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
    );
  }

  Future<void> cancelAllNotifications() async {
    await _notificationsPlugin.cancelAll();
  }
}
