import 'dart:convert';
import 'package:flutter/material.dart';
import '../mahas/services/mahas_format.dart';

class ShifthariiniModel {
	int? id;
	String? kode;
	String? nama;
	String? kodewarna;
	TimeOfDay? jammulai;
	TimeOfDay? jamselesai;
	bool? shiftke2;
	TimeOfDay? jammulaike2;
	TimeOfDay? jamselesaike2;
	bool? shiftke3;
	TimeOfDay? jammulaike3;
	TimeOfDay? jamselesaike3;

  ShifthariiniModel();

	static fromJson(String jsonString) {
		final data = json.decode(jsonString);
		return fromDynamic(data);
	}

	static ShifthariiniModel fromDynamic(dynamic dynamicData) {
		final model = ShifthariiniModel();

		model.id = MahasFormat.dynamicToInt(dynamicData['id']);
		model.kode = dynamicData['kode'];
		model.nama = dynamicData['nama'];
		model.kodewarna = dynamicData['kodeWarna'];
		model.jammulai = MahasFormat.stringToTime(dynamicData['jamMulai']);
		model.jamselesai = MahasFormat.stringToTime(dynamicData['jamSelesai']);
		model.shiftke2 = MahasFormat.dynamicToBool(dynamicData['shiftKe2']);
		model.jammulaike2 = MahasFormat.stringToTime(dynamicData['jamMulaiKe2']);
		model.jamselesaike2 = MahasFormat.stringToTime(dynamicData['jamSelesaiKe2']);
		model.shiftke3 = MahasFormat.dynamicToBool(dynamicData['shiftKe3']);
		model.jammulaike3 = MahasFormat.stringToTime(dynamicData['jamMulaiKe3']);
		model.jamselesaike3 = MahasFormat.stringToTime(dynamicData['jamSelesaiKe3']);

		return model;
	}
}
