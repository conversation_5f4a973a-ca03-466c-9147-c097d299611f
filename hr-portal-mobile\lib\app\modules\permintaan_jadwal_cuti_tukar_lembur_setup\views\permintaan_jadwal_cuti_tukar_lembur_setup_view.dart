import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_datetime_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_detail_setup_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_dropdown_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_text_component.dart';
import 'package:hr_portal/app/mahas/components/mahas_themes.dart';
import 'package:hr_portal/app/mahas/components/pages/setup_page_component.dart';
import 'package:hr_portal/app/models/cuti_tukar_lembur.dart';

import '../../../mahas/services/helper.dart';
import '../../../mahas_complement/status_request.dart';
import '../controllers/permintaan_jadwal_cuti_tukar_lembur_setup_controller.dart';

class PermintaanJadwalCutiTukarLemburSetupView
    extends GetView<PermintaanJadwalCutiTukarLemburSetupController> {
  const PermintaanJadwalCutiTukarLemburSetupView({super.key});

  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      title: "Cuti Tukar Lembur",
      controller: controller.formCon,
      childrenPadding: false,
      children: () => [
        controller.isKadiv == true &&
                controller.formCon.isState == SetupPageState.detail
            ? Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    InputTextComponent(
                      label: "Pegawai Request",
                      controller: controller.pegawaiCutiCon,
                      editable: false,
                      marginBottom: 5,
                    ),
                    InkWell(
                      onTap: () async => await Helper.fotoProfilOnTap(
                          controller.idPegawaiRequest.value),
                      child: SizedBox(
                        height: 30,
                        child: Text(
                          "Lihat Foto Profil Pegawai",
                          style: MahasThemes.link,
                          textAlign: TextAlign.left,
                        ),
                      ),
                    ),
                  ],
                ),
              )
            : const SizedBox(),
        InputDetailSetupComponent<CutitukarlemburDetailModel>(
          label: "Tanggal",
          controller: controller.detailSetupCon,
          editable: controller.formCon.editable,
          required: true,
          formBuilder: (e) => Column(
            children: [
              // form detail
              InputDatetimeComponent(
                label: "Cuti Tanggal",
                controller: controller.tanggalCon,
                required: true,
                editable: controller.formCon.editable,
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Column(
            children: [
              InputTextComponent(
                  label: "Alasan",
                  controller: controller.alasanCon,
                  required: true,
                  editable: controller.formCon.editable),
              Obx(
                () => ApprovalStatusContainer(
                  isEditable: controller.formCon.editable,
                  isVisible: controller.isVisible,
                  isBatal: controller.batal,
                  allowED: controller.allowED,
                  isDinamisApprove: controller.isDinamisApprove,
                  alasanTolakController: controller.alasanTolakCon,
                  onTolakPressed: () {
                    controller.approvalOnPress(false);
                  },
                  onTerimaPressed: () {
                    controller.approvalOnPress(true);
                  },
                  batalOnPress: () {
                    alert();
                  },
                  pegawaiKadiv: controller.pegawaiKadiv.value,
                  pegawaiManager: controller.pegawaiManager.value,
                  accKadiv: controller.accKadiv.value,
                  accManager: controller.accManager.value,
                  statusTolak: controller.statusTolak.value,
                  modelApproval: controller.modelApproval,
                  isStop: controller.isStop.value,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Future<dynamic> alert() {
    return Get.dialog(
      AlertDialog(
        content: SizedBox(
          width: 1000,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              InputTextComponent(
                label: "Alasan Ditolak",
                controller: controller.alasanTolakCon,
                required: true,
              ),
            ],
          ),
        ),
        contentPadding:
            const EdgeInsets.only(bottom: 0, top: 20, right: 10, left: 10),
        actionsPadding:
            const EdgeInsets.only(top: 0, bottom: 0, left: 10, right: 10),
        actions: [
          TextButton(
            child: Text(
              "Close",
              style: MahasThemes.muted,
            ),
            onPressed: () {
              Get.back(result: false);
            },
          ),
          TextButton(
            child: const Text(
              "Simpan",
            ),
            onPressed: () {
              if (controller.alasanTolakCon.isValid) {
                controller.approvalOnPress(false);
                Get.back(result: true);
              }
            },
          ),
        ],
      ),
    );
  }

  Future<dynamic> dialogBatal() {
    return Get.dialog(
      AlertDialog(
        content: SizedBox(
          width: 1000,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              InputDropdownComponent(
                label: "Pilih Tanggal",
                controller: controller.tanggalBatalCon,
                required: true,
              ),
              InputDropdownComponent(
                label: "Ganti Menjadi Shift",
                controller: controller.shiftBatalCon,
                required: true,
              ),
            ],
          ),
        ),
        contentPadding:
            const EdgeInsets.only(bottom: 0, top: 20, right: 10, left: 10),
        actionsPadding:
            const EdgeInsets.only(top: 0, bottom: 0, left: 10, right: 10),
        actions: [
          TextButton(
            child: Text(
              "Close",
              style: MahasThemes.muted,
            ),
            onPressed: () {
              Get.back(result: false);
            },
          ),
          TextButton(
            child: const Text(
              "Simpan",
            ),
            onPressed: () {
              controller.batalOnPress();
            },
          ),
        ],
      ),
    );
  }
}
