import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/others/list_component.dart';
import '../../../mahas/mahas_colors.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../mahas_complement/mahas_colors.dart';
import '../../../models/profile_sertifikasi_model.dart';
import '../controllers/profile_jenis_sertifikasi_controller.dart';

class ProfileJenisSertifikasiView
    extends GetView<ProfileJenisSertifikasiController> {
  const ProfileJenisSertifikasiView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('<PERSON><PERSON>'),
        centerTitle: true,
        actions: [
          GetBuilder<ProfileJenisSertifikasiController>(
            builder: (controller) {
              return PopupMenuButton(
                onSelected: controller.popupMenuButtonOnSelected,
                itemBuilder: (context) {
                  List<PopupMenuItem<String>> r = [];
                  r.add(
                    const PopupMenuItem(
                      value: 'Tambah',
                      child: Text('Tambah'),
                    ),
                  );
                  if (MahasConfig.hasHistory) {
                    r.add(
                      const PopupMenuItem(
                        value: 'History',
                        child: Text('History'),
                      ),
                    );
                  }

                  return r;
                },
              );
            },
          ),
        ],
      ),
      body: ListComponent(
        controller: controller.listCon,
        itemBuilder: (SertifikasiModel e) {
          return Card(
            elevation: 3,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(MahasThemes.borderRadius)),
            child: ListTile(
              title: Text("Jenis Sertifikasi : ${e.namasertifikasi ?? "-"}",
                  style: MahasThemes.title),
              subtitle: Text("Nama Sertifikasi : ${e.nama ?? "-"}"),
              leading: Container(
                color: MahasColors.primary,
                width: 8,
              ),
              minLeadingWidth: 0,
              minVerticalPadding: 0,
              horizontalTitleGap: 10,
              trailing: Text(
                MahasFormat.displayDate(e.tanggal),
                style: MahasColor.muted,
              ),
              onTap: () => controller.itemOnTab(e.id!),
              contentPadding: const EdgeInsets.symmetric(
                vertical: 0,
                horizontal: 10,
              ),
            ),
          );
        },
      ),
    );
  }
}
