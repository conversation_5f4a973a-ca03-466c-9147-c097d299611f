import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/others/list_component.dart';
import 'package:hr_portal/app/models/cuti_hamil_model.dart';
import 'package:hr_portal/app/routes/app_pages.dart';

import '../../../mahas/mahas_config.dart';

class PermintaanJadwalCutiHamilController extends GetxController {
  final listCon = ListComponentController<CutihamilModel>(
    urlApi: (index, filter) =>
        '/api/PermintaanJadwal/CutiHamil/List?pageIndex=$index',
    fromDynamic: CutihamilModel.fromDynamic,
    allowSearch: false,
  );

  bool isDinamis = MahasConfig.profileMenu.approvalDinamis;

  void addOnPress() {
    Get.toNamed(Routes.PERMINTAAN_JADWAL_CUTI_HAMIL_SETUP)?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PERMINTAAN_JADWAL_CUTI_HAMIL_SETUP,
      parameters: {
        'id': id.toString(),
      },
    )?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }
}
