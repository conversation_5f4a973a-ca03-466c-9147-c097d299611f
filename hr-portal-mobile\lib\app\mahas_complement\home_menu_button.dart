import 'package:flutter/material.dart';
import 'package:hr_portal/app/mahas/components/mahas_themes.dart';

import '../mahas/mahas_colors.dart';

class HomeMenuButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final GestureTapCallback? onTap;
  final bool isMuted;

  const HomeMenuButton(
      {super.key,
      required this.icon,
      required this.label,
      required this.onTap,
      this.isMuted = false});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: InkWell(
        onTap: isMuted ? null : onTap,
        child: Container(
          decoration: BoxDecoration(
            color: isMuted ? Colors.black.withValues(alpha: .5) : Colors.white,
            borderRadius:
                BorderRadius.all(Radius.circular(MahasThemes.borderRadius)),
            boxShadow: [
              BoxShadow(
                color: MahasColors.primary.withValues(alpha: 0.5),
                blurRadius: 2,
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: MahasColors.primary,
                size: 16,
              ),
              const Padding(padding: EdgeInsets.all(5)),
              Text(
                label,
                style: TextStyle(
                  color: MahasColors.primary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
