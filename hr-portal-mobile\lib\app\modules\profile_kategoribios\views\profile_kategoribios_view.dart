import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../mahas/components/mahas_themes.dart';
import '../controllers/profile_kategoribios_controller.dart';

class ProfileKategoribiosView extends GetView<ProfileKategoribiosController> {
  const ProfileKategoribiosView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Kategori BIOS'),
        centerTitle: true,
      ),
      body: ListTile(
        title: Obx(() => Text(controller.dataCon.value.nama ?? "-",
            style: MahasThemes.title)),
        onTap: () {},
      ),
    );
  }
}
