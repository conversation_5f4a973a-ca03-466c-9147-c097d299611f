import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class CutiUnpaiLeaveModel {
  int? id;
  int? idDivisi;
  DateTime? tanggalinput;
  bool? approvekadiv;
  bool? approvemanager;
  DateTime? daritanggal;
  DateTime? sampaitanggal;
  int? jumlahhari;
  String? alasan;
  String? delegasi;
  String? nohp;
  String? alasantolak;
  int? idPegawaiRequest;
  String? pathfoto;
  String? divisi;
  String? pegawairequest;
  String? pegawaikadiv;
  String? pegawaimanager;
  String? approvemethod;
  int? idUnitbisnis;
  int? idPegawaiKadiv;
  int? idPegawaiManager;
  int? idPegawaiDelegasi;

  CutiUnpaiLeaveModel();

  static CutiUnpaiLeaveModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static CutiUnpaiLeaveModel fromDynamic(dynamic dynamicData) {
    final model = CutiUnpaiLeaveModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idDivisi = MahasFormat.dynamicToInt(dynamicData['id_Divisi']);
    model.tanggalinput =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalInput']);
    model.approvekadiv = MahasFormat.dynamicToBool(dynamicData['approveKadiv']);
    model.approvemanager =
        MahasFormat.dynamicToBool(dynamicData['approveManager']);
    model.daritanggal =
        MahasFormat.dynamicToDateTime(dynamicData['dariTanggal']);
    model.sampaitanggal =
        MahasFormat.dynamicToDateTime(dynamicData['sampaiTanggal']);
    model.jumlahhari = MahasFormat.dynamicToInt(dynamicData['jumlahHari']);
    model.alasan = dynamicData['alasan'];
    model.delegasi = dynamicData['delegasi'];
    model.nohp = dynamicData['noHp'];
    model.alasantolak = dynamicData['alasanTolak'];
    model.idPegawaiRequest =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Request']);
    model.pathfoto = dynamicData['pathFoto'];
    model.divisi = dynamicData['divisi'];
    model.pegawairequest = dynamicData['pegawaiRequest'];
    model.pegawaikadiv = dynamicData['pegawaiKadiv'];
    model.pegawaimanager = dynamicData['pegawaiManager'];
    model.approvemethod = dynamicData['approveMethod'];
    model.idUnitbisnis = MahasFormat.dynamicToInt(dynamicData['id_UnitBisnis']);
    model.idPegawaiKadiv =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Kadiv']);
    model.idPegawaiManager =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Manager']);
    model.idPegawaiDelegasi =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Delegasi']);

    return model;
  }
}

class LookUpPegawaiModel {
  int? id;
  String? nama;
  String? nip;

  LookUpPegawaiModel();
  LookUpPegawaiModel.init(this.id, this.nama);

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static LookUpPegawaiModel fromDynamic(dynamic dynamicData) {
    final model = LookUpPegawaiModel();

    model.id = dynamicData['id'];
    model.nama = dynamicData['nama'];
    model.nip = dynamicData['nip'];

    return model;
  }
}
