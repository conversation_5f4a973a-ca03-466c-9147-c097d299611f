import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/models/absensi_model.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/others/list_component.dart';
import '../../../mahas/models/menu_item_model.dart';

class DataAbsenController extends GetxController {
  late ListComponentController<AbsensiModel> listCon;
  RxString dariTanggal =
      "${DateTime.now().year}-${DateTime.now().month}-${DateTime.now().day}"
          .obs;
  RxString sampaiTanggal =
      "${DateTime.now().year}-${DateTime.now().month}-${DateTime.now().day}"
          .obs;
  final InputDatetimeController dariTanggalCon = InputDatetimeController();
  final InputDatetimeController sampaiTanggalCon = InputDatetimeController();
  final List<MenuItemModel> menus = [];
  RxInt selectedIndex = 0.obs;

  @override
  void onInit() {
    listCon = ListComponentController<AbsensiModel>(
      urlApi: (index, filter) =>
          '/api/Absensi/Periode?dariTanggal=${dariTanggal.value}&sampaiTanggal=${sampaiTanggal.value}&pageIndex=$index',
      fromDynamic: AbsensiModel.fromDynamic,
      allowSearch: false,
    );
    menus.add(MenuItemModel('Masuk', Icons.login, () {}));
    menus.add(MenuItemModel('Keluar', Icons.logout, () {}));
    super.onInit();
  }

  Future<void> filterOnTap() async {
    Helper.dialogFilter(
      dariTanggalCon: dariTanggalCon,
      sampaiTanggalCon: sampaiTanggalCon,
      todayFilterOnTap: () => filter(false),
      periodFilterOnTap: () => filter(
        true,
      ),
    );
  }

  void filter(bool isPeriod) async {
    if (isPeriod) {
      dariTanggal.value = dariTanggalCon.value.toString();
      sampaiTanggal.value = sampaiTanggalCon.value.toString();
      Get.back(result: true);
      await listCon.refresh();
    } else {
      dariTanggal.value =
          "${DateTime.now().year}-${DateTime.now().month}-${DateTime.now().day}";
      sampaiTanggal.value =
          "${DateTime.now().year}-${DateTime.now().month}-${DateTime.now().day}";
      Get.back(result: true);
      await listCon.refresh();
    }
  }
}
