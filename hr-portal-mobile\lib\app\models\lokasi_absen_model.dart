import 'dart:convert';
import 'dart:math';
import '../mahas/services/mahas_format.dart';

class LokasiabsenModel {
  int? id;
  String? nama;
  double? latitude;
  double? longitude;
  double? radius;

  LokasiabsenModel();

  static LokasiabsenModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static fromDynamic(dynamic dynamicData) {
    final model = LokasiabsenModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.nama = dynamicData['nama'];
    model.latitude = MahasFormat.dynamicToDouble(dynamicData['latitude']);
    model.longitude = MahasFormat.dynamicToDouble(dynamicData['longitude']);
    model.radius = MahasFormat.dynamicToDouble(dynamicData['radius']);

    return model;
  }

  double distanceInMeters(double lat1, double lon1, double lat2, double lon2) {
    const R = 6371000; // Earth's radius in meters
    double dLat = _toRad(lat2 - lat1);
    double dLon = _toRad(lon2 - lon1);
    double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(_toRad(lat1)) * cos(_toRad(lat2)) * sin(dLon / 2) * sin(dLon / 2);
    double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    double d = R * c;
    return d;
  }

  double _toRad(double value) {
    return value * pi / 180;
  }

  LokasiabsenModel? isUserInLocation(
      double userLat, double userLon, List<LokasiabsenModel> locations) {
    for (var location in locations) {
      double distance = distanceInMeters(
          userLat, userLon, location.latitude!, location.longitude!);
      if (distance <= location.radius!) {
        return location;
      }
    }
    return null;
  }
}
