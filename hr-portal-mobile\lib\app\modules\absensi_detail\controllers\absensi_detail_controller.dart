import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_checkbox_component.dart';
import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../models/absensi_model.dart';
import '../../../models/lokasi_absen_model.dart';

class AbsensiDetailController extends GetxController {
  late SetupPageController formCon;

  final jamAbsenCon = InputDatetimeController();
  final namaLokasiCon = InputDropdownController();
  final atasNamaCon = InputTextController();
  final masukCon = InputTextController();
  final prioritasCon = InputCheckboxController();
  final fileCon = InputFileController(
    urlImage: true,
    tipe: InputFileType.camera,
  );
  RxString imageUrl = "".obs;

  @override
  void onInit() {
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/Absensi/Foto/$id',
      allowDelete: false,
      allowEdit: false,
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        return true;
      },
      apiToView: (json) {
        AbsensifotoModel model = AbsensifotoModel.fromJson(json);

        //mahasComponent
        jamAbsenCon.value = TimeOfDay.fromDateTime(model.tanggaljam!);
        namaLokasiCon.value = model.idLokasiabsensi;
        atasNamaCon.value = model.namapegawai;
        masukCon.value = model.status;
        fileCon.value = model.pathfoto!;
      },
      onInit: () async {
        await getLokasi();
      },
    );

    super.onInit();
  }

  Future<void> getLokasi() async {
    if (EasyLoading.isShow) {
      EasyLoading.dismiss();
    }
    EasyLoading.show();
    var r = await HttpApi.get("/api/Absensi/DaftarLokasi");
    if (r.success) {
      RxList<LokasiabsenModel> listModel = RxList<LokasiabsenModel>();
      final datas = json.decode(r.body);
      for (var e in (datas ?? [])) {
        listModel.add(LokasiabsenModel.fromDynamic(e));
      }
      if (namaLokasiCon.items.isNotEmpty) {
        namaLokasiCon.items.clear();
      }
      if (listModel.isNotEmpty && namaLokasiCon.items.isEmpty) {
        namaLokasiCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      }
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }
}
