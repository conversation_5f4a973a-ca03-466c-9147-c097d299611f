import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_checkbox_component.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/models/api_list_resut_model.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../models/profile_dokumen_model.dart';
import 'package:path/path.dart' as p;

class ProfileDokumenSetupController extends GetxController {
  late SetupPageController formCon;
  final jenisDokumenCon = InputDropdownController();
  final nomorDokumenCon = InputTextController();
  final statusPajakCon = InputTextController();
  final sisaExpiredCon = InputTextController(
    type: InputTextType.number,
  );
  final tglBerlakuCon = InputDatetimeController();
  final tglBerakhirCon = InputDatetimeController();
  final aktifCon = InputCheckboxController();
  final fileCon = InputFileController(
    urlImage: true,
    type: FileType.custom,
    tipe: InputFileType.pdf,
    extension: ["pdf", "jpg", "jpeg", "png"],
  );

  @override
  void onInit() {
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PegawaiDokumen/$id',
      urlApiPost: () => '/api/PegawaiDokumen',
      urlApiPut: (id) => '/api/PegawaiDokumen/$id',
      urlApiDelete: (id) => '/api/PegawaiDokumen/$id',
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      bodyApi: (id) => {
        "id_Dokumen": jenisDokumenCon.value,
        "nomor": nomorDokumenCon.value,
        "tanggalBerlaku": MahasFormat.dateToString(DateTime.now()),
        "tanggalBerakhir": MahasFormat.dateToString(DateTime.now()),
        "filePhoto": fileCon.value,
        "fileName": fileCon.name,
        "statusPajak": statusPajakCon.value,
        "aktif": true
      },
      apiToView: (json) {
        DokumenModel model = DokumenModel.fromJson(json);
        jenisDokumenCon.value = model.idDokumen;
        nomorDokumenCon.value = model.nomor;
        statusPajakCon.value = model.statuspajak;
        sisaExpiredCon.value = model.sisaHariExpired;
        tglBerlakuCon.value = model.tanggalberlaku;
        tglBerakhirCon.value = model.tanggalberakhir;
        fileCon.value = model.pathfile;
        aktifCon.checked = model.aktif!;

        if (model.pathfile != null) {
          String extension = p.extension(model.pathfile!).toLowerCase();
          if (extension == ".pdf") {
            updateState(InputFileType.pdf);
          } else {
            updateState(InputFileType.image);
          }
        }
      },
      onBeforeSubmit: () {
        if (!jenisDokumenCon.isValid) return false;
        if (!nomorDokumenCon.isValid) return false;
        return true;
      },
      onInit: () async {
        await getDokumen();
      },
    );
    super.onInit();
  }

  void updateState(InputFileType tipe) {
    fileCon.tipe = tipe;
  }

  Future<void> getDokumen() async {
    if (EasyLoading.isShow) return;
    EasyLoading.show();
    var r = await HttpApi.get("/api/JenisDokumen");
    if (r.success) {
      RxList<JenisDokumenModel> listModel = RxList<JenisDokumenModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(JenisDokumenModel.fromDynamic(e));
      }
      if (r.body != null && jenisDokumenCon.items.isEmpty) {
        jenisDokumenCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      }
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }
}
