import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class GajiModel {
  int? id;
  int? idPegawai;
  DateTime? tanggal;
  int? bulan;
  int? tahun;
  DateTime? mulaikerja;
  String? jabatan;
  int? harikerja;
  int? absensiizin;
  int? absensicuti;
  double? jumlah;
  String? catatan;
  String? pegawai;
  List<GajiDetailModel>? detail;

  GajiModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static GajiModel fromDynamic(dynamic dynamicData) {
    final model = GajiModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.tanggal = MahasFormat.dynamicToDateTime(dynamicData['tanggal']);
    model.bulan = MahasFormat.dynamicToInt(dynamicData['bulan']);
    model.tahun = MahasFormat.dynamicToInt(dynamicData['tahun']);
    model.mulaikerja = MahasFormat.dynamicToDateTime(dynamicData['mulaiKerja']);
    model.jabatan = dynamicData['jabatan'];
    model.harikerja = MahasFormat.dynamicToInt(dynamicData['hariKerja']);
    model.absensiizin = MahasFormat.dynamicToInt(dynamicData['absensiIzin']);
    model.absensicuti = MahasFormat.dynamicToInt(dynamicData['absensiCuti']);
    model.jumlah = MahasFormat.dynamicToDouble(dynamicData['jumlah']);
    model.catatan = dynamicData['catatan'];
    model.pegawai = dynamicData['pegawai'];
    if (dynamicData['detail'] != null) {
      final detailT = dynamicData['detail'] as List;
      model.detail = [];
      for (var i = 0; i < detailT.length; i++) {
        model.detail!.add(GajiDetailModel.fromDynamic(detailT[i]));
      }
    }

    return model;
  }
}

class GajiDetailModel {
  int? idJenisdetailslipgaji;
  double? nilai;
  bool? pendapatan;
  String? jenisdetailslipgaji;

  GajiDetailModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static GajiDetailModel fromDynamic(dynamic dynamicData) {
    final model = GajiDetailModel();

    model.idJenisdetailslipgaji =
        MahasFormat.dynamicToInt(dynamicData['id_JenisDetailSlipGaji']);
    model.nilai = MahasFormat.dynamicToDouble(dynamicData['nilai']);
    model.pendapatan = MahasFormat.dynamicToBool(dynamicData['pendapatan']);
    model.jenisdetailslipgaji = dynamicData['jenisDetailSlipGaji'];

    return model;
  }
}

class ReportModel {
  String? urlreport;
  String? urlreportstatis;

  ReportModel();

  static ReportModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static ReportModel fromDynamic(dynamic dynamicData) {
    final model = ReportModel();

    model.urlreport = dynamicData['urlReport'];
    model.urlreportstatis = dynamicData['urlReportStatis'];

    return model;
  }
}

class SlipGajiStatisModel {
  int? id;
  int? idPegawai;
  DateTime? tanggal;
  int? bulan;
  int? tahun;
  String? keterangan;
  double? penghasilanbruto;
  double? totalpotongan;
  double? penghasilanbersih;

  SlipGajiStatisModel();

  static SlipGajiStatisModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static SlipGajiStatisModel fromDynamic(dynamic dynamicData) {
    final model = SlipGajiStatisModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.tanggal = MahasFormat.dynamicToDateTime(dynamicData['tanggal']);
    model.bulan = MahasFormat.dynamicToInt(dynamicData['bulan']);
    model.tahun = MahasFormat.dynamicToInt(dynamicData['tahun']);
    model.keterangan = dynamicData['keterangan'];
    model.penghasilanbruto =
        MahasFormat.dynamicToDouble(dynamicData['penghasilanBruto']);
    model.totalpotongan =
        MahasFormat.dynamicToDouble(dynamicData['totalPotongan']);
    model.penghasilanbersih =
        MahasFormat.dynamicToDouble(dynamicData['penghasilanBersih']);

    return model;
  }
}
