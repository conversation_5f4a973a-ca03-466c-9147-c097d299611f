import 'dart:convert';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_datetime_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_detail_setup_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_text_component.dart';
import 'package:hr_portal/app/mahas/components/pages/setup_page_component.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:hr_portal/app/mahas/models/api_result_model.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/mahas/services/http_api.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';
import 'package:hr_portal/app/models/cuti_tahunan_model.dart';
import 'package:intl/intl.dart';

import '../../../mahas/components/inputs/input_detail_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../models/pegawai_approval.dart';

class PermintaanJadwalCutiTahunanSetupController extends GetxController {
  late SetupPageController formCon;
  final alasanCon = InputTextController(type: InputTextType.paragraf);
  final tanggalCon = InputDatetimeController();
  late InputDetailSetupControler<CutitahunanDetailModel> detailSetupCon;
  late InputDetailControler<CutitahunanDetailModel> detailApproveCon;
  final InputTextController berapaHariCon =
      InputTextController(type: InputTextType.number);

  final tanggalMulaiCon = InputDatetimeController();
  final tanggalSelesaiCon = InputDatetimeController();
  final jumlahCutiCon = InputTextController(type: InputTextType.number);
  final sisaCutiCon = InputTextController(type: InputTextType.number);
  final tanggalSisaSelesaiCon = InputDatetimeController();
  final totalCutiCon = InputTextController(type: InputTextType.number);

  final pegawaiCutiCon = InputTextController();
  final alasanTolakCon = InputTextController();
  final alasanBatalCon = InputTextController();
  final String nama = MahasConfig.profile!.nama!;
  late String approve;
  late bool allowED;
  Rxn<bool> accKadiv = Rxn<bool>();
  Rxn<bool> accManager = Rxn<bool>();
  RxBool isVisible = false.obs;
  late RxString pegawaiKadiv = ''.obs;
  late RxString pegawaiManager = ''.obs;
  late String kadiv;
  late bool isKadiv;
  final isDinamisApprove = MahasConfig.profileMenu.approvalDinamis;
  RxBool isStop = false.obs;

  RxBool batal = false.obs;
  final shiftBatalCon = InputDropdownController();
  final tanggalBatalCon = InputDropdownController();
  List<CutitahunanDetailModel> modelDetail = [];
  RxList<PegawaiApprovalModel> modelApproval = <PegawaiApprovalModel>[].obs;
  int? id;
  RxString statusTolak = ''.obs;
  RxInt idPegawaiRequest = 0.obs;
  RxString tglPengajuan = ''.obs;
  RxInt jumlahHari = 0.obs;
  int? idDivisi;

  @override
  void onInit() {
    detailApprove();
    cekApproval();
    detailSetUp();
    cekKadiv();
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PermintaanJadwal/CutiTahunan/$id',
      urlApiPost: () => '/api/PermintaanJadwal/CutiTahunan',
      urlApiPut: (id) => '/api/PermintaanJadwal/CutiTahunan/$id',
      urlApiDelete: (id) => '/api/PermintaanJadwal/CutiTahunan/$id',
      allowDelete: allowED,
      allowEdit: allowED,
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "alasan": alasanCon.value,
        "jumlahHari": berapaHariCon.value,
        "detail": detailSetupCon.values
            .map((e) => {
                  'tanggal': MahasFormat.dateToString(e.value.tanggal),
                })
            .toList()
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (MahasConfig.dinamisForm.cutiTahunan?.quotaVisible == true) {
          if (jumlahCutiCon.value == 0) {
            Helper.dialogWarning("Anda tidak memiliki cuti tahunan");
            return false;
          }
          if (detailSetupCon.values.length > 6) {
            Helper.dialogWarning("Maksimal 6 hari cuti dalam 1x pengajuan");
            return false;
          }
        }
        if (!detailSetupCon.isValid) return false;
        if (MahasConfig.dinamisForm.cutiTahunan?.quotaVisible == true) {
          if (detailSetupCon.values
              .map((e) => e.value.tanggal?.isAfter(tanggalSelesaiCon.value))
              .contains(true)) {
            Helper.dialogWarning("Cuti melewati akhir hak cuti");
            return false;
          }
        }
        if (MahasConfig.dinamisForm.cutiTahunan?.pengajuanHari != 0) {
          var minPengajuanHari =
              MahasConfig.dinamisForm.cutiTahunan!.pengajuanHari;
          DateTime hMinPengajuan =
              DateTime.now().add(Duration(days: minPengajuanHari! - 1));
          if (detailSetupCon.values.isNotEmpty &&
              detailSetupCon.values[0].value.tanggal!.isBefore(hMinPengajuan)) {
            Helper.dialogWarning(
                "Minimal pengajuan $minPengajuanHari hari sebelum cuti");
            return false;
          }
        }
        berapaHariCon.value = detailSetupCon.values.length.toString();
        if (!alasanCon.isValid) return false;
        return true;
      },
      successMessage: Helper.succesPengajuanMessage(),
      apiToView: (json) {
        CutitahunanModel model = CutitahunanModel.fromJson(json);
        id = model.id;
        idDivisi = model.idDivisi;
        alasanCon.value = model.alasan;
        berapaHariCon.value = model.jumlahHari.toString();
        jumlahHari.value = model.jumlahHari ?? 0;
        detailSetupCon.clear();
        modelDetail.clear();
        for (var i = 0; i < model.detail!.length; i++) {
          detailSetupCon.addValue(CutitahunanDetailModel.init(
              model.detail![i].id, model.detail![i].tanggal));
          modelDetail.add(model.detail![i]);
        }
        tanggalBatalCon.items = modelDetail
            .map<DropdownItem>((e) =>
                DropdownItem.init(MahasFormat.displayDate(e.tanggal), e.id))
            .toList();
        detailApproveCon.clear();
        for (var i = 0; i < modelDetail.length; i++) {
          detailApproveCon.addValue(CutitahunanDetailModel.init(
              model.detail![i].id, model.detail![i].tanggal));
        }

        var format = DateFormat("dd MMMM yyyy HH:mm");
        tglPengajuan.value = format.format(model.tanggalinput!);

        accManager.value = model.approvemanager;
        accKadiv.value = model.approvekadiv;
        pegawaiKadiv.value = model.pegawaikadiv!;
        pegawaiManager.value = model.pegawaimanager!;

        pegawaiCutiCon.value = model.pegawairequest;
        statusTolak.value = model.alasantolak ?? '';
        idPegawaiRequest.value = model.idPegawaiRequest!;

        // approval
        if (isDinamisApprove == false && allowED == false) {
          if ((model.approvekadiv == null &&
                  model.idPegawaiKadiv == MahasConfig.profile!.id) ||
              (model.approvekadiv == true &&
                  model.approvemanager == null &&
                  model.idPegawaiManager == MahasConfig.profile!.id)) {
            isVisible.value = true;
          } else if (model.approvekadiv == true &&
              model.approvemanager == true &&
              model.idPegawaiManager == MahasConfig.profile!.id) {
            if (MahasConfig.dinamisForm.approval?.bisaBatal == true) {
              batal.value = true;
              getShiftBatal();
            }
          }
        }

        if (isDinamisApprove == true && allowED == false) {
          getDataApproval();
        }
      },
      onInit: () async {
        if (MahasConfig.dinamisForm.cutiTahunan?.quotaVisible == true &&
            allowED == true) {
          await getQuotaCutiTahunan();
        }
      },
    );
    detailSetupCon.onChanged = () {
      berapaHariCon.value = detailSetupCon.values.length;
      jumlahHari.value = detailSetupCon.values.length;
    };
    detailSetupCon.onBeforeSubmit = () {
      if (!lookUpValidation()) return false;
      return true;
    };

    super.onInit();
  }

  void detailApprove() {
    detailApproveCon = InputDetailControler<CutitahunanDetailModel>(
      itemKey: (e) => e.id,
      fromDynamic: (e) => CutitahunanDetailModel.fromDynamic(e),
      itemText: (e) => MahasFormat.displayDate(e.tanggal),
      urlApi: (index, filter) => '/api/PermintaanJadwal/CutiTahunan/$id',
    );
  }

  Future<void> getQuotaCutiTahunan() async {
    var url = '/api/PermintaanJadwal/CutiTahunan/QuotaCutiTahunan';
    var r = await HttpApi.get(url);
    if (r.success) {
      var model = QuotaCutiTahunanModel.fromJson(r.body);

      tanggalMulaiCon.value = model.tanggalberlaku;
      tanggalSelesaiCon.value = DateTime(model.tanggalselesai!.year,
          model.tanggalberlaku!.month + 6, model.tanggalberlaku!.day);
      jumlahCutiCon.value = model.jumlahcuti.toString();
      sisaCutiCon.value = model.sisacuti.toString();
      tanggalSisaSelesaiCon.value = model.tanggalsisaselesai;
      totalCutiCon.value = model.totalcuti.toString();
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal mendapatkan data, silahkan cek koneksi internet");
    } else {
      jumlahCutiCon.value = 0;
      Helper.dialogWarning(r.message);
    }
  }

  void detailSetUp() {
    detailSetupCon = InputDetailSetupControler<CutitahunanDetailModel>(
      itemKey: (e) => e.id,
      fromDynamic: (e) => CutitahunanDetailModel.fromDynamic(e),
      itemText: (e) => MahasFormat.dateToString(e.tanggal) ?? "",
      onOpenForm: (id, e) {
        tanggalCon.value = e?.tanggal;
      },
      onFormInsert: (id) {
        if (!tanggalCon.isValid) return null;
        var r = CutitahunanDetailModel();
        r.id = id;
        r.tanggal = tanggalCon.value;
        return r;
      },
    );
  }

  void cekApproval() {
    approve = Get.parameters['approval'].toString();
    if (approve == "approval") {
      allowED = false;
    } else {
      allowED = true;
    }
  }

  void cekKadiv() {
    kadiv = Get.parameters['isKadiv'].toString();
    if (kadiv == "false") {
      isKadiv = false;
    } else {
      isKadiv = true;
    }
  }

  void getShiftBatal() async {
    final url = '/api/PermintaanJadwal/CutiTahunan/Shift?Id_Divisi=$idDivisi';
    final r = await HttpApi.get(url);
    if (r.success) {
      List<LookupCutiTahunanShiftModel> model =
          lookupCutiTahunanShiftModelFromJson(r.body);
      if (r.body != null) {
        if (shiftBatalCon.items.isEmpty) {
          shiftBatalCon.items = model
              .map<DropdownItem>((e) => DropdownItem.init(e.nama, e.id))
              .toList();
        }
      } else {
        Helper.dialogWarning(r.message);
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan aksi, silahkan cek koneksi internet");
    } else {
      Helper.dialogWarning(r.message);
    }
  }

  batalOnPress() async {
    if (!tanggalBatalCon.isValid) return false;
    if (!shiftBatalCon.isValid) return false;
    if (EasyLoading.isShow) return;
    EasyLoading.show();
    var url = '/api/PermintaanJadwal/CutiTahunan/Batal/Manager/$id';
    var r = await HttpApi.put(
      url,
      body: {
        "alasanBatal": alasanBatalCon.value,
      },
    );

    if (r.success) {
      final url = '/api/PermintaanJadwal/CutiTahunan/$id';
      ApiResultModel r;
      r = await HttpApi.get(url);
      if (r.success) {
        CutitahunanModel model = CutitahunanModel.fromJson(r.body);
        accKadiv.value = model.approvekadiv;
        accManager.value = model.approvemanager;
        statusTolak.value = model.alasantolak ?? '';
        batal.value = false;
        update();
        Get.back(result: true);
        Helper.dialogSuccess("Berhasil Batal!");
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan aksi, silahkan cek koneksi internet");
    } else if (!r.success) {
      Helper.dialogWarning(
        "Gagal melakukan aksi",
        resMassage: r.message,
        noInternetMassage:
            "Gagal melakukan aksi, silahkan cek koneksi internet",
      );
    }
    EasyLoading.dismiss();
  }

  Future<void> approvalOnPress(bool approve) async {
    String urlKadiv = '/api/PermintaanJadwal/CutiTahunan/Approve/Kadiv/';
    String urlManager = '/api/PermintaanJadwal/CutiTahunan/Approve/Manager/';
    String urlApprove = '/api/PermintaanJadwal/CutiTahunan/Approve/';
    var action = await Helper.approvalAction(
      id.toString(),
      approve,
      alasanTolakCon.value,
      accKadiv.value,
      urlKadiv,
      urlManager,
      isDinamis: isDinamisApprove,
      urlApprove: urlApprove,
    );
    if (action.success) {
      final url = '/api/PermintaanJadwal/CutiTahunan/$id';
      ApiResultModel r;
      r = await HttpApi.get(url);
      if (r.success) {
        CutitahunanModel model = CutitahunanModel.fromJson(r.body);
        accKadiv.value = model.approvekadiv;
        accManager.value = model.approvemanager;
        statusTolak.value = model.alasantolak ?? '';
        if (isDinamisApprove) {
          getDataApproval();
        }
        isVisible.value = false;
        update();
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan approve, silahkan cek koneksi internet");
    } else if (!action.success) {
      Helper.dialogWarning(
        "Gagal melakukan approval",
        resMassage: action.message,
        noInternetMassage:
            "Gagal melakukan approval, silahkan cek koneksi internet",
      );
    }
  }

  Future<void> getDataApproval() async {
    EasyLoading.show();
    final url = '/api/PermintaanJadwal/CutiTahunan/Approval/$id';
    final r = await HttpApi.get(url);
    modelApproval.clear();
    if (r.success) {
      if (r.body != null) {
        var data = json.decode(r.body);
        for (var e in data['datas']) {
          modelApproval.add(PegawaiApprovalModel.fromDynamic(e));
        }
        bool canApprove = modelApproval.any((item) =>
            item.idPegawaiapprove == MahasConfig.profile?.id &&
            item.approve == null);
        if (canApprove) {
          isVisible.value = true;
        }
        bool notApprove = modelApproval.any((item) => item.approve == false);
        if (notApprove) {
          isVisible.value = false;
        }
        isStop.value = modelApproval.any((item) => item.approve == false);
      } else {
        Helper.dialogWarning(r.message);
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan aksi, silahkan cek koneksi internet");
    } else if (r.statusCode == 404) {
      Helper.dialogWarning("Tidak Ada Data Pegawai Approval");
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }

  bool lookUpValidation() {
    if (detailSetupCon.values.isNotEmpty) {
      var indexSebelumnya = detailSetupCon.values.length - 1;
      if (detailSetupCon.values[indexSebelumnya].value.tanggal
              ?.isAfter(tanggalCon.value) ==
          true) {
        Helper.dialogWarning(
            "Tanggal yang dipilih harus lebih besar dari tanggal sebelumnya");
        return false;
      }

      if (detailSetupCon.values
          .map((e) => e.value.tanggal)
          .contains(tanggalCon.value)) {
        Helper.dialogWarning("Tidak boleh ada tanggal yang sama");
        return false;
      }
    }
    return true;
  }
}
