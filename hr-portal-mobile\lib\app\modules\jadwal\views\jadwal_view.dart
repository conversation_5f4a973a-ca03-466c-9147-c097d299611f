import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/mahas_themes.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';
import 'package:table_calendar/table_calendar.dart';
import '../../../mahas/mahas_colors.dart';
import '../controllers/jadwal_controller.dart';

class JadwalView extends GetView<JadwalController> {
  const JadwalView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('J<PERSON><PERSON> Kerja'),
        centerTitle: true,
      ),
      body: Obx(
        () => ListView(
          children: [
            TableCalendar(
              firstDay: DateTime(controller.focusDate.value.year - 10, 1, 1),
              lastDay: DateTime(controller.focusDate.value.year + 10, 1, 1),
              focusedDay: controller.focusDate.value,
              headerStyle: HeaderStyle(
                titleTextStyle: TextStyle(
                  color: MahasColors.primary,
                  fontWeight: FontWeight.bold,
                  fontSize: 18.0,
                ),
                titleCentered: true,
                formatButtonVisible: false,
              ),
              calendarStyle: CalendarStyle(
                selectedDecoration: BoxDecoration(
                  color: MahasColors.primary,
                  shape: BoxShape.circle,
                ),
                todayDecoration: BoxDecoration(
                  color: MahasColors.primary.withValues(alpha: 0.3),
                  shape: BoxShape.circle,
                ),
              ),
              startingDayOfWeek: StartingDayOfWeek.monday,
              onDaySelected: controller.onDaySelected,
              onPageChanged: controller.onPageChanged,
              selectedDayPredicate: controller.selectedDayPredicate,
              //langsung tampilkan data hari ini
              currentDay: controller.focusDate.value,
            ),
            const Divider(height: 0),
            ListView.separated(
              shrinkWrap: true,
              primary: false,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: controller.listData.length,
              itemBuilder: (context, i) => ListTile(
                title: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(controller.listData[i].pegawai!),
                    Row(
                      children: [
                        Text(
                          controller.listData[i].shift!,
                          style: MahasThemes.muted,
                        ),
                        Visibility(
                          visible: controller.listData[i].lembur != null,
                          child: Text(
                            " - Lembur",
                            style: MahasThemes.muted,
                          ),
                        ),
                      ],
                    )
                  ],
                ),
                onTap: () => controller.itemOnTab(
                  [
                    ListTile(
                      title: Text(controller.listData[i].shift!),
                    ),
                    ...(controller.listData[i].jammulai == null
                        ? []
                        : [
                            const Divider(height: 0),
                            ListTile(
                              title: Text(
                                "${MahasFormat.displayTime(controller.listData[i].jammulai)} - ${MahasFormat.displayTime(controller.listData[i].jamselesai)}",
                                style: MahasThemes.muted,
                              ),
                            ),
                          ]),
                    ...(controller.listData[i].jammulaike2 == null
                        ? []
                        : [
                            const Divider(height: 0),
                            ListTile(
                              title: Text(
                                "${MahasFormat.displayTime(controller.listData[i].jammulaike2)} - ${MahasFormat.displayTime(controller.listData[i].jamselesaike2)}",
                                style: MahasThemes.muted,
                              ),
                            ),
                          ]),
                    ...(controller.listData[i].jammulaike3 == null
                        ? []
                        : [
                            const Divider(height: 0),
                            ListTile(
                              title: Text(
                                "${MahasFormat.displayTime(controller.listData[i].jammulaike3)} - ${MahasFormat.displayTime(controller.listData[i].jamselesaike3)}",
                                style: MahasThemes.muted,
                              ),
                            ),
                          ]),
                    ...(controller.listData[i].lembur == null
                        ? []
                        : [
                            const Divider(height: 0),
                            ListTile(
                              title: Text(
                                "${MahasFormat.displayTime(TimeOfDay.fromDateTime(controller.listData[i].lembur!.tanggaljam!))} - ${MahasFormat.displayTime(TimeOfDay.fromDateTime(controller.listData[i].lembur!.tanggaljamSelesai!))}",
                                style: MahasThemes.muted,
                              ),
                            ),
                          ]),
                  ],
                ),
              ),
              separatorBuilder: (BuildContext context, int index) {
                return const Divider(height: 0);
              },
            )
          ],
        ),
      ),
    );
  }
}
