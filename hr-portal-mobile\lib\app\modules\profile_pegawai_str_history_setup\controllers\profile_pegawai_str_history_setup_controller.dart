import 'dart:convert';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/models/api_list_resut_model.dart';
import '../../../mahas/models/api_result_model.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../models/profesi_model.dart';
import '../../../models/profile_pegawai_str_model.dart';
import '../../../routes/app_pages.dart';

class ProfilePegawaiStrHistorySetupController extends GetxController {
  late SetupPageController formCon;
  late bool reqDelete;
  late bool showStatus;
  late bool withActionBack;

  final profesiCon = InputDropdownController();
  final noStrCon = InputTextController();
  final tglStrCon = InputDatetimeController();
  final tglBerakhirStrCon = InputDatetimeController();
  final noSipCon = InputTextController();
  final tglSipCon = InputDatetimeController();
  final tglBerakhirSipCon = InputDatetimeController();
  final alasanTolakCon = InputTextController();

  RxBool approvalButtonsVisible = false.obs;
  RxBool approvalFromNotif = false.obs;
  RxBool allowED = false.obs;
  late dynamic statusApprovalHRD = null.obs;
  RxString status = "".obs;
  RxString namaHRD = "".obs;
  RxString namaPegawaiRequest = "".obs;
  RxString alasanTolakHRD = "".obs;
  Rx<DateTime> tglPermintaan = DateTime.now().obs;
  RxInt idPegawaiStr = 0.obs;

  @override
  void onInit() {
    //init
    String notifParam = Get.parameters['approval'].toString();
    approvalFromNotif.value = notifParam == "true" ? true : false;
    String showStatusParam = Get.parameters['showStatus'].toString();
    showStatus = showStatusParam == "true" ? true : false;
    String withActionBackParam = Get.parameters['withActionBack'].toString();
    withActionBack = withActionBackParam == "true" ? true : false;

    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PegawaiStr/History/$id',
      urlApiPut: (id) => '/api/PegawaiStr/History/$id',
      urlApiDelete: (id) => '/api/PegawaiStr/History/$id',
      allowDelete: allowED.value,
      allowEdit: allowED.value,
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "id_JenisProfesi": profesiCon.value,
        "id_PegawaiStr": idPegawaiStr.value,
        "nomorSTR": noStrCon.value,
        "tanggalSTR": MahasFormat.dateToString(tglStrCon.value),
        "tanggalBerakhirSTR": MahasFormat.dateToString(tglBerakhirStrCon.value),
        "nomorSIP": noSipCon.value,
        "tanggalSIP": MahasFormat.dateToString(tglSipCon.value),
        "tanggalBerakhirSIP": MahasFormat.dateToString(tglBerakhirSipCon.value),
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      apiToView: (json) {
        PegawaistrModel model = PegawaistrModel.fromJson(json);

        //mahascomponent
        profesiCon.value = model.idJenisprofesi;
        noStrCon.value = model.nomorstr;
        tglStrCon.value = model.tanggalstr;
        tglBerakhirStrCon.value = model.tanggalberakhirstr;
        noSipCon.value = model.nomorsip;
        tglSipCon.value = model.tanggalsip;
        tglBerakhirSipCon.value = model.tanggalberakhirsip;

        //non mahas
        idPegawaiStr.value = model.id ?? 0;
        namaHRD.value = model.pegawaiapprovalnama ?? "";
        alasanTolakHRD.value = model.alasanTolak ?? "";
        namaPegawaiRequest.value = model.pegawainama ?? "";
        statusApprovalHRD = model.approval;
        tglPermintaan.value = model.tglpermintaan ?? DateTime.now();
        reqDelete = model.status == "DELETE" ? true : false;
        status.value = model.status!;

        //cek approval for allowedit or delete
        cekApproval();

        //approval
        if (approvalFromNotif.value && statusApprovalHRD == null) {
          approvalButtonsVisible.value = true;
        }
      },
      onBeforeSubmit: () {
        if (!profesiCon.isValid) return false;
        if (!noStrCon.isValid) return false;
        if (!tglStrCon.isValid) return false;
        if (!tglBerakhirStrCon.isValid) return false;
        if (!noSipCon.isValid) return false;
        if (!tglSipCon.isValid) return false;
        if (!tglBerakhirSipCon.isValid) return false;
        return true;
      },
      onInit: () async {
        await getProfesi();
      },
    );

    if (MahasConfig.hasHistory) {
      formCon.onSuccessSubmit = (r) {
        var idHistory = json.decode(r.body)['id'];
        Get.toNamed(
          Routes.PROFILE_PEGAWAI_STR_HISTORY_SETUP,
          parameters: {
            'id': idHistory.toString(),
            'showStatus': true.toString(),
            'withActionBack': true.toString(),
          },
        );
      };
      formCon.deleteOnTap = (id) {
        Get.toNamed(
          Routes.PROFILE_PEGAWAI_STR_HISTORY_SETUP,
          parameters: {
            'id': id.toString(),
            'showStatus': true.toString(),
            'withActionBack': true.toString(),
          },
        );
      };
    }

    super.onInit();
  }

  Future<void> getProfesi() async {
    EasyLoading.show();
    var r = await HttpApi.get("/api/JenisProfesi");
    if (r.success) {
      RxList<ProfesiModel> listModel = RxList<ProfesiModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(ProfesiModel.fromDynamic(e));
      }
      if (profesiCon.items.isNotEmpty) {
        profesiCon.items.clear();
      }
      if (listModel.isNotEmpty && profesiCon.items.isEmpty) {
        profesiCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      }
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }

  //backAction
  Future<bool> _back() async {
    Get.until((route) => route.settings.name == Routes.PROFILE_PEGAWAI_STR);
    Get.toNamed(Routes.PROFILE_PEGAWAI_STR_HISTORY);
    return true;
  }

  //cek approval parameters come from notifikasi
  void cekApproval() {
    if (approvalFromNotif.value || statusApprovalHRD != null) {
      allowED.value = false;
    } else {
      allowED.value = true;
    }
    if (withActionBack) {
      formCon.backAction = () {
        return _back();
      };
      formCon.deleteOnTap = (id) {
        return _back();
      };
    }
    formCon.setState(() {
      reqDelete ? formCon.allowEdit = false : formCon.allowEdit = allowED.value;
      formCon.allowDelete = allowED.value;
      if (reqDelete) {
        formCon.deleteCaption = "Batal Hapus";
        formCon.deleteDescription =
            "Yakin akan membatalkan permintaan hapus data ini?";
      }
    });
  }

  Future<void> approvalAction(bool approve) async {
    if (EasyLoading.isShow) {
      EasyLoading.dismiss();
    }
    EasyLoading.show();
    String id = Get.parameters['id'].toString();
    final body = {
      'approve': approve,
      'alasanTolak': alasanTolakCon.value,
    };
    ApiResultModel? r;
    String url = '/api/PegawaiJenisKontak/History/Approve/$id';
    r = await HttpApi.put(
      url,
      body: body,
    );
    if (r.success) {
      r = await HttpApi.get(
        '/api/PegawaiJenisKontak/History/$id',
      );
      if (r.success) {
        PegawaistrModel model = PegawaistrModel.fromJson(r.body);
        statusApprovalHRD = model.approval;
        alasanTolakHRD.value = model.alasanTolak ?? "";
        approvalButtonsVisible.value = false;
        update();
      }
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }
}
