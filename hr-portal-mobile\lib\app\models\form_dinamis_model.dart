import 'dart:convert';

import '../mahas/services/mahas_format.dart';

class DinamisFormModel {
  AbsensiModel? absensi;
  DataPribadiModel? datapribadi;
  KontakModel? kontak;
  CutiTahunanModel? cutiTahunan;
  CutiHamilModel? cutiHamil;
  IzinModel? izin;
  CutiSakitModel? cutiSakit;
  ApprovalModel? approval;
  NotifikasiModel? notifikasi;
  LemburModel? lembur;

  DinamisFormModel();

  static DinamisFormModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static DinamisFormModel fromDynamic(dynamic dynamicData) {
    final model = DinamisFormModel();

    model.absensi = AbsensiModel.fromDynamic(dynamicData['absensi']);
    model.datapribadi =
        DataPribadiModel.fromDynamic(dynamicData['dataPribadi']);
    model.kontak = KontakModel.fromDynamic(dynamicData['kontak']);
    model.cutiTahunan =
        CutiTahunanModel.fromDynamic(dynamicData['cutiTahunan']);
    model.cutiHamil = CutiHamilModel.fromDynamic(dynamicData['cutiHamil']);
    model.izin = IzinModel.fromDynamic(dynamicData['izin']);
    model.cutiSakit = CutiSakitModel.fromDynamic(dynamicData['cutiSakit']);
    model.approval = ApprovalModel.fromDynamic(dynamicData['approval']);
    model.notifikasi = NotifikasiModel.fromDynamic(dynamicData['notifikasi']);
    model.lembur = LemburModel.fromDynamic(dynamicData['lembur']);

    return model;
  }
}

class AbsensiModel {
  String? title;
  bool? seeAllDivisi;

  AbsensiModel();

  static AbsensiModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static AbsensiModel fromDynamic(dynamic dynamicData) {
    final model = AbsensiModel();

    model.title = dynamicData['title'] ?? "Absensi";
    model.seeAllDivisi =
        MahasFormat.dynamicToBool(dynamicData['seeAllDivisi']) ?? false;

    return model;
  }
}

class DataPribadiModel {
  bool? cekmenunggu;
  bool? nipvisible;
  bool? nipedit;
  bool? gradestepvisible;
  bool? pangkatvisible;
  bool? kategorisdmvisible;
  bool? bpjsKesehatanEdit;
  bool? bpjsKetenagakerjaanEdit;
  bool? npwpEdit;

  DataPribadiModel();

  static DataPribadiModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static DataPribadiModel fromDynamic(dynamic dynamicData) {
    final model = DataPribadiModel();

    model.cekmenunggu =
        MahasFormat.dynamicToBool(dynamicData['cekMenunggu']) ?? true;
    model.nipvisible =
        MahasFormat.dynamicToBool(dynamicData['nipVisible']) ?? true;
    model.nipedit = MahasFormat.dynamicToBool(dynamicData['nipEdit']) ?? true;
    model.gradestepvisible =
        MahasFormat.dynamicToBool(dynamicData['gradeStepVisible']) ?? true;
    model.pangkatvisible =
        MahasFormat.dynamicToBool(dynamicData['pangkatVisible']) ?? true;
    model.kategorisdmvisible =
        MahasFormat.dynamicToBool(dynamicData['kategoriSdmVisible']) ?? true;
    model.bpjsKesehatanEdit =
        MahasFormat.dynamicToBool(dynamicData['bpjsKesehatanEdit']) ?? true;
    model.bpjsKetenagakerjaanEdit =
        MahasFormat.dynamicToBool(dynamicData['bpjsKetenagakerjaanEdit']) ??
            true;
    model.npwpEdit = MahasFormat.dynamicToBool(dynamicData['npwpEdit']) ?? true;

    return model;
  }
}

class KontakModel {
  bool? hashistory;

  KontakModel();

  static KontakModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static KontakModel fromDynamic(dynamic dynamicData) {
    final model = KontakModel();

    model.hashistory =
        MahasFormat.dynamicToBool(dynamicData['hasHistory']) ?? true;

    return model;
  }
}

class CutiTahunanModel {
  bool? quotaVisible;
  int? pengajuanHari;

  CutiTahunanModel();

  static CutiTahunanModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static CutiTahunanModel fromDynamic(dynamic dynamicData) {
    final model = CutiTahunanModel();

    model.quotaVisible =
        MahasFormat.dynamicToBool(dynamicData['quotaVisible']) ?? false;
    model.pengajuanHari =
        MahasFormat.dynamicToInt(dynamicData['pengajuanHari']) ?? 0;

    return model;
  }
}

class CutiHamilModel {
  String? tittle;
  bool? anakKeVisible;
  bool? selamaVisible;
  bool? berapaHariVisible;
  bool? autoAdd45Days;
  CutiHamilModel();

  static CutiHamilModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static CutiHamilModel fromDynamic(dynamic dynamicData) {
    final model = CutiHamilModel();

    model.tittle = dynamicData['tittle'] ?? 'Cuti Hamil';
    model.anakKeVisible =
        MahasFormat.dynamicToBool(dynamicData['anakKeVisible']) ?? true;
    model.selamaVisible =
        MahasFormat.dynamicToBool(dynamicData['selamaVisible']) ?? true;
    model.berapaHariVisible =
        MahasFormat.dynamicToBool(dynamicData['berapaHariVisible']) ?? false;
    model.autoAdd45Days =
        MahasFormat.dynamicToBool(dynamicData['autoAdd45Days']) ?? false;

    return model;
  }
}

class IzinModel {
  String? tittle;
  String? tittleQuotaIzin;

  IzinModel();

  static IzinModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static IzinModel fromDynamic(dynamic dynamicData) {
    final model = IzinModel();

    model.tittle = dynamicData['tittle'] ?? 'Izin';
    model.tittleQuotaIzin = dynamicData['tittleQuotaIzin'] ?? 'Quota Izin';

    return model;
  }
}

class CutiSakitModel {
  String? tittle;
  int? hMin;
  int? hPlus;
  bool? cekBackDate;

  CutiSakitModel();

  static CutiSakitModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static CutiSakitModel fromDynamic(dynamic dynamicData) {
    final model = CutiSakitModel();

    model.tittle = dynamicData['tittle'] ?? 'Cuti Sakit';
    model.hMin = MahasFormat.dynamicToInt(dynamicData['hMin']) ?? 0;
    model.hPlus = MahasFormat.dynamicToInt(dynamicData['hPlus']) ?? 0;
    model.cekBackDate =
        MahasFormat.dynamicToBool(dynamicData['cekBackDate']) ?? false;

    return model;
  }
}

class ApprovalModel {
  bool? khususAtasan;
  bool? hanyaManager;
  bool? visibleTitle;
  bool? bisaBatal;

  ApprovalModel();

  static ApprovalModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static ApprovalModel fromDynamic(dynamic dynamicData) {
    final model = ApprovalModel();

    model.khususAtasan = dynamicData['khususAtasan'] ?? false;
    model.hanyaManager = dynamicData['hanyaManager'] ?? false;
    model.visibleTitle = dynamicData['visibleTitle'] ?? true;
    model.bisaBatal = dynamicData['bisaBatal'] ?? true;

    return model;
  }
}

class NotifikasiModel {
  int? tanggalNotifJadwal;

  NotifikasiModel();

  static NotifikasiModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static NotifikasiModel fromDynamic(dynamic dynamicData) {
    final model = NotifikasiModel();

    model.tanggalNotifJadwal =
        MahasFormat.dynamicToInt(dynamicData['tanggalNotifJadwal']) ?? 25;

    return model;
  }
}

class LemburModel {
  bool? dokumenPenugasan;

  LemburModel();

  static LemburModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static LemburModel fromDynamic(dynamic dynamicData) {
    final model = LemburModel();

    model.dokumenPenugasan =
        MahasFormat.dynamicToBool(dynamicData['dokumenPenugasan']) ?? false;

    return model;
  }
}
