import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class ProfilekeluargaModel {
  int? id;
  int? idPegawai;
  String? pegawaiNama;
  int? idHubungankeluarga;
  String? namahubungankeluarga;
  String? nama;
  String? jeniskelamin;
  String? alamatdomisili;
  String? nobpjs;
  String? nobpjstk;
  bool? ditanggungpegawai;
  DateTime? tglpermintaan;
  DateTime? tglapprove;
  bool? approval;
  String? alasanTolak;
  int? idPegawaiApproval;
  String? pegawaiapprovalnama;
  String? status;
  List<ProfilekeluargaDetailkeluargakontakModel>? detailkeluargakontak;

  ProfilekeluargaModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static ProfilekeluargaModel fromDynamic(dynamic dynamicData) {
    final model = ProfilekeluargaModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.pegawaiNama = dynamicData['pegawaiNama'];
    model.idHubungankeluarga =
        MahasFormat.dynamicToInt(dynamicData['id_HubunganKeluarga']);
    model.namahubungankeluarga = dynamicData['namaHubunganKeluarga'];
    model.nama = dynamicData['nama'];
    model.jeniskelamin = dynamicData['jenisKelamin'];
    model.alamatdomisili = dynamicData['alamatDomisili'];
    model.nobpjs = dynamicData['noBPJS'];
    model.nobpjstk = dynamicData['noBPJSTK'];
    model.ditanggungpegawai =
        MahasFormat.dynamicToBool(dynamicData['ditanggungPegawai']);
    model.tglpermintaan =
        MahasFormat.dynamicToDateTime(dynamicData['tglPermintaan']);
    model.approval = MahasFormat.dynamicToBool(dynamicData['approval']);
    model.alasanTolak = dynamicData['alasanTolak'];
    model.idPegawaiApproval =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Approval']);
    model.pegawaiapprovalnama = dynamicData['pegawaiApprovalNama'];
    model.tglapprove = MahasFormat.dynamicToDateTime(dynamicData['tglApprove']);
    model.status = dynamicData['status'];
    if (dynamicData['detailPegawaiJenisKontak'] != null) {
      final detailT = dynamicData['detailPegawaiJenisKontak'] as List;
      model.detailkeluargakontak = [];
      for (var i = 0; i < detailT.length; i++) {
        model.detailkeluargakontak!.add(
            ProfilekeluargaDetailkeluargakontakModel.fromDynamic(detailT[i]));
      }
    }

    return model;
  }
}

class ProfilekeluargaDetailkeluargakontakModel {
  int? id;
  int? idPegawaikeluarga;
  int? idJeniskontak;
  String? namajeniskontak;
  String? nama;

  ProfilekeluargaDetailkeluargakontakModel();

  ProfilekeluargaDetailkeluargakontakModel.init(
      this.idJeniskontak, this.nama, this.namajeniskontak);

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static ProfilekeluargaDetailkeluargakontakModel fromDynamic(
      dynamic dynamicData) {
    final model = ProfilekeluargaDetailkeluargakontakModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idPegawaikeluarga =
        MahasFormat.dynamicToInt(dynamicData['id_PegawaiKeluarga']);
    model.idJeniskontak =
        MahasFormat.dynamicToInt(dynamicData['id_JenisKontak']);
    model.namajeniskontak = dynamicData['namaJenisKontak'];
    model.nama = dynamicData['nama'];

    return model;
  }
}
