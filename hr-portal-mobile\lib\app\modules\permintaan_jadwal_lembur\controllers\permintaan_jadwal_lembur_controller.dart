import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/mahas/services/http_api.dart';
import 'package:hr_portal/app/models/divisi_model.dart';
import 'package:hr_portal/app/models/lembur_model.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../routes/app_pages.dart';

class PermintaanJadwalLemburController extends GetxController {
  late ListComponentController<LemburModel> listCon;

  List<DivisiModel>? listDivisi;
  RxBool isKadiv = false.obs;
  bool isDinamis = MahasConfig.profileMenu.approvalDinamis;

  @override
  void onInit() {
    getDivisi();
    listCon = ListComponentController<LemburModel>(
      urlApi: (index, filter) {
        if (MahasConfig.profileMenu.customLemburPage) {
          return '/api/PenugasanLembur/List?pageIndex=$index';
        } else {
          return '/api/PermintaanJadwal/Lembur/List?pageIndex=$index';
        }
      },
      fromDynamic: (e) => LemburModel.fromDynamic(e),
      allowSearch: false,
    );
    super.onInit();
  }

  getDivisi() async {
    var r = await HttpApi.get('/api/Profile/Divisi');
    if (r.success) {
      listDivisi = divisiModelFromJson(r.body);
      for (var i = 0; i < listDivisi!.length; i++) {
        if (MahasConfig.selectedDivisi == listDivisi![i].id) {
          if (MahasConfig.profile!.nama == listDivisi![i].kadiv) {
            isKadiv.value = true;
          }
        }
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning("Gagal memuat data, silahkan cek koneksi internet");
    } else {
      Helper.dialogWarning(r.message);
    }
  }

  void addOnPress() {
    MahasConfig.profileMenu.customLemburPage
        ? Get.toNamed(Routes.PERMINTAAN_JADWAL_LEMBUR_CUSTOM_KEPALA_UNIT_SETUP,
            arguments: {"pegawai": false})?.then((value) {
            if (value) {
              listCon.refresh();
            }
          })
        : Get.toNamed(Routes.PERMINTAAN_JADWAL_LEMBUR_SETUP)?.then((value) {
            if (value) {
              listCon.refresh();
            }
          });
  }

  void itemOnTab(int id, String? approveManager) {
    MahasConfig.profileMenu.customLemburPage
        ? Get.toNamed(
            Routes.PERMINTAAN_JADWAL_LEMBUR_CUSTOM_KEPALA_UNIT_SETUP,
            arguments: {"pegawai": true},
            parameters: {
              'id': id.toString(),
              "approval": "approval",
            },
          )?.then((value) {
            if (value) {
              listCon.refresh();
            }
          })
        : Get.toNamed(
            Routes.PERMINTAAN_JADWAL_LEMBUR_SETUP,
            parameters: {
              'id': id.toString(),
              'isKadiv': isKadiv.value.toString(),
              'status': approveManager.toString(),
            },
          )?.then((value) {
            if (value) {
              listCon.refresh();
            }
          });
  }
}
