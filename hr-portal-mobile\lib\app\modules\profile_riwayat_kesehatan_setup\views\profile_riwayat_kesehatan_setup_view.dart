import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_datetime_component.dart';

import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../controllers/profile_riwayat_kesehatan_setup_controller.dart';

class ProfileRiwayatKesehatanSetupView
    extends GetView<ProfileRiwayatKesehatanSetupController> {
  const ProfileRiwayatKesehatanSetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      controller: controller.formCon,
      title: 'Informasi',
      children: () => [
        InputTextComponent(
          label: 'Nomor',
          controller: controller.nomorCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDatetimeComponent(
          label: 'Tanggal',
          controller: controller.tanggalCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputFileComponent(
          label: 'File pendukung (*pdf)',
          controller: controller.fileCon,
          required: true,
          editable: controller.formCon.editable,
        ),
      ],
    );
  }
}
