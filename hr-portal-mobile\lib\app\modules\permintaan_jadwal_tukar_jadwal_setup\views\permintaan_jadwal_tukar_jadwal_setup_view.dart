import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_datetime_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_dropdown_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_radio_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_text_component.dart';
import 'package:hr_portal/app/mahas/components/mahas_themes.dart';
import 'package:hr_portal/app/mahas/components/pages/setup_page_component.dart';
import 'package:hr_portal/app/mahas/mahas_colors.dart';

import '../../../mahas/mahas_config.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas_complement/mahas_colors.dart';
import '../../../mahas_complement/status_request.dart';
import '../controllers/permintaan_jadwal_tukar_jadwal_setup_controller.dart';

class PermintaanJadwalTukarJadwalSetupView
    extends GetView<PermintaanJadwalTukarJadwalSetupController> {
  const PermintaanJadwalTukarJadwalSetupView({super.key});

  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      title: "Tukar Jadwal",
      controller: controller.formCon,
      children: () => [
        InputDatetimeComponent(
          label: "Dari Tanggal",
          controller: controller.dariTanggalCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        controller.formCon.isState == SetupPageState.update ||
                controller.formCon.isState == SetupPageState.create
            ? Obx(() {
                return Visibility(
                  visible: controller.keTanggalVisible.value,
                  child: InputDatetimeComponent(
                    label: "Ke Tanggal",
                    controller: controller.keTanggalCon,
                    required: true,
                    editable: controller.formCon.editable,
                  ),
                );
              })
            : InputDatetimeComponent(
                label: "Ke Tanggal",
                controller: controller.keTanggalCon,
                required: true,
                editable: controller.formCon.editable,
              ),
        controller.formCon.isState == SetupPageState.update ||
                controller.formCon.isState == SetupPageState.create
            ? Obx(() {
                return Visibility(
                    visible: controller.shiftVisible.value,
                    child: InputRadioComponent(
                      label: "Shift",
                      controller: controller.shiftCon,
                      required: true,
                      editable: controller.formCon.editable,
                    ));
              })
            : InputTextComponent(
                label: "Shift",
                controller: controller.viewShiftCon,
                required: true,
                editable: false,
              ),
        controller.formCon.isState == SetupPageState.update ||
                controller.formCon.isState == SetupPageState.create
            ? Obx(() {
                return Visibility(
                    visible: controller.listPengganti.value,
                    child: InputDropdownComponent(
                      label: "Pengganti",
                      controller: controller.tukarCon,
                      required: true,
                      editable: controller.formCon.editable,
                    ));
              })
            : InputTextComponent(
                label: "Pengganti",
                controller: controller.viewPenggantiCon,
                editable: false,
              ),
        Obx(() => Visibility(
              visible: controller.alasanVisible.value,
              child: InputTextComponent(
                label: "Alasan",
                controller: controller.alasanCon,
                required: true,
                editable: controller.formCon.editable,
              ),
            )),
        controller.isDinamisApprove == false ? statis() : dinamis(),
      ],
    );
  }

  Column statis() {
    return Column(
      children: [
        Obx(
          () {
            return Visibility(
              visible: controller.isVisible.value,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Expanded(
                    child: ElevatedButton(
                        onPressed: () {
                          alert();
                        },
                        style: ElevatedButton.styleFrom(
                            backgroundColor: MahasColors.red),
                        child: const Text("Tolak")),
                  ),
                  const SizedBox(
                    width: 20,
                  ),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        controller.approvalOnPress(true);
                      },
                      child: const Text("Terima"),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
        Visibility(
          visible: controller.allowED == false ? true : false,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 10,
              ),
              const Text(
                'Status Request :',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(
                height: 5,
              ),
              Container(
                decoration: BoxDecoration(
                    border: Border.all(
                      color: MahasColor.primaryGrey,
                    ),
                    borderRadius:
                        BorderRadius.circular(MahasThemes.borderRadius)),
                padding: const EdgeInsets.all(10),
                child: Row(
                  children: [
                    InkWell(
                      onTap: () async => await Helper.fotoProfilOnTap(
                          controller.idPegawaiRequest.value),
                      child: Padding(
                        padding: const EdgeInsets.only(right: 10),
                        child: CircleAvatar(
                          radius: 25,
                          backgroundColor: MahasColors.primary,
                          child: CircleAvatar(
                            backgroundColor: Colors.white,
                            radius: 23,
                            child: Center(
                              child: Text(
                                "Lihat\nFoto",
                                style: MahasThemes.link.copyWith(fontSize: 12),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Request oleh',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(controller.pegawaiReq.value),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 15,
              ),
              Container(
                decoration: BoxDecoration(
                    border: Border.all(
                      color: MahasColor.primaryGrey,
                    ),
                    borderRadius:
                        BorderRadius.circular(MahasThemes.borderRadius)),
                padding: const EdgeInsets.all(10),
                child: GetBuilder(
                  builder:
                      (PermintaanJadwalTukarJadwalSetupController controller) =>
                          Row(
                    children: [
                      InkWell(
                        onTap: () async => await Helper.fotoProfilOnTap(
                            controller.idPegawaiPengganti.value),
                        child: Padding(
                          padding: const EdgeInsets.only(right: 10),
                          child: CircleAvatar(
                            radius: 25,
                            backgroundColor: MahasColors.primary,
                            child: CircleAvatar(
                              backgroundColor: Colors.white,
                              radius: 23,
                              child: Center(
                                child: Text(
                                  "Lihat\nFoto",
                                  style:
                                      MahasThemes.link.copyWith(fontSize: 12),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Pegawai Tukar',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(controller.pegawaiPegawai.value),
                            controller.accPegawai.value == null
                                ? const Row(
                                    children: [
                                      Icon(
                                        FontAwesomeIcons.clock,
                                        size: 12,
                                      ),
                                      Text(" Menunggu")
                                    ],
                                  )
                                : controller.accPegawai.value == true
                                    ? const Row(
                                        children: [
                                          Icon(
                                            FontAwesomeIcons.check,
                                            size: 12,
                                            color: MahasColor.colorGreen,
                                          ),
                                          Text(
                                            ' Diterima',
                                            style: TextStyle(
                                              color: MahasColor.colorGreen,
                                            ),
                                          ),
                                        ],
                                      )
                                    : const Row(
                                        children: [
                                          Icon(
                                            FontAwesomeIcons.circleXmark,
                                            size: 12,
                                            color: MahasColors.red,
                                          ),
                                          Text(
                                            " Ditolak",
                                            style: TextStyle(
                                                color: MahasColors.red),
                                          ),
                                        ],
                                      ),
                            controller.accPegawai.value == false
                                ? Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                          'Alasan : "${controller.alasanTolak.value}"'),
                                    ],
                                  )
                                : const SizedBox(),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(
                height: 15,
              ),
              Container(
                decoration: BoxDecoration(
                    border: Border.all(
                      color: MahasColor.primaryGrey,
                    ),
                    borderRadius:
                        BorderRadius.circular(MahasThemes.borderRadius)),
                padding: const EdgeInsets.all(10),
                child: GetBuilder(
                  builder:
                      (PermintaanJadwalTukarJadwalSetupController controller) =>
                          Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Kadiv',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(controller.pegawaiKadiv.value),
                      controller.accKadiv.value == null
                          ? Row(
                              children: [
                                Visibility(
                                  visible: controller.accPegawai.value != false,
                                  child: const Icon(
                                    FontAwesomeIcons.clock,
                                    size: 12,
                                  ),
                                ),
                                Text(controller.accPegawai.value == false
                                    ? "Tidak dilanjutkan"
                                    : " Menunggu")
                              ],
                            )
                          : controller.accKadiv.value == true
                              ? const Row(
                                  children: [
                                    Icon(
                                      FontAwesomeIcons.check,
                                      size: 12,
                                      color: MahasColor.colorGreen,
                                    ),
                                    Text(
                                      ' Diterima',
                                      style: TextStyle(
                                        color: MahasColor.colorGreen,
                                      ),
                                    ),
                                  ],
                                )
                              : const Row(
                                  children: [
                                    Icon(
                                      FontAwesomeIcons.circleXmark,
                                      size: 12,
                                      color: MahasColors.red,
                                    ),
                                    Text(
                                      " Ditolak",
                                      style: TextStyle(color: MahasColors.red),
                                    ),
                                  ],
                                ),
                      controller.accKadiv.value == false
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                    'Alasan : "${controller.alasanTolak.value}"'),
                              ],
                            )
                          : const SizedBox(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Visibility dinamis() {
    return Visibility(
      visible: controller.allowED == false ? true : false,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Detail Request :',
            style: TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(
            height: 5,
          ),
          DetailRequest(
            tittle: "Request Oleh",
            idPegawaiPengganti: controller.idPegawaiRequest.value,
            namaPegawai: controller.pegawaiReq.value,
          ),
          const SizedBox(
            height: 15,
          ),
          Obx(() => DetailRequest(
                tittle: "Pegawai Tukar",
                idPegawaiPengganti: controller.idPegawaiPengganti.value,
                namaPegawai: controller.pegawaiPegawai.value,
                withStatus: true,
                isAcc: controller.accPegawai.value,
                alasanTolak: controller.alasanTolak.value,
              )),
          const SizedBox(
            height: 15,
          ),
          Obx(
            () => ApprovalStatusContainer(
              textStatus:
                  MahasConfig.appName != "SI MADE RS Bhayangkara Denpasar"
                      ? "Status Approval :"
                      : "",
              isEditable: controller.formCon.editable,
              isVisible: controller.isVisible,
              isBatal: false.obs,
              allowED: controller.allowED,
              isDinamisApprove: controller.isDinamisApprove,
              alasanTolakController: controller.alasanTolakCon,
              onTolakPressed: () {
                controller.approvalOnPress(false);
              },
              onTerimaPressed: () {
                controller.approvalOnPress(true);
              },
              batalOnPress: () {
                alert();
              },
              pegawaiKadiv: "",
              pegawaiManager: "",
              accKadiv: false,
              accManager: false,
              statusTolak: controller.alasanTolak.value,
              modelApproval: controller.modelApproval,
              isStop: controller.isStop.value,
            ),
          ),
        ],
      ),
    );
  }

  Future<dynamic> alert() {
    return Get.dialog(
      AlertDialog(
        content: SizedBox(
          width: 1000,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              InputTextComponent(
                label: "Alasan Ditolak",
                controller: controller.alasanTolakCon,
                required: true,
              ),
            ],
          ),
        ),
        contentPadding:
            const EdgeInsets.only(bottom: 0, top: 20, right: 10, left: 10),
        actionsPadding:
            const EdgeInsets.only(top: 0, bottom: 0, left: 10, right: 10),
        actions: [
          TextButton(
            child: Text(
              "Close",
              style: MahasThemes.muted,
            ),
            onPressed: () {
              Get.back(result: false);
            },
          ),
          TextButton(
            child: const Text(
              "Simpan",
            ),
            onPressed: () {
              if (controller.alasanTolakCon.isValid) {
                controller.approvalOnPress(false);
                Get.back(result: true);
              }
            },
          ),
        ],
      ),
    );
  }
}

class DetailRequest extends StatelessWidget {
  final int idPegawaiPengganti;
  final String tittle;
  final String namaPegawai;
  final bool withStatus;
  final bool? isAcc;
  final String? alasanTolak;

  const DetailRequest({
    super.key,
    required this.idPegawaiPengganti,
    required this.tittle,
    required this.namaPegawai,
    this.withStatus = false,
    this.isAcc,
    this.alasanTolak,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          border: Border.all(
            color: MahasColor.primaryGrey,
          ),
          borderRadius: BorderRadius.circular(MahasThemes.borderRadius)),
      padding: const EdgeInsets.all(10),
      child: Row(
        children: [
          InkWell(
            onTap: () async => await Helper.fotoProfilOnTap(idPegawaiPengganti),
            child: Padding(
              padding: const EdgeInsets.only(right: 10),
              child: CircleAvatar(
                radius: 25,
                backgroundColor: MahasColors.primary,
                child: CircleAvatar(
                  backgroundColor: Colors.white,
                  radius: 23,
                  child: Center(
                    child: Text(
                      "Lihat\nFoto",
                      style: MahasThemes.link.copyWith(fontSize: 12),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  tittle,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(namaPegawai),
                if (withStatus)
                  isAcc == null
                      ? const Row(
                          children: [
                            Icon(
                              FontAwesomeIcons.clock,
                              size: 12,
                            ),
                            Text(" Menunggu")
                          ],
                        )
                      : isAcc == true
                          ? const Row(
                              children: [
                                Icon(
                                  FontAwesomeIcons.check,
                                  size: 12,
                                  color: MahasColor.colorGreen,
                                ),
                                Text(
                                  ' Diterima',
                                  style: TextStyle(
                                    color: MahasColor.colorGreen,
                                  ),
                                ),
                              ],
                            )
                          : const Row(
                              children: [
                                Icon(
                                  FontAwesomeIcons.circleXmark,
                                  size: 12,
                                  color: MahasColors.red,
                                ),
                                Text(
                                  " Ditolak",
                                  style: TextStyle(color: MahasColors.red),
                                ),
                              ],
                            ),
                isAcc == false
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('Alasan : $alasanTolak'),
                        ],
                      )
                    : const SizedBox(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
