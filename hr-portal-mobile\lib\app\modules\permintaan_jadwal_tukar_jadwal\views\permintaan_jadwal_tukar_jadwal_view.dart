import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas_complement/mahas_colors.dart';
import 'package:hr_portal/app/models/tukar_jadwal_model.dart';
import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/others/list_component.dart';
import '../../../mahas/mahas_colors.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../mahas_complement/approval_status.dart';
import '../controllers/permintaan_jadwal_tukar_jadwal_controller.dart';

class PermintaanJadwalTukarJadwalView
    extends GetView<PermintaanJadwalTukarJadwalController> {
  const PermintaanJadwalTukarJadwalView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tukar Jadwal'),
        centerTitle: true,
        actions: <Widget>[
          Padding(
            padding: const EdgeInsets.only(right: 10),
            child: GestureDetector(
              onTap: controller.addOnPress,
              child: const Icon(
                FontAwesomeIcons.plus,
                size: 24,
              ),
            ),
          ),
        ],
      ),
      body: controller.isDinamis == false ? listStatis() : listDinamis(),
    );
  }

  ListComponent<TukarJadwalModel> listStatis() {
    return ListComponent(
      controller: controller.listCon,
      itemBuilder: (TukarJadwalModel e) {
        return InkWell(
          onTap: () => controller.itemOnTab(e.id!),
          child: Padding(
            padding:
                const EdgeInsets.only(left: 12, right: 12, top: 8, bottom: 8),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(e.alasan ?? "", style: MahasThemes.title),
                      const SizedBox(height: 5),
                      ApprovalStatusWidget(
                        pegawai: e.pegawaipengganti.toString(),
                        approveStatus: e.approvepengganti,
                      ),
                      const SizedBox(height: 5),
                      ApprovalStatusWidget(
                        pegawai: e.pegawaikadiv.toString(),
                        approveStatus: e.approvekadiv,
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      "Dari ${MahasFormat.displayDate(e.jadwaltanggalrequest)}",
                      style: MahasColor.muted,
                    ),
                    Text(
                      "Ke ${MahasFormat.displayDate(e.jadwaltanggalpengganti)}",
                      style: MahasColor.muted,
                    ),
                  ],
                ),
                const SizedBox(height: 5),
              ],
            ),
          ),
        );
      },
    );
  }

  ListComponent<TukarJadwalModel> listDinamis() {
    return ListComponent(
      controller: controller.listCon,
      itemBuilder: (TukarJadwalModel e) {
        return Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => controller.itemOnTab(e.id!),
                child: Padding(
                  padding: const EdgeInsets.only(
                      left: 12, right: 12, top: 12, bottom: 0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Pegawai Pengganti', style: MahasThemes.muted),
                      Text(e.pegawaipengganti.toString(),
                          style: MahasThemes.title),
                      e.approvepengganti.toString() == "null"
                          ? const Row(
                              children: [
                                Icon(
                                  FontAwesomeIcons.clock,
                                  size: 12,
                                ),
                                Text(" Menunggu")
                              ],
                            )
                          : e.approvepengganti == false
                              ? const Row(
                                  children: [
                                    Icon(
                                      FontAwesomeIcons.circleXmark,
                                      size: 12,
                                      color: MahasColors.red,
                                    ),
                                    Text(
                                      " Ditolak",
                                      style: TextStyle(color: MahasColors.red),
                                    )
                                  ],
                                )
                              : e.approvepengganti == true
                                  ? const Row(
                                      children: [
                                        Icon(
                                          FontAwesomeIcons.check,
                                          size: 12,
                                          color: MahasColor.colorGreen,
                                        ),
                                        Text(
                                          " Diterima",
                                          style: TextStyle(
                                            color: MahasColor.colorGreen,
                                          ),
                                        )
                                      ],
                                    )
                                  : const Row(
                                      children: [
                                        Icon(
                                          FontAwesomeIcons.clock,
                                          size: 12,
                                        ),
                                        Text(" Menunggu")
                                      ],
                                    ),
                      const SizedBox(height: 5),
                    ],
                  ),
                ),
              ),
            ),
            InkWell(
              onTap: () async {
                if (MahasConfig.appName != "SI MADE RS Bhayangkara Denpasar") {
                  await Helper.statusApproval(
                    "/api/PermintaanJadwal/TukarJadwal/Approval/${e.id}",
                    alasanTolak: e.alasanTolak,
                    approvePengganti: e.approvepengganti,
                  );
                }
              },
              child: Padding(
                padding: const EdgeInsets.only(
                    left: 12, right: 12, top: 12, bottom: 0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          "Dari ${MahasFormat.displayDate(e.jadwaltanggalrequest)}",
                          style: MahasColor.muted,
                        ),
                        Text(
                          "Ke ${MahasFormat.displayDate(e.jadwaltanggalpengganti)}",
                          style: MahasColor.muted,
                        ),
                      ],
                    ),
                    Visibility(
                      visible: MahasConfig.appName !=
                          "SI MADE RS Bhayangkara Denpasar",
                      child: Text(
                        "Lihat Status",
                        style: TextStyle(
                          color: MahasColor.primaryColor.shade300,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
