import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class LemburgantiuangModel {
  int? id;
  int? idDivisi;
  int? idPegawai;
  int? idPegawaiKadiv;
  DateTime? tanggalinput;
  String? alasan;
  bool? approvemanager;
  DateTime? tanggaljam;
  int? lamajam;
  String? jenis;
  int? idPegawaiManager;
  String? pegawai;
  String? pegawaikadiv;
  String? pegawaimanager;
  String? divisi;

  LemburgantiuangModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static LemburgantiuangModel fromDynamic(dynamic dynamicData) {
    final model = LemburgantiuangModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idDivisi = MahasFormat.dynamicToInt(dynamicData['id_Divisi']);
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.idPegawaiKadiv =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Kadiv']);
    model.tanggalinput =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalInput']);
    model.alasan = dynamicData['alasan'];
    model.approvemanager =
        MahasFormat.dynamicToBool(dynamicData['approveManager']);
    model.tanggaljam = MahasFormat.dynamicToDateTime(dynamicData['tanggalJam']);
    model.lamajam = MahasFormat.dynamicToInt(dynamicData['lamaJam']);
    model.jenis = dynamicData['jenis'];
    model.idPegawaiManager =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Manager']);
    model.pegawai = dynamicData['pegawai'];
    model.pegawaikadiv = dynamicData['pegawaiKadiv'];
    model.pegawaimanager = dynamicData['pegawaiManager'];
    model.divisi = dynamicData['divisi'];

    return model;
  }
}
