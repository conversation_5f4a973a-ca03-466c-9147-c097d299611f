import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_datetime_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_radio_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_text_component.dart';
import 'package:hr_portal/app/mahas/components/mahas_themes.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:hr_portal/app/mahas/models/api_list_resut_model.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';
import 'package:hr_portal/app/models/golongan_darah_model.dart';

import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../mahas_complement/mahas_colors.dart';
import '../../../models/agama_model.dart';
import '../../../models/kategori_sdm_model.dart';
import '../../../models/pangkat_model.dart';
import '../../../models/profile_model.dart';
import '../../../routes/app_pages.dart';

List<dynamic> datas = [
  {"id": 1, "tanggal": "2022-09-01", "status": "Menunggu"},
  {"id": 2, "tanggal": "2022-08-21", "status": "Ditolak"},
  {"id": 3, "tanggal": "2022-08-26", "status": "Diterima"},
];

class ProfileDataPribadiController extends GetxController {
  late SetupPageController formCon;
  final namaCon = InputTextController();
  final nipCon = InputTextController(type: InputTextType.number);
  final gradeStepCon = InputTextController();
  final jabatanCon = InputTextController();
  final tempatLahirCon = InputTextController();
  final tanggalLahirCon = InputDatetimeController();
  final alamatKtpCon = InputTextController(type: InputTextType.paragraf);
  final alamatDomisiliCon = InputTextController(type: InputTextType.paragraf);
  final agamaCon = InputDropdownController();
  final statusPerkawinanCon = InputDropdownController(
    items: [
      DropdownItem.init("Kawin", 1),
      DropdownItem.init("Belum Kawin", 2),
    ],
  );
  final noKtpCon = InputTextController(type: InputTextType.number);
  final noKKCon = InputTextController(type: InputTextType.number);
  final noBpjsKesCon = InputTextController(type: InputTextType.number);
  final noBpjsTKcon = InputTextController(type: InputTextType.number);
  final noNpwpCon = InputTextController(type: InputTextType.number);
  final ptkpPegawaiCon = InputTextController();
  final emailCon = InputTextController(type: InputTextType.email);
  final mulaiBekerjaCon = InputDatetimeController();
  final statusPegawaiCon = InputTextController();
  final selesaiBekerjaCon = InputDatetimeController();
  final pangkatCon = InputDropdownController();
  final kategoriSdmCon = InputDropdownController();
  final golonganDarahCon = InputDropdownController();
  final sukuBangsaCon = InputTextController();
  final jenisKelaminCon = InputRadioController(
    items: [
      RadioButtonItem(text: "Laki-laki", value: 1),
      RadioButtonItem(text: "Perempuan", value: 2),
    ],
  );
  final masaKerjaCon = InputTextController();

  RxBool alamatSama = false.obs;
  RxList<HistoryProfileModel> listHistoryModel = RxList<HistoryProfileModel>();

  RxBool masihAdaRequest = false.obs;

  FocusNode focusNode = FocusNode();
  String? akhirKontrak;

  @override
  void onInit() async {
    akhirKontrak = Get.parameters['akhirkontrak'];
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/Profile',
      urlApiPut: (id) => '/api/HistoryDataPegawai',
      allowDelete: false,
      allowHistory: true,
      bodyApi: (id) => {
        "id_Pegawai": MahasConfig.profile!.id,
        "id_Divisi": MahasConfig.selectedDivisi,
        "nama": namaCon.value,
        "tanggalLahir": MahasFormat.dateToString(tanggalLahirCon.value),
        "tempatLahir": tempatLahirCon.value,
        "alamatKTP": alamatKtpCon.value,
        "alamatDomisili": alamatDomisiliCon.value,
        "id_Agama": agamaCon.value,
        "statusPerkawinan":
            statusPerkawinanCon.value == 1 ? "Kawin" : "Belum Kawin",
        "noKTP": noKtpCon.value.toString(),
        "noKK": noKKCon.value.toString(),
        "noBPJSKesehatan": noBpjsKesCon.value.toString(),
        "noBPJSTK": noBpjsTKcon.value.toString(),
        "noNPWP": noNpwpCon.value.toString(),
        "nip": nipCon.value.toString(),
        "id_Pangkat": pangkatCon.value,
        "id_GolonganDarah": golonganDarahCon.value,
        "id_KategoriSdm": kategoriSdmCon.value,
        "sukuBangsa": sukuBangsaCon.value,
        "jenisKelamin": jenisKelaminCon.value == 1 ? "Laki-laki" : "Perempuan",
      },
      itemKey: (e) => 1,
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (masihAdaRequest.value == true) {
          Helper.dialogWarning(
              "Masih ada request perubahan data sebelumnya yang belum di approve dari bagian HRD");
          return false;
        }
        if (!namaCon.isValid) return false;
        if (!nipCon.isValid) return false;
        if (!pangkatCon.isValid) return false;
        if (!tempatLahirCon.isValid) return false;
        if (!tanggalLahirCon.isValid) return false;
        if (!golonganDarahCon.isValid) return false;
        if (!jenisKelaminCon.isValid) return false;
        if (!sukuBangsaCon.isValid) return false;
        if (!alamatKtpCon.isValid) return false;
        if (!alamatDomisiliCon.isValid) return false;
        if (!agamaCon.isValid) return false;
        if (!statusPerkawinanCon.isValid) return false;
        if (!noKtpCon.isValid) return false;
        if (!noKKCon.isValid) return false;
        if (!noBpjsKesCon.isValid) return false;
        if (!noBpjsTKcon.isValid) return false;
        if (!noNpwpCon.isValid) return false;

        return true;
      },
      apiToView: (json) {
        ProfileModel model = ProfileModel.fromJson(json);
        if (model.alamatktp!.isNotEmpty &&
            model.alamatdomisili!.isNotEmpty &&
            model.alamatktp == model.alamatdomisili) {
          alamatSama.value = true;
          update();
        }
        namaCon.value = model.nama;
        nipCon.value = model.nip;
        ptkpPegawaiCon.value = model.statusptkp;
        jabatanCon.value = model.namajabatan;
        gradeStepCon.value = model.namagradestep;
        tempatLahirCon.value = model.tempatlahir;
        tanggalLahirCon.value = model.tanggalLahir;
        alamatKtpCon.value = model.alamatktp;
        alamatDomisiliCon.value = model.alamatdomisili;
        agamaCon.value = model.idAgama;
        statusPerkawinanCon.value = null;
        if (model.statusperkawinan!.isNotEmpty) {
          statusPerkawinanCon.value = model.statusperkawinan == "Kawin" ? 1 : 2;
        }
        noKtpCon.value = model.noktp;
        noKKCon.value = model.nokk;
        noBpjsKesCon.value = model.nobpjskesehatan;
        noBpjsTKcon.value = model.nobpjstk;
        noNpwpCon.value = model.nonpwp;
        emailCon.value = model.email;
        mulaiBekerjaCon.value = model.tanggalmulaibekerja;
        statusPegawaiCon.value = model.statusPegawai;
        selesaiBekerjaCon.value = model.tanggalselesaibekerja;
        pangkatCon.value = model.idPangkat;
        golonganDarahCon.value = model.idGolongandarah;
        sukuBangsaCon.value = model.sukubangsa;
        kategoriSdmCon.value = model.idKategoriSdm;
        jenisKelaminCon.value = null;
        if (model.jeniskelamin!.isNotEmpty) {
          jenisKelaminCon.value = model.jeniskelamin == "Laki-laki" ? 1 : 2;
        }
        masaKerjaCon.value = model.masakerja;
      },
      onInit: () async {
        //get dropdown data
        await getAgama();
      },
      onHistory: () async {
        history();
      },
      onRefreshWhenSubmit: (r) {
        var idHistory = json.decode(r.body)['id'];
        getHistorys();

        if (idHistory != 0) {
          itemOnTab(idHistory, "false");
        }
      },
    );
    getHistorys();
    super.onInit();
  }

  @override
  void onReady() {
    if (akhirKontrak == "true") {
      focusNode.requestFocus();
    }
    super.onReady();
  }

  //start get data procedure (must inline)
  Future<void> getAgama() async {
    EasyLoading.show();
    var r = await HttpApi.get("/api/Agama");
    if (r.success) {
      RxList<AgamaModel> listModel = RxList<AgamaModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(AgamaModel.fromDynamic(e));
      }
      if (agamaCon.items.isNotEmpty) {
        agamaCon.items.clear();
      }
      if (r.body != null && agamaCon.items.isEmpty) {
        agamaCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      }
    } else {
      if (EasyLoading.isShow) {
        EasyLoading.dismiss();
      }
    }
    await getPangkat();
  }

  Future<void> getPangkat() async {
    var r = await HttpApi.get("/api/Pangkat");
    if (r.success) {
      RxList<PangkatModel> listModel = RxList<PangkatModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(PangkatModel.fromDynamic(e));
      }
      if (pangkatCon.items.isNotEmpty) {
        pangkatCon.items.clear();
      }
      if (r.body != null && pangkatCon.items.isEmpty) {
        pangkatCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      } else {
        if (EasyLoading.isShow) {
          EasyLoading.dismiss();
        }
      }
    }
    await getGolonganDarah();
  }

  Future<void> getGolonganDarah() async {
    var r = await HttpApi.get("/api/GolonganDarah");
    if (r.success) {
      RxList<GolonganDarahModel> listModel = RxList<GolonganDarahModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(GolonganDarahModel.fromDynamic(e));
      }
      if (golonganDarahCon.items.isNotEmpty) {
        golonganDarahCon.items.clear();
      }
      if (r.body != null && golonganDarahCon.items.isEmpty) {
        golonganDarahCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      } else {
        if (EasyLoading.isShow) {
          EasyLoading.dismiss();
        }
      }
    }
    await getKategoriSdm();
  }

  Future<void> getKategoriSdm() async {
    var r = await HttpApi.get("/api/KategoriSdm");
    if (r.success) {
      RxList<KategoriSdmModel> listModel = RxList<KategoriSdmModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(KategoriSdmModel.fromDynamic(e));
      }
      if (kategoriSdmCon.items.isNotEmpty) {
        kategoriSdmCon.items.clear();
      }
      if (r.body != null && kategoriSdmCon.items.isEmpty) {
        kategoriSdmCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      } else {
        if (EasyLoading.isShow) {
          EasyLoading.dismiss();
        }
      }
    }
    EasyLoading.dismiss();
  }
  //end get data procedure (must inline)

  Future<void> getHistorys() async {
    listHistoryModel.clear();
    var r = await HttpApi.get("/api/HistoryDataPegawai/List");
    if (r.success) {
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listHistoryModel.add(HistoryProfileModel.fromDynamic(e));
      }
    } else {
      if (EasyLoading.isShow) {
        EasyLoading.dismiss();
      }
    }
    var hasilPencarian = listHistoryModel.any((e) => e.approved == null);
    if (MahasConfig.dinamisForm.datapribadi?.cekmenunggu == true) {
      if (hasilPencarian == true) {
        masihAdaRequest.value = true;
      } else {
        masihAdaRequest.value = false;
      }
    }
  }

  Future<void> history() async {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(15),
        height: Get.height,
        decoration: const BoxDecoration(
          color: MahasColor.backgroundColor,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            const Text(
              "History",
              style: TextStyle(
                fontSize: 20,
              ),
            ),
            Expanded(
              child: ListView.builder(
                itemCount: listHistoryModel.length,
                itemBuilder: (context, index) {
                  var item = listHistoryModel[index];
                  return ListTile(
                    title: Text(
                        MahasFormat.dateToString(item.tanggalrequest) ??
                            "tanggal",
                        style: MahasThemes.title),
                    trailing: Text(
                        item.approved == null
                            ? "Menunggu"
                            : item.approved == true
                                ? "Diterima"
                                : "Ditolak",
                        style: MahasThemes.muted),
                    onTap: () {
                      Get.back();
                      if (item.approved == null) {
                        itemOnTab(item.id!, "false");
                      } else {
                        itemOnTab(item.id!, "true");
                      }
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void itemOnTab(int id, String approve) {
    Get.toNamed(
      Routes.PROFILE_DATA_PRIBADI_HISTORY,
      parameters: {
        'id': id.toString(),
        'approval': approve,
      },
    )!
        .then(
      (value) async => {
        await getHistorys(),
      },
    );
  }
}
