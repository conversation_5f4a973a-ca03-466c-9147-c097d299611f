// ignore_for_file: unused_local_variable

import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_datetime_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_text_component.dart';
import 'package:hr_portal/app/mahas/components/pages/setup_page_component.dart';
import 'package:hr_portal/app/mahas/models/api_result_model.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/mahas/services/http_api.dart';
import 'package:hr_portal/app/models/cuti_sakit_model.dart';
import 'package:intl/intl.dart';
import 'package:path/path.dart' as p;

import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/components/inputs/input_radio_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../models/pegawai_approval.dart';

class PermintaanJadwalCutiSakitSetupController extends GetxController {
  late SetupPageController formCon;
  final berapaHariCon = InputTextController(type: InputTextType.number);
  final alasanCon = InputTextController(type: InputTextType.paragraf);
  final pegawaiCutiCon = InputTextController();

  final fileCon = InputFileController(
    urlImage: true,
    type: FileType.custom,
    tipe: InputFileType.pdf,
    extension: ["pdf", "jpg", "jpeg", "png"],
  );

  final keteranganCon = InputTextController(type: InputTextType.paragraf);
  final rawatInapCon = InputRadioController(
    items: [
      RadioButtonItem.autoId("Ya", true),
      RadioButtonItem.autoId("Tidak", false),
    ],
  );
  final sampaiTanggalCon = InputDatetimeController();
  final dariTanggalCon = InputDatetimeController();
  final alasanTolakCon = InputTextController();
  final String nama = MahasConfig.profile!.nama!;
  late String approve;
  late bool allowED;
  Rxn<bool> accKadiv = Rxn<bool>();
  Rxn<bool> accManager = Rxn<bool>();
  RxBool isVisible = false.obs;
  late RxString pegawaiKadiv = ''.obs;
  late RxString pegawaiManager = ''.obs;
  late String kadiv;
  late bool isKadiv;
  RxString statusTolak = ''.obs;
  RxBool isBatal = false.obs;
  RxInt idPegawaiRequest = 0.obs;
  final isDinamisApprove = MahasConfig.profileMenu.approvalDinamis;
  RxBool isStop = false.obs;
  RxList<PegawaiApprovalModel> modelApproval = <PegawaiApprovalModel>[].obs;
  int? id;
  RxString tglPengajuan = ''.obs;
  RxBool requiredKeterangan = true.obs;
  String title = MahasConfig.dinamisForm.cutiSakit?.tittle ?? "Cuti Sakit";

  @override
  void onInit() {
    cekKadiv();
    cekApproval();
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PermintaanJadwal/CutiSakit/$id',
      urlApiPost: () => '/api/PermintaanJadwal/CutiSakit',
      urlApiPut: (id) => '/api/PermintaanJadwal/CutiSakit/$id',
      urlApiDelete: (id) => '/api/PermintaanJadwal/CutiSakit/$id',
      allowDelete: allowED,
      allowEdit: allowED,
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "berapaHari": berapaHariCon.value,
        "alasan": alasanCon.value,
        "dariTanggal": MahasFormat.dateToString(dariTanggalCon.value),
        "sampaiTanggal": MahasFormat.dateToString(sampaiTanggalCon.value),
        "filePhoto": fileCon.value,
        "fileName": fileCon.name,
        "rawatInap": rawatInapCon.value,
        "keterangan": keteranganCon.value
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (!dariTanggalCon.isValid) return false;
        if (!sampaiTanggalCon.isValid) return false;
        berapaHariCon.value =
            sampaiTanggalCon.value.difference(dariTanggalCon.value).inDays + 1;

        if (MahasConfig.dinamisForm.cutiSakit?.cekBackDate == true) {
          var hMin = MahasConfig.dinamisForm.cutiSakit!.hMin!;
          var hPlus = MahasConfig.dinamisForm.cutiSakit?.hPlus;
          if (dariTanggalCon.value.isBefore(
                  DateTime.now().subtract(Duration(days: hMin + 1))) ||
              dariTanggalCon.value
                  .isAfter(DateTime.now().add(Duration(days: hPlus!)))) {
            Helper.dialogWarning(
                "Tanggal Maksimal Pengajuan $title Adalah H-$hMin Atau H+$hPlus Dari Hari Ini");
            return false;
          }
        }

        if (!alasanCon.isValid) return false;
        if (!rawatInapCon.isValid) return false;
        if (requiredKeterangan.value && !keteranganCon.isValid) return false;
        return true;
      },
      successMessage: Helper.succesPengajuanMessage(),
      apiToView: (json) {
        CutiSakitModel model = CutiSakitModel.fromJson(json);
        id = model.id;
        berapaHariCon.value = model.berapaHari;
        alasanCon.value = model.alasan;
        dariTanggalCon.value = model.dariTanggal;
        pegawaiCutiCon.value = model.pegawaiRequest;

        accManager.value = model.approveManager;
        accKadiv.value = model.approveKadiv;
        pegawaiKadiv.value = model.pegawaiKadiv!;
        pegawaiManager.value = model.pegawaiManager!;
        idPegawaiRequest.value = model.idPegawaiRequest!;
        fileCon.value = model.pathFoto;
        sampaiTanggalCon.value = model.sampaiTanggal;
        keteranganCon.value = model.keterangan;
        rawatInapCon.value = model.rawatInap;
        requiredKeterangan.value = model.rawatInap == true ? true : false;

        var format = DateFormat("dd MMMM yyyy HH:mm");
        tglPengajuan.value = format.format(model.tanggalInput!);

        statusTolak.value = model.alasanTolak ?? '';

        // approval
        if (isDinamisApprove == false && allowED == false) {
          if ((model.approveKadiv == null &&
                  model.idPegawaiKadiv == MahasConfig.profile!.id) ||
              (model.approveKadiv == true &&
                  model.approveManager == null &&
                  model.idPegawaiManager == MahasConfig.profile!.id)) {
            isVisible.value = true;
          }
        }

        // batal
        if (MahasConfig.dinamisForm.approval?.bisaBatal == true) {
          if (isDinamisApprove == false && allowED == false) {
            if (model.approveKadiv == true &&
                model.approveManager == true &&
                model.idPegawaiManager == MahasConfig.profile!.id) {
              isBatal.value = true;
            }
          }
        }
        if (isDinamisApprove == true && allowED == false) {
          getDataApproval();
        }

        if (model.pathFoto != null) {
          String extension = p.extension(model.pathFoto!).toLowerCase();
          if (extension == ".pdf") {
            updateState(InputFileType.pdf);
          } else {
            updateState(InputFileType.image);
          }
        }
      },
    );
    cekValidasi();
    super.onInit();
  }

  void updateState(InputFileType tipe) {
    fileCon.tipe = tipe;
  }

  void cekValidasi() {
    rawatInapCon.onChanged = (item) {
      if (item.value == true) {
        requiredKeterangan.value = true;
      } else {
        requiredKeterangan.value = false;
      }
    };
    dariTanggalCon.onChanged = () {
      if (sampaiTanggalCon.value != null) {
        berapaHariCon.value =
            sampaiTanggalCon.value.difference(dariTanggalCon.value).inDays + 1;
      }
    };
    sampaiTanggalCon.onChanged = () {
      if (dariTanggalCon.value != null) {
        berapaHariCon.value =
            sampaiTanggalCon.value.difference(dariTanggalCon.value).inDays + 1;
      }
    };
    sampaiTanggalCon.onCheck = (date) {
      DateTime dariTanggal = dariTanggalCon.value;
      DateTime sampaiTanggal = date ?? DateTime.now();
      if (sampaiTanggal.isBefore(dariTanggal)) {
        Helper.dialogWarning(
            "Tanggal Selesai tidak boleh kurang dari Tanggal Mulai");
        return false;
      }
      return true;
    };
    sampaiTanggalCon.onClear = () {
      berapaHariCon.value = 0;
    };
    dariTanggalCon.onClear = () {
      berapaHariCon.value = 0;
    };
  }

  Future<void> batalOnPress() async {
    var id = Get.parameters['id'].toString();
    String urlManager = '/api/PermintaanJadwal/CutiSakit/Batal/Manager/';
    var action = await Helper.batalAction(
      id,
      alasanTolakCon.value,
      urlManager,
    );
    if (action.success) {
      final url = '/api/PermintaanJadwal/CutiSakit/$id';
      ApiResultModel r;
      r = await HttpApi.get(url);
      if (action.success) {
        CutiSakitModel model = CutiSakitModel.fromJson(r.body);
        accKadiv.value = model.approveKadiv;
        accManager.value = model.approveManager;
        statusTolak.value = model.alasanTolak ?? '';
        isBatal.value = false;
        update();
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan aksi, silahkan cek koneksi internet");
    } else if (!action.success) {
      Helper.dialogWarning(
        "Gagal melakukan aksi",
        resMassage: action.message,
        noInternetMassage:
            "Gagal melakukan aksi, silahkan cek koneksi internet",
      );
    }
  }

  Future<void> approvalOnPress(bool approve) async {
    var id = Get.parameters['id'].toString();
    String urlKadiv = '/api/PermintaanJadwal/CutiSakit/Approve/Kadiv/';
    String urlManager = '/api/PermintaanJadwal/CutiSakit/Approve/Manager/';
    String urlApprove = '/api/PermintaanJadwal/CutiSakit/Approve/';
    var action = await Helper.approvalAction(
      id,
      approve,
      alasanTolakCon.value,
      accKadiv.value,
      urlKadiv,
      urlManager,
      isDinamis: isDinamisApprove,
      urlApprove: urlApprove,
    );
    if (action.success) {
      final url = '/api/PermintaanJadwal/CutiSakit/$id';
      ApiResultModel r;
      r = await HttpApi.get(url);
      if (r.success) {
        CutiSakitModel model = CutiSakitModel.fromJson(r.body);
        accKadiv.value = model.approveKadiv;
        accManager.value = model.approveManager;
        statusTolak.value = model.alasanTolak ?? '';
        if (isDinamisApprove) {
          getDataApproval();
        }
        isVisible.value = false;
        update();
      } else if (MahasConfig.hasInternet == false) {
        Helper.dialogWarning(
            "Gagal melakukan approve, silahkan cek koneksi internet");
      } else if (!action.success) {
        Helper.dialogWarning(action.message);
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan approve, silahkan cek koneksi internet");
    } else if (!action.success) {
      Helper.dialogWarning(
        "Gagal melakukan approval",
        resMassage: action.message,
        noInternetMassage:
            "Gagal melakukan approval, silahkan cek koneksi internet",
      );
    }
  }

  void cekKadiv() {
    kadiv = Get.parameters['isKadiv'].toString();
    if (kadiv == "false") {
      isKadiv = false;
    } else {
      isKadiv = true;
    }
  }

  void cekApproval() {
    approve = Get.parameters['approval'].toString();
    if (approve == "approval") {
      allowED = false;
    } else {
      allowED = true;
    }
  }

  Future<void> getDataApproval() async {
    EasyLoading.show();
    final url = '/api/PermintaanJadwal/CutiSakit/Approval/$id';
    final r = await HttpApi.get(url);
    modelApproval.clear();
    if (r.success) {
      if (r.body != null) {
        var data = json.decode(r.body);
        for (var e in data['datas']) {
          modelApproval.add(PegawaiApprovalModel.fromDynamic(e));
        }
        bool canApprove = modelApproval.any((item) =>
            item.idPegawaiapprove == MahasConfig.profile?.id &&
            item.approve == null);
        if (canApprove) {
          isVisible.value = true;
        }
        bool notApprove = modelApproval.any((item) => item.approve == false);
        if (notApprove) {
          isVisible.value = false;
        }
        isStop.value = modelApproval.any((item) => item.approve == false);
      } else {
        Helper.dialogWarning(r.message);
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan aksi, silahkan cek koneksi internet");
    } else if (r.statusCode == 404) {
      Helper.dialogWarning("Tidak Ada Data Pegawai Approval");
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }
}
