import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_datetime_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_dropdown_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_lookup_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_text_component.dart';
import 'package:hr_portal/app/mahas/components/pages/setup_page_component.dart';
import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas_complement/status_request.dart';
import '../controllers/permintaan_jadwal_lembur_setup_controller.dart';

class PermintaanJadwalLemburSetupView
    extends GetView<PermintaanJadwalLemburSetupController> {
  const PermintaanJadwalLemburSetupView({super.key});

  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      title: "Lembur",
      crossAxisAlignmentChildren: CrossAxisAlignment.start,
      controller: controller.formCon,
      children: () => [
        InputLookupComponent(
          label: "Request Lembur Kepada",
          required: true,
          controller: controller.staffLemburCon,
          editable: controller.formCon.editable,
          marginBottom: 5,
        ),
        InkWell(
          onTap: () async {
            if (controller.idPegawaiRequest.value != 0) {
              await Helper.fotoProfilOnTap(controller.idPegawaiRequest.value);
            } else {
              Helper.dialogWarning("Pilih pegawai terlebih dahulu!");
            }
          },
          child: SizedBox(
            height: 30,
            child: Text(
              "Lihat Foto Profil Pegawai",
              style: MahasThemes.link,
              textAlign: TextAlign.left,
            ),
          ),
        ),
        InputDatetimeComponent(
          label: "Tanggal Mulai",
          required: true,
          controller: controller.tanggalMulaiCon,
          editable: controller.formCon.editable,
        ),
        InputDatetimeComponent(
          type: InputDatetimeType.time,
          label: "Jam Mulai",
          required: true,
          controller: controller.jamMulaiCon,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Lembur Berapa Jam",
          controller: controller.lamaJamCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDropdownComponent(
          label: "Jenis Lembur",
          controller: controller.jenisCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Alasan Lembur",
          controller: controller.alasanCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        Obx(
          () => ApprovalStatusContainer(
            isEditable: controller.formCon.editable,
            isVisible: controller.isVisible,
            isBatal: controller.isBatal,
            allowED: controller.allowED,
            isDinamisApprove: controller.isDinamisApprove,
            alasanTolakController: controller.alasanTolakCon,
            onTolakPressed: () {
              controller.approvalOnPress(false);
            },
            onTerimaPressed: () {
              controller.approvalOnPress(true);
            },
            batalOnPress: () {
              var status = controller.isBatal.isTrue ? 'batal' : 'tolak';
              alert(status);
            },
            pegawaiKadiv: "",
            pegawaiManager: controller.pegawaiManager.value,
            accKadiv: controller.accKadiv.value,
            accManager: controller.accManager.value,
            statusTolak: controller.alasanTolak.value,
            modelApproval: controller.modelApproval,
            isStop: controller.isStop.value,
          ),
        ),
      ],
    );
  }

  Future<dynamic> alert(String status) {
    return Get.dialog(
      AlertDialog(
        content: SizedBox(
          width: 1000,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              InputTextComponent(
                label: status == "tolak" ? "Alasan Ditolak" : "Alasan Batal",
                controller: controller.alasanTolakCon,
                required: true,
              ),
            ],
          ),
        ),
        contentPadding:
            const EdgeInsets.only(bottom: 0, top: 20, right: 10, left: 10),
        actionsPadding:
            const EdgeInsets.only(top: 0, bottom: 0, left: 10, right: 10),
        actions: [
          TextButton(
            child: Text(
              "Close",
              style: MahasThemes.muted,
            ),
            onPressed: () {
              Get.back(result: false);
            },
          ),
          TextButton(
            child: const Text(
              "Simpan",
            ),
            onPressed: () {
              if (controller.alasanTolakCon.isValid) {
                if (status == "tolak") {
                  controller.approvalOnPress(false);
                } else if (status == "batal") {
                  controller.batalOnPress();
                }
                Get.back(result: true);
              }
            },
          ),
        ],
      ),
    );
  }
}
