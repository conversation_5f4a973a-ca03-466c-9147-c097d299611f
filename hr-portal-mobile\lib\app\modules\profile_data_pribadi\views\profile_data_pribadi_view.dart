import 'package:ensure_visible_when_focused/ensure_visible_when_focused.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_datetime_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_radio_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_text_component.dart';
import 'package:hr_portal/app/mahas/mahas_colors.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_config.dart';
import '../controllers/profile_data_pribadi_controller.dart';

class ProfileDataPribadiView extends GetView<ProfileDataPribadiController> {
  const ProfileDataPribadiView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      title: "Data Pribadi",
      controller: controller.formCon,
      children: () => [
        InputTextComponent(
          label: "Nama",
          controller: controller.namaCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          focusNode: controller.focusNode,
          label: "NRP/NIP/NITK",
          controller: controller.nipCon,
          required: true,
          editable: MahasConfig.dinamisForm.datapribadi?.nipedit ?? true
              ? controller.formCon.editable
              : false,
        ),
        Visibility(
          visible:
              MahasConfig.dinamisForm.datapribadi?.gradestepvisible ?? true,
          child: InputTextComponent(
            label: "Grade Step",
            controller: controller.gradeStepCon,
            editable: false,
          ),
        ),
        InputTextComponent(
          label: "Jabatan",
          controller: controller.jabatanCon,
          editable: false,
        ),
        Visibility(
          visible: MahasConfig.dinamisForm.datapribadi?.pangkatvisible ?? true,
          child: InputDropdownComponent(
            label: "Pangkat",
            controller: controller.pangkatCon,
            editable: controller.formCon.editable,
          ),
        ),
        Visibility(
          visible:
              MahasConfig.dinamisForm.datapribadi?.kategorisdmvisible ?? true,
          child: InputDropdownComponent(
            label: "Kategori SDM",
            controller: controller.kategoriSdmCon,
            editable: controller.formCon.editable,
          ),
        ),
        InputTextComponent(
          label: "Tempat Lahir",
          controller: controller.tempatLahirCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDatetimeComponent(
          label: "Tanggal Lahir",
          controller: controller.tanggalLahirCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDropdownComponent(
          label: "Golongan Darah",
          controller: controller.golonganDarahCon,
          editable: controller.formCon.editable,
        ),
        InputRadioComponent(
          label: "Jenis Kelamin",
          controller: controller.jenisKelaminCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Suku Bangsa",
          controller: controller.sukuBangsaCon,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Alamat KTP",
          controller: controller.alamatKtpCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        GetBuilder(
          builder: (ProfileDataPribadiController c) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Switch(
                      activeColor: MahasColors.primary,
                      value: c.alamatSama.value,
                      onChanged: (v) {
                        if (c.formCon.editable) {
                          c.alamatSama.value = v;
                          if (c.alamatSama.value) {
                            c.alamatDomisiliCon.value = c.alamatKtpCon.value;
                          } else {
                            if (c.alamatDomisiliCon.value != null) {
                              c.alamatDomisiliCon.value = null;
                            }
                          }
                          c.update();
                        }
                      },
                    ),
                    Text(
                      "Alamat Domisili sama dengan alamat KTP",
                      style: MahasThemes.muted,
                    ),
                  ],
                ),
                const SizedBox(
                  height: 10,
                ),
                Visibility(
                  visible: !c.alamatSama.value,
                  child: InputTextComponent(
                    label: "Alamat Domisili",
                    controller: controller.alamatDomisiliCon,
                    required: true,
                    editable: controller.formCon.editable,
                  ),
                ),
              ],
            );
          },
        ),
        InputDropdownComponent(
          label: "Agama",
          controller: controller.agamaCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDropdownComponent(
          label: "Status Perkawinan",
          controller: controller.statusPerkawinanCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Nomor KTP",
          controller: controller.noKtpCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Nomor KK",
          controller: controller.noKKCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Nomor BPJS Kesehatan",
          controller: controller.noBpjsKesCon,
          editable:
              MahasConfig.dinamisForm.datapribadi?.bpjsKesehatanEdit ?? true
                  ? controller.formCon.editable
                  : false,
        ),
        InputTextComponent(
          label: "Nomor BPJS Tenaga Kerja",
          controller: controller.noBpjsTKcon,
          editable:
              MahasConfig.dinamisForm.datapribadi?.bpjsKetenagakerjaanEdit ??
                      true
                  ? controller.formCon.editable
                  : false,
        ),
        InputTextComponent(
          label: "Nomor NPWP",
          controller: controller.noNpwpCon,
          editable: MahasConfig.dinamisForm.datapribadi?.npwpEdit ?? true
              ? controller.formCon.editable
              : false,
        ),
        InputTextComponent(
          label: "PTKP Pegawai",
          controller: controller.ptkpPegawaiCon,
          editable: false,
        ),
        InputTextComponent(
          label: "Alamat Email",
          controller: controller.emailCon,
          required: true,
          editable: false,
        ),
        Visibility(
          visible: controller.masaKerjaCon.value != null ? true : false,
          child: InputTextComponent(
            label: "Masa Kerja",
            controller: controller.masaKerjaCon,
            editable: false,
          ),
        ),
        InputDatetimeComponent(
          label: "Tanggal Mulai Bekerja",
          controller: controller.mulaiBekerjaCon,
          editable: false,
        ),
        InputTextComponent(
          label: "Status Pegawai",
          controller: controller.statusPegawaiCon,
          editable: false,
        ),
        EnsureVisibleWhenFocused(
          focusNode: controller.focusNode,
          child: InputDatetimeComponent(
            label: "Tanggal Selesai Bekerja",
            controller: controller.selesaiBekerjaCon,
            editable: false,
          ),
        ),
      ],
    );
  }
}
