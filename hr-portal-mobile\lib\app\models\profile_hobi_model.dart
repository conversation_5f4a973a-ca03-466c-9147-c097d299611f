import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class ProfilehobiModel {
  int? id;
  int? idPegawai;
  String? nama;

  ProfilehobiModel();

  static ProfilehobiModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static ProfilehobiModel fromDynamic(dynamic dynamicData) {
    final model = ProfilehobiModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.nama = dynamicData['nama'];

    return model;
  }
}
