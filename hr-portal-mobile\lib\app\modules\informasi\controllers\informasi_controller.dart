import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:hr_portal/app/mahas/models/menu_item_model.dart';
import 'package:hr_portal/app/routes/app_pages.dart';

class InformasiController extends GetxController {
  final List<MenuItemModel> menus = [];

  void cDataAbsensiSaya() {
    Get.toNamed(Routes.DATA_ABSENSI_LIST);
  }

  void cAbsensiDivisi() {
    Get.toNamed(Routes.INFORMASI_ABSENSI_SESUAI_DIVISI);
  }

  void cAbsensiJadwalDivisi() {
    Get.toNamed(Routes.INFORMASI_ABSENSI_JADWAL_ANGGOTA_DIVISI);
  }

  void cTukarLembur() {
    Get.toNamed(Routes.INFORMASI_QUOTA_TUKAR_LEMBUR);
  }

  void cQuotaIzin() {
    Get.toNamed(Routes.INFORMASI_QUOTA_IZIN);
  }

  void cTahunan() {
    Get.toNamed(Routes.INFORMASI_QUOTA_CUTI_TAHUNAN);
  }

  void cGantiUang() {
    Get.toNamed(Routes.INFORMASI_LEMBUR_GANTI_UANG);
  }

  void cSlipGaji() {
    Get.toNamed(Routes.GAJI);
  }

  void cPerpus() {
    Get.toNamed(Routes.INFORMASI_PERPUS);
  }

  void cSerahTerima() {
    Get.toNamed(Routes.INFORMASI_SERAH_TERIMA);
  }

  void cPengumuman() {
    Get.toNamed(Routes.INFORMASI_PENGUMUMAN);
  }

  void cPeringatan() {
    Get.toNamed(Routes.INFORMASI_PERINGATAN);
  }

  @override
  void onInit() {
    if (MahasConfig.profileMenu.absensiSaya == true) {
      menus.add(MenuItemModel(
          'Data Absensi Saya', Icons.access_time, cDataAbsensiSaya));
    }
    if (MahasConfig.profileMenu.absensiDivisi == true) {
      menus.add(MenuItemModel('Absensi Anggota Divisi',
          FontAwesomeIcons.clipboardUser, cAbsensiDivisi));
    }
    if (MahasConfig.atasan.isManager == true ||
        MahasConfig.profile?.melihatsemuaabsensi == true) {
      menus.add(MenuItemModel('Absensi Jadwal Anggota Divisi',
          FontAwesomeIcons.clipboardList, cAbsensiJadwalDivisi));
    }
    if (MahasConfig.profileMenu.quotaCutiTukarLembur == true) {
      menus.add(MenuItemModel(
          'Quota Cuti Tukar Lembur', FontAwesomeIcons.userClock, cTukarLembur));
    }
    if (MahasConfig.profileMenu.jenisIzin == true) {
      menus.add(MenuItemModel('Quota Jenis Izin',
          FontAwesomeIcons.personCircleQuestion, cQuotaIzin));
    }
    menus.add(MenuItemModel(
        'Cuti Tahunan', FontAwesomeIcons.calendarXmark, cTahunan));
    if (MahasConfig.profileMenu.quotaLemburGantiUang == true) {
      menus.add(MenuItemModel('Quota Lembur Ganti Uang',
          FontAwesomeIcons.businessTime, cGantiUang));
    }
    if (MahasConfig.profileMenu.slipGaji == true) {
      menus.add(
          MenuItemModel('Slip Gaji', FontAwesomeIcons.dollarSign, cSlipGaji));
    }
    if (MahasConfig.profileMenu.perpus == true) {
      menus.add(MenuItemModel('E-Perpus', FontAwesomeIcons.book, cPerpus));
    }
    if (MahasConfig.profileMenu.serahTerima == true) {
      menus.add(MenuItemModel(
          'Serah Terima', FontAwesomeIcons.handHoldingDollar, cSerahTerima));
    }
    menus.add(
        MenuItemModel('Pengumuman', FontAwesomeIcons.bullhorn, cPengumuman));
    menus.add(MenuItemModel(
        'Peringatan', FontAwesomeIcons.triangleExclamation, cPeringatan));
    super.onInit();
  }
}
