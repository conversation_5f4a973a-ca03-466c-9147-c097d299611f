import 'dart:convert';

class KadivmanagerModel {
  String? kadiv;
  String? manager;

  // Constructor default dengan nilai opsional
  KadivmanagerModel({this.kadiv = 'Kadiv', this.manager = 'Manager'});

  // Mengambil data dari JSON
  static KadivmanagerModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  // Mengambil data dari dynamic
  static KadivmanagerModel fromDynamic(dynamic dynamicData) {
    final model = KadivmanagerModel(
      kadiv: dynamicData['kadiv'] ?? 'Kadiv',
      manager: dynamicData['manager'] ?? 'Manager',
    );

    return model;
  }
}

class AtasanModel {
  bool? isKadiv;
  bool? isManager;

  AtasanModel({this.isKadiv = false, this.isManager = false});
}
