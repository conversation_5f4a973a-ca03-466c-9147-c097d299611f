// To parse this JSON data, do
//
//     final divisiModel = divisiModelFromJson(jsonString);

import 'dart:convert';

List<DivisiModel> divisiModelFromJson(String str) => List<DivisiModel>.from(
    json.decode(str).map((x) => DivisiModel.fromJson(x)));

String divisiModelToJson(List<DivisiModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class DivisiModel {
  DivisiModel({
    this.id,
    this.nama,
    this.kadiv,
    this.manager,
    this.pegawai,
  });

  int? id;
  String? nama;
  String? kadiv;
  String? manager;
  List<Pegawai>? pegawai;

  factory DivisiModel.fromJson(Map<String, dynamic> json) => DivisiModel(
        id: json["id"],
        nama: json["nama"],
        kadiv: json["kadiv"],
        manager: json["manager"],
        pegawai:
            List<Pegawai>.from(json["pegawai"].map((x) => Pegawai.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "nama": nama,
        "kadiv": kadiv,
        "manager": manager,
        "pegawai": List<dynamic>.from(pegawai!.map((x) => x.toJson())),
      };
}

class Pegawai {
  Pegawai({
    this.nama,
  });

  String? nama;

  factory Pegawai.fromJson(Map<String, dynamic> json) => Pegawai(
        nama: json["nama"],
      );

  Map<String, dynamic> toJson() => {
        "nama": nama,
      };
}
