import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class RiwayatkesehatanModel {
  int? id;
  int? idPegawai;
  String? pegawainama;
  String? nomor;
  DateTime? tanggal;
  String? berkaspendukung;
  DateTime? tanggalRequest;
  bool? approved;
  String? alasanTolak;
  int? idPegawaiApproval;
  String? pegawaiapprovalnama;
  DateTime? tanggalApproval;
  String? status;
  int? idPegawaiRiwayatKesehatan;

  RiwayatkesehatanModel();

  static RiwayatkesehatanModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static RiwayatkesehatanModel fromDynamic(dynamic dynamicData) {
    final model = RiwayatkesehatanModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.pegawainama = dynamicData['pegawaiNama'];
    model.nomor = dynamicData['nomor'];
    model.tanggal = MahasFormat.dynamicToDateTime(dynamicData['tanggal']);
    model.berkaspendukung = dynamicData['berkasPendukung'];
    model.tanggalRequest =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalRequest']);
    model.approved = MahasFormat.dynamicToBool(dynamicData['approved']);
    model.alasanTolak = dynamicData['alasanTolak'];
    model.idPegawaiApproval =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Approval']);
    model.pegawaiapprovalnama = dynamicData['pegawaiApprovalNama'];
    model.tanggalApproval =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalApproval']);
    model.status = dynamicData['status'];
    model.idPegawaiRiwayatKesehatan =
        MahasFormat.dynamicToInt(dynamicData['id_PegawaiRiwayatKesehatan']);

    return model;
  }
}
