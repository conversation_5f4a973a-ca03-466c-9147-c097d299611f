import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../models/profile_riwayat_pendidikan_model.dart';
import '../../../routes/app_pages.dart';

class ProfileRiwayatPendidikanController extends GetxController {
  final listCon = ListComponentController<RiwayatpendidikanModel>(
    urlApi: (index, filter) =>
        '/api/PegawaiRiwayatPendidikan/List?pageIndex=$index',
    fromDynamic: RiwayatpendidikanModel.fromDynamic,
    allowSearch: false,
  );

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PROFILE_RIWAYAT_PENDIDIKAN_SETUP,
      parameters: {
        'id': id.toString(),
      },
    )?.then((value) {
      listCon.refresh();
    });
  }

  void popupMenuButtonOnSelected(String v) async {
    if (v == 'Tambah') {
      Get.toNamed(Routes.PROFILE_RIWAYAT_PENDIDIKAN_SETUP)?.then(
        (value) {
          listCon.refresh();
        },
      );
    } else if (v == 'History') {
      Get.toNamed(
        Routes.PROFILE_RIWAYAT_PENDIDIKAN_HISTORY,
        parameters: {
          'showStatus': true.toString(),
        },
      )?.then(
        (value) {
          listCon.refresh();
        },
      );
    }
  }
}
