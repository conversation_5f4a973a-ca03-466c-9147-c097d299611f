import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:intl/intl.dart';

import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/others/list_component.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../mahas_complement/mahas_colors.dart';
import '../../../models/gaji_model.dart';
import '../controllers/gaji_controller.dart';

class GajiView extends GetView<GajiController> {
  const GajiView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gaji'),
        centerTitle: true,
      ),
      body: MahasConfig.profileMenu.slipGajiStatis == false
          ? listGaji()
          : listGajiStatis(),
    );
  }

  ListComponent<GajiModel> listGaji() {
    return ListComponent(
      controller: controller.listCon,
      itemBuilder: (GajiModel e) {
        return InkWell(
          onTap: () => controller.itemOnTab(e.id!),
          child: Padding(
            padding:
                const EdgeInsets.only(left: 12, right: 12, top: 8, bottom: 8),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 5),
                      Text(
                          DateFormat.MMMM()
                              .format(DateTime(0, e.bulan!))
                              .toString(),
                          style: MahasThemes.title),
                      const SizedBox(height: 5),
                    ],
                  ),
                ),
                Text(
                  MahasFormat.displayDate(e.tanggal),
                  style: MahasColor.muted,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  ListComponent<SlipGajiStatisModel> listGajiStatis() {
    return ListComponent(
      controller: controller.listConStatis,
      itemBuilder: (SlipGajiStatisModel e) {
        return InkWell(
          onTap: () => controller.itemOnTab(e.id!),
          child: Padding(
            padding:
                const EdgeInsets.only(left: 12, right: 12, top: 8, bottom: 8),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 5),
                      Text(
                          DateFormat.MMMM()
                              .format(DateTime(0, e.bulan!))
                              .toString(),
                          style: MahasThemes.title),
                      const SizedBox(height: 5),
                    ],
                  ),
                ),
                Text(
                  MahasFormat.displayDate(e.tanggal),
                  style: MahasColor.muted,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
