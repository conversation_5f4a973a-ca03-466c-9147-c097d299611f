import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../controllers/profile_dokumen_setup_controller.dart';

class ProfileDokumenSetupView extends GetView<ProfileDokumenSetupController> {
  const ProfileDokumenSetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      controller: controller.formCon,
      title: "Dokumen Pegawai",
      children: () => [
        InputDropdownComponent(
          label: "<PERSON><PERSON> Dokumen",
          controller: controller.jenisDokumenCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Nomor",
          controller: controller.nomorDokumenCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputFileComponent(
          controller: controller.fileCon,
          label: "Upload Dokumen",
          editable: controller.formCon.editable,
          required: false,
        ),
      ],
    );
  }
}
