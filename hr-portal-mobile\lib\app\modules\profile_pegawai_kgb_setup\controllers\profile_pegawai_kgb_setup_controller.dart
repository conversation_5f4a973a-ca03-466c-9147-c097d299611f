import 'dart:convert';

import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../models/profile_kgb_model.dart';

class ProfilePegawaiKgbSetupController extends GetxController {
  late SetupPageController formCon;

  final nomorKepCon = InputTextController();
  final terhitungMulaiCon = InputDatetimeController();
  final kgbBerikutnyaCon = InputDatetimeController();

  @override
  void onInit() {
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PegawaiKGB/History/$id',
      allowEdit: false,
      allowDelete: false,
      bodyApi: (id) => {},
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (!nomorKepCon.isValid) return false;
        if (!terhitungMulaiCon.isValid) return false;
        if (!kgbBerikutnyaCon.isValid) return false;

        return true;
      },
      apiToView: (json) {
        KgbModel model = KgbModel.fromJson(json);
        nomorKepCon.value = model.nomorkep;
        terhitungMulaiCon.value = model.terhitungmulaitanggalberkala;
        kgbBerikutnyaCon.value = model.kgbberikutnya;
      },
    );

    super.onInit();
  }
}
