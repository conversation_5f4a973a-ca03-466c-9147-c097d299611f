import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/others/list_component.dart';
import '../../../models/profile_kontak_model.dart';
import '../controllers/profile_kontak_pegawai_controller.dart';

class ProfileKontakPegawaiView extends GetView<ProfileKontakPegawaiController> {
  const ProfileKontakPegawaiView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Kontak Pegawai'),
        centerTitle: true,
        actions: [
          GetBuilder<ProfileKontakPegawaiController>(
            builder: (controller) {
              return PopupMenuButton(
                onSelected: controller.popupMenuButtonOnSelected,
                itemBuilder: (context) {
                  List<PopupMenuItem<String>> r = [];
                  r.add(
                    const PopupMenuItem(
                      value: 'Tambah',
                      child: Text('Tambah'),
                    ),
                  );
                  if (MahasConfig.dinamisForm.kontak?.hashistory == true) {
                    r.add(
                      const PopupMenuItem(
                        value: 'History',
                        child: Text('History'),
                      ),
                    );
                  }

                  return r;
                },
              );
            },
          ),
        ],
      ),
      body: ListComponent<ProfilekontakModel>(
        controller: controller.listCon,
        itemBuilder: (e) {
          return ListTile(
            title: Text(e.nama ?? "-", style: MahasThemes.title),
            subtitle: Text(e.namajeniskontak ?? "-", style: MahasThemes.muted),
            onTap: () => controller.itemOnTab(e.id!),
          );
        },
      ),
    );
  }
}
