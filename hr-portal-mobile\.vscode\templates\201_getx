#packages: get
@@@lib/presentation/hyper_example/view/hyper_example_view.dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hyper_ui/core.dart';

class HyperExampleView extends StatelessWidget {
  const HyperExampleView({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(HyperExampleController());
    
    return Obx(() {
      if (controller.isLoading.value) {
        return const Scaffold(
          body: Center(
            child: CircularProgressIndicator(),
          ),
        );
      }

      if (controller.hasError.value) {
        return Scaffold(
          body: Center(
            child: Text("Error: ${controller.errorMessage.value}"),
          ),
        );
      }

      return Scaffold(
        appBar: AppBar(
          title: const Text("HyperExample"),
          actions: const [],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            children: [
              Text(
                "UniqueID: ${UniqueKey()}",
                style: const TextStyle(
                  fontSize: 18.0,
                ),
              ),
              const SizedBox(
                height: 12.0,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  IconButton(
                    onPressed: () => controller.decrement(),
                    icon: const Icon(Icons.remove, color: Colors.grey),
                  ),
                  Obx(() => Text(
                    "${controller.counter.value}",
                    style: const TextStyle(
                      fontSize: 20.0,
                      color: Colors.grey,
                    ),
                  )),
                  IconButton(
                    onPressed: () => controller.increment(),
                    icon: const Icon(Icons.add, color: Colors.grey),
                  ),
                ],
              ),
              const SizedBox(
                height: 12.0,
              ),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.black,
                  foregroundColor: Colors.white,
                ),
                onPressed: () => controller.initializeData(),
                child: const Text("Reload"),
              ),
            ],
          ),
        ),
      );
    });
  }
}
---
@@@lib/presentation/hyper_example/controller/hyper_example_controller.dart
import 'package:get/get.dart';

class HyperExampleController extends GetxController {
  final RxBool isLoading = false.obs;
  final RxBool hasError = false.obs;
  final RxString errorMessage = "".obs;
  final RxInt counter = 0.obs;

  @override
  void onInit() {
    super.onInit();
    initializeData();
  }

  Future<void> initializeData() async {
    isLoading.value = true;
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      isLoading.value = false;
      hasError.value = false;
    } catch (e) {
      isLoading.value = false;
      hasError.value = true;
      errorMessage.value = e.toString();
    }
  }

  void increment() {
    counter.value++;
  }

  void decrement() {
    counter.value--;
  }
}
---
@@@lib/presentation/hyper_example/widget/_
---