import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/others/list_component.dart';
import 'package:hr_portal/app/mahas/mahas_colors.dart';
import 'package:hr_portal/app/mahas/mahas_widget.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';
import 'package:hr_portal/app/mahas_complement/mahas_colors.dart';

import '../controllers/informasi_serah_terima_mutasi_barang_controller.dart';

class InformasiSerahTerimaMutasiBarangView
    extends GetView<InformasiSerahTerimaMutasiBarangController> {
  const InformasiSerahTerimaMutasiBarangView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('<PERSON><PERSON><PERSON>'),
        centerTitle: true,
        actions: [
          InkWell(
            onTap: controller.scanOnTap,
            child: const Padding(
              padding: EdgeInsets.symmetric(horizontal: 10),
              child: Icon(
                Icons.qr_code_scanner_rounded,
                size: 30,
                color: MahasColors.light,
              ),
            ),
          )
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.only(top: 10),
        child: ListComponent(
          controller: controller.listCon,
          separatorBuilder: (context, index, length) => const SizedBox(),
          itemBuilder: (e) {
            return MahasWidget.smoothRectangleItemWidget(
              ListTile(
                onTap: () => controller.itemOnTap(e.id.toString()),
                title: Text(e.noBukti ?? "-"),
                leading: const Icon(
                  FontAwesomeIcons.fileInvoice,
                  size: 35,
                ),
                subtitle: Text(
                  MahasFormat.displayDate(e.tanggal),
                  style: MahasColor.muted,
                ),
                trailing: e.approve == true
                    ? const Icon(
                        FontAwesomeIcons.circleCheck,
                        color: MahasColors.green,
                        size: 35,
                      )
                    : null,
                horizontalTitleGap: 0,
              ),
            );
          },
        ),
      ),
    );
  }
}
