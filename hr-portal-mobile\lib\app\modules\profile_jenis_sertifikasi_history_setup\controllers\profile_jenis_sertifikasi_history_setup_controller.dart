import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/models/api_list_resut_model.dart';
import '../../../mahas/models/api_result_model.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../models/jenis_sertifikasi_model.dart';
import '../../../models/profile_sertifikasi_model.dart';
import '../../../routes/app_pages.dart';

class ProfileJenisSertifikasiHistorySetupController extends GetxController {
  late SetupPageController formCon;
  late bool reqDelete;
  late bool showStatus;
  late bool withActionBack;

  final jenisSertifikasiCon = InputDropdownController();
  final namaSertifikasiCon = InputTextController();
  final noSertifikasiCon = InputTextController();
  final tanggalCon = InputDatetimeController();
  final berlakuSampaiCon = InputDatetimeController();
  final berkasPendukungCon = InputFileController(
    type: FileType.custom,
    extension: ['pdf'],
    tipe: InputFileType.pdf,
  );
  final alasanTolakCon = InputTextController();

  RxBool approvalButtonsVisible = false.obs;
  RxBool approvalFromNotif = false.obs;
  RxBool allowED = false.obs;
  late dynamic statusApprovalHRD = null.obs;
  RxString status = "".obs;
  RxString namaHRD = "".obs;
  RxString namaPegawaiRequest = "".obs;
  RxString alasanTolakHRD = "".obs;
  Rx<DateTime> tglPermintaan = DateTime.now().obs;
  RxInt idPegawaiSertifikasi = 0.obs;

  @override
  void onInit() async {
    //init
    String notifParam = Get.parameters['approval'].toString();
    approvalFromNotif.value = notifParam == "true" ? true : false;
    String showStatusParam = Get.parameters['showStatus'].toString();
    showStatus = showStatusParam == "true" ? true : false;
    String withActionBackParam = Get.parameters['withActionBack'].toString();
    withActionBack = withActionBackParam == "true" ? true : false;

    //formcon
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PegawaiSertifikasi/History/$id',
      urlApiPost: () => '/api/PegawaiSertifikasi/History',
      urlApiPut: (id) => '/api/PegawaiSertifikasi/History/$id',
      urlApiDelete: (id) =>
          '/api/PegawaiSertifikasi/History?id=$id&idDivisi=${MahasConfig.selectedDivisi}',
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "id_JenisSertifikasi": jenisSertifikasiCon.value,
        "nama": namaSertifikasiCon.value,
        "noSertifikasi": noSertifikasiCon.value,
        "tanggal": MahasFormat.dateToString(tanggalCon.value),
        "berlakuSampai": MahasFormat.dateToString(berlakuSampaiCon.value),
        "berkasPendukung": berkasPendukungCon.value,
        "id_PegawaiSertifikasi": idPegawaiSertifikasi.value,
      },
      allowDelete: allowED.value,
      allowEdit: allowED.value,
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      apiToView: (json) {
        SertifikasiModel model = SertifikasiModel.fromJson(json);

        //mahascomponent
        jenisSertifikasiCon.value = model.idJenissertifikasi;
        namaSertifikasiCon.value = model.nama;
        noSertifikasiCon.value = model.nosertifikasi;
        tanggalCon.value = model.tanggal;
        berlakuSampaiCon.value = model.berlakusampai;
        berkasPendukungCon.value = model.berkaspendukung;

        //display non mahas
        namaHRD.value = model.pegawaiapprovalnama ?? "";
        alasanTolakHRD.value = model.alasantolak ?? "";
        namaPegawaiRequest.value = model.pegawainama ?? "";
        statusApprovalHRD = model.approved;
        tglPermintaan.value = model.tanggalrequest ?? DateTime.now();
        idPegawaiSertifikasi.value = model.id ?? 0;
        reqDelete = model.status == "DELETE" ? true : false;
        status.value = model.status!;

        //cek approval for allowedit or delete
        cekApproval();

        //approval
        if (approvalFromNotif.value && statusApprovalHRD == null) {
          approvalButtonsVisible.value = true;
        }
      },
      onBeforeSubmit: () {
        if (!jenisSertifikasiCon.isValid) return false;
        if (!namaSertifikasiCon.isValid) return false;
        if (!noSertifikasiCon.isValid) return false;
        if (!tanggalCon.isValid) return false;
        if (!berlakuSampaiCon.isValid) return false;
        if (!berkasPendukungCon.isValid) return false;

        return true;
      },
      onInit: () async {
        await getSertifikasi();
      },
    );

    super.onInit();
  }

  Future<void> getSertifikasi() async {
    if (EasyLoading.isShow) {
      EasyLoading.dismiss();
    }
    EasyLoading.show();
    var r = await HttpApi.get("/api/PegawaiSertifikasi/List/JenisSertifikasi");
    if (r.success) {
      RxList<JenisSertifikasiModel> listModel = RxList<JenisSertifikasiModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(JenisSertifikasiModel.fromDynamic(e));
      }
      if (jenisSertifikasiCon.items.isNotEmpty) {
        jenisSertifikasiCon.items.clear();
      }
      if (listModel.isNotEmpty && jenisSertifikasiCon.items.isEmpty) {
        jenisSertifikasiCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      }
    }
    EasyLoading.dismiss();
  }

  //backAction
  Future<bool> _back() async {
    Get.until(
        (route) => route.settings.name == Routes.PROFILE_JENIS_SERTIFIKASI);
    Get.toNamed(Routes.PROFILE_JENIS_SERTIFIKASI_HISTORY);
    return true;
  }

  //cek approval parameters come from notifikasi
  void cekApproval() {
    if (approvalFromNotif.value || statusApprovalHRD != null) {
      allowED.value = false;
    } else {
      allowED.value = true;
    }
    if (withActionBack) {
      formCon.backAction = () {
        return _back();
      };
      formCon.deleteOnTap = (id) {
        return _back();
      };
    }
    formCon.setState(() {
      reqDelete ? formCon.allowEdit = false : formCon.allowEdit = allowED.value;
      formCon.allowDelete = allowED.value;
      if (reqDelete) {
        formCon.deleteCaption = "Batal Hapus";
        formCon.deleteDescription =
            "Yakin akan membatalkan permintaan hapus data ini?";
      }
    });
  }

  Future<void> approvalAction(bool approve) async {
    if (EasyLoading.isShow) {
      EasyLoading.dismiss();
    }
    EasyLoading.show();
    String id = Get.parameters['id'].toString();
    final body = {
      'approve': approve,
      'alasanTolak': alasanTolakCon.value,
    };
    ApiResultModel? r;
    String url = '/api/PegawaiSertifikasi/History/Approve/$id';
    r = await HttpApi.put(
      url,
      body: body,
    );
    if (r.success) {
      r = await HttpApi.get(
        '/api/PegawaiSertifikasi/History/$id',
      );
      if (r.success) {
        SertifikasiModel model = SertifikasiModel.fromJson(r.body);
        statusApprovalHRD = model.approved;
        alasanTolakHRD.value = model.alasantolak ?? "";
        approvalButtonsVisible.value = false;
        update();
      }
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }
}
