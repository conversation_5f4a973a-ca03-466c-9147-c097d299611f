import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:hr_portal/app/constant/environment_constant.dart';
import 'package:hr_portal/app/mahas/components/mahas_themes.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:hr_portal/app/mahas_complement/home_menu_button.dart';
import '../../../mahas/mahas_colors.dart';
import '../../../models/profile_model.dart';
import '../controllers/home_controller.dart';

class HomeView extends GetView<HomeController> {
  const HomeView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: controller.onRefresh,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Container(
            color: MahasColors.primary,
            height: MediaQuery.of(context).size.height,
            child: Safe<PERSON><PERSON>(
              child: Container(
                color: Colors.white,
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 13, horizontal: 10),
                      color: MahasColors.primary,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                MahasConfig.appName,
                                style: MahasThemes.muted.copyWith(
                                    color: Colors.white.withValues(alpha: .5)),
                              ),
                              const Padding(padding: EdgeInsets.all(3)),
                              Text(
                                "${MahasConfig.packageInfo!.version}+${MahasConfig.packageInfo!.buildNumber}",
                                style: MahasThemes.muted.copyWith(
                                    color: Colors.white.withValues(alpha: .5)),
                              ),
                              Expanded(child: Container()),
                              InkWell(
                                onTap: () {
                                  controller.goToNotifikasi();
                                },
                                child: SizedBox(
                                  width: 50,
                                  height: 35,
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Obx(
                                        () => Stack(
                                          children: [
                                            const Icon(
                                              FontAwesomeIcons.solidBell,
                                              color: Colors.white,
                                              size: 14,
                                            ),
                                            Visibility(
                                              visible:
                                                  controller.notifikasi.value,
                                              child: const Icon(
                                                Icons.circle,
                                                size: 7,
                                                color: Colors.red,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const Padding(padding: EdgeInsets.all(5)),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  InkWell(
                                    onTap: () {
                                      controller.dialogFoto();
                                    },
                                    child: CircleAvatar(
                                      radius: 30,
                                      backgroundColor: Colors.white,
                                      child: CircleAvatar(
                                        backgroundColor: Colors.white,
                                        radius: 28,
                                        child: Obx(
                                          () => ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(50),
                                            child: controller.images.value !=
                                                    null
                                                ? Image.memory(
                                                    controller.images.value!,
                                                    width: 100,
                                                    height: 100,
                                                    fit: BoxFit.cover,
                                                  )
                                                : Image.asset(
                                                    EnvironmentConstant
                                                        .imageLogo,
                                                  ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  const Padding(padding: EdgeInsets.all(5)),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Obx(
                                        () => Text(
                                          controller.profile.value!.nama ??
                                              "Nama Pegawai : -",
                                          maxLines: 2,
                                          style: const TextStyle(
                                              fontSize: 16,
                                              color: Colors.white),
                                        ),
                                      ),
                                      const Padding(
                                          padding: EdgeInsets.all(2.5)),
                                      Text(
                                        controller.profile.value!.email ??
                                            "Email : -",
                                        style: MahasThemes.muted.copyWith(
                                            color: Colors.white
                                                .withValues(alpha: .5)),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              const Padding(padding: EdgeInsets.all(5)),
                              Row(
                                children: [
                                  Text(
                                    controller.profile.value!.statusPegawai!,
                                    style: MahasThemes.muted.copyWith(
                                      color: Colors.white.withValues(alpha: .5),
                                    ),
                                  ),
                                  const SizedBox(
                                    width: 20,
                                    child: Text(
                                      "-",
                                      textAlign: TextAlign.center,
                                      style: TextStyle(color: Colors.white),
                                    ),
                                  ),
                                  Obx(
                                    () => DropdownButton<ProfilDivisiModel>(
                                      dropdownColor: MahasColors.primary,
                                      value: controller.selectedDrowpdown.value,
                                      icon: const Icon(
                                        Icons.arrow_drop_down,
                                        color: Colors.white,
                                      ),
                                      underline: Container(
                                        color: Colors.transparent,
                                      ),
                                      onChanged: controller.divisionChanged,
                                      items: controller.dropdown.map((e) {
                                        return DropdownMenuItem(
                                          value: e,
                                          child: Container(
                                            constraints: BoxConstraints(
                                                maxWidth: MediaQuery.of(context)
                                                        .size
                                                        .width *
                                                    0.7),
                                            child: Column(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Text(
                                                  e.divisi!,
                                                  style: const TextStyle(
                                                      color: Colors.white),
                                                  textAlign: TextAlign.left,
                                                ),
                                              ],
                                            ),
                                          ),
                                        );
                                      }).toList(),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Stack(
                      children: [
                        Container(
                          color: MahasColors.primary,
                          height: 50,
                        ),
                        Positioned(
                          child: Container(
                            padding: const EdgeInsets.fromLTRB(10, 5, 10, 20),
                            child: Container(
                              padding: const EdgeInsets.all(20),
                              height: 100,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.all(
                                    Radius.circular(MahasThemes.borderRadius)),
                                boxShadow: [
                                  BoxShadow(
                                    color: MahasColors.primary
                                        .withValues(alpha: 0.5),
                                    blurRadius: 5,
                                  ),
                                ],
                              ),
                              child: Row(
                                children: [
                                  ButtonMenuNav(
                                    icon: FontAwesomeIcons.solidUser,
                                    subtitle: "Profile",
                                    onTap: () {
                                      controller.goToProfile();
                                    },
                                  ),
                                  Container(
                                    width: 1,
                                    color: MahasColors.primary
                                        .withValues(alpha: .2),
                                  ),
                                  ButtonMenuNav(
                                    icon: FontAwesomeIcons.locationDot,
                                    subtitle: MahasConfig
                                            .dinamisForm.absensi?.title ??
                                        "Absensi",
                                    onTap: () {
                                      controller.absenOnTab();
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.fromLTRB(11, 0, 11, 20),
                        child: Column(
                          children: [
                            Expanded(
                              child: Row(
                                children: [
                                  HomeMenuButton(
                                    icon: FontAwesomeIcons.calendar,
                                    label: "Jadwal",
                                    onTap: () {
                                      controller.goToJadwa();
                                    },
                                  ),
                                  const Padding(padding: EdgeInsets.all(7)),
                                  HomeMenuButton(
                                    icon: FontAwesomeIcons.clipboardList,
                                    label: "E-Ticket",
                                    onTap: () {
                                      controller.goToETicket();
                                    },
                                  )
                                ],
                              ),
                            ),
                            const Padding(padding: EdgeInsets.all(7)),
                            Expanded(
                              child: Row(
                                children: [
                                  HomeMenuButton(
                                    icon: FontAwesomeIcons.calendarCheck,
                                    label: "Permintaan Jadwal",
                                    onTap: () {
                                      controller.goToPermintaanJadwal();
                                    },
                                  ),
                                  const Padding(padding: EdgeInsets.all(7)),
                                  HomeMenuButton(
                                    icon: Icons.info_outlined,
                                    label: "Informasi",
                                    onTap: () {
                                      controller.goToInformasi();
                                    },
                                  )
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class ButtonMenuNav extends StatelessWidget {
  final IconData icon;
  final GestureTapCallback? onTap;
  final String subtitle;

  const ButtonMenuNav({
    super.key,
    required this.icon,
    required this.subtitle,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        child: Column(
          children: [
            Icon(
              icon,
              color: MahasColors.primary,
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              subtitle,
              style: TextStyle(
                color: MahasColors.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
