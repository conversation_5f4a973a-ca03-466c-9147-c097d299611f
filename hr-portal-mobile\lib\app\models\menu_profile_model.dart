import 'dart:convert';

class ProfileMenuModel {
  bool str;
  bool kontak;
  bool kesehatan;
  bool dokumen;
  bool keluarga;
  bool pendidikan;
  bool rekening;
  bool pengalaman;
  bool seragam;
  bool sertifikasi;
  bool kontrak;
  bool hobi;
  bool kgb;
  bool pangkat;
  bool absenwithfoto;
  bool absentugaswithfoto;
  bool customLemburPage;
  bool quotaCutiTukarLembur;
  bool quotaLemburGantiUang;
  bool perpus;
  bool serahTerima;
  bool approvalDinamis;
  bool slipGajiStatis;
  bool kategoriBios;
  bool absen;
  bool absensiSaya;
  bool absensiDivisi;
  bool slipGaji;
  bool absensiIstirahat;
  bool downloadSlipGaji;
  bool jenisIzin;
  bool loginEmail;
  bool cutiDayPayment;
  bool cutiUnpaidLeave;
  bool cutiSetengahHari;
  bool cutiTukarLembur;
  bool lembur;
  bool tukarJadwal;
  bool absensiQr;

  ProfileMenuModel({
    required this.str,
    required this.kontak,
    required this.kesehatan,
    required this.dokumen,
    required this.keluarga,
    required this.pendidikan,
    required this.rekening,
    required this.pengalaman,
    required this.seragam,
    required this.sertifikasi,
    required this.kontrak,
    required this.hobi,
    required this.kgb,
    required this.pangkat,
    required this.absenwithfoto,
    required this.absentugaswithfoto,
    required this.customLemburPage,
    required this.quotaCutiTukarLembur,
    required this.quotaLemburGantiUang,
    required this.perpus,
    required this.serahTerima,
    required this.approvalDinamis,
    required this.slipGajiStatis,
    required this.kategoriBios,
    required this.absen,
    required this.absensiSaya,
    required this.absensiDivisi,
    required this.slipGaji,
    required this.absensiIstirahat,
    required this.downloadSlipGaji,
    required this.jenisIzin,
    required this.loginEmail,
    required this.cutiDayPayment,
    required this.cutiUnpaidLeave,
    required this.cutiSetengahHari,
    required this.cutiTukarLembur,
    required this.lembur,
    required this.tukarJadwal,
    required this.absensiQr,
  });

  static ProfileMenuModel fromString(String jsonString) {
    final data = json.decode(jsonString);
    return ProfileMenuModel.fromDynamic(data);
  }

  factory ProfileMenuModel.fromDynamic(dynamic json) {
    return ProfileMenuModel(
      str: json['str'] ?? false,
      kontak: json['kontak'] ?? false,
      kesehatan: json['kesehatan'] ?? false,
      dokumen: json['dokumen'] ?? false,
      keluarga: json['keluarga'] ?? false,
      pendidikan: json['pendidikan'] ?? false,
      rekening: json['rekening'] ?? false,
      pengalaman: json['pengalaman'] ?? false,
      seragam: json['seragam'] ?? false,
      sertifikasi: json['sertifikasi'] ?? false,
      kontrak: json['kontrak'] ?? false,
      hobi: json['hobi'] ?? false,
      kgb: json['kgb'] ?? false,
      pangkat: json['pangkat'] ?? false,
      absenwithfoto: json['absenwithfoto'] ?? false,
      absentugaswithfoto: json['absentugaswithfoto'] ?? false,
      customLemburPage: json['customLemburPage'] ?? false,
      quotaCutiTukarLembur: json['quotaCutiTukarLembur'] ?? false,
      quotaLemburGantiUang: json['quotaLemburGantiUang'] ?? false,
      perpus: json['perpus'] ?? false,
      serahTerima: json['serahTerima'] ?? false,
      approvalDinamis: json['approvalDinamis'] ?? false,
      slipGajiStatis: json['slipGajiStatis'] ?? false,
      kategoriBios: json['kategoriBios'] ?? false,
      absen: json['absen'] ?? true,
      absensiSaya: json['absensiSaya'] ?? true,
      absensiDivisi: json['absensiDivisi'] ?? true,
      slipGaji: json['slipGaji'] ?? true,
      absensiIstirahat: json['absensiIstirahat'] ?? false,
      downloadSlipGaji: json['downloadSlipGaji'] ?? false,
      jenisIzin: json['jenisIzin'] ?? false,
      loginEmail: json['loginEmail'] ?? false,
      cutiDayPayment: json['cutiDayPayment'] ?? false,
      cutiUnpaidLeave: json['cutiUnpaidLeave'] ?? false,
      cutiSetengahHari: json['cutiSetengahHari'] ?? false,
      cutiTukarLembur: json['cutiTukarLembur'] ?? false,
      lembur: json['lembur'] ?? false,
      tukarJadwal: json['tukarJadwal'] ?? false,
      absensiQr: json['absensiQr'] ?? false,
    );
  }

  factory ProfileMenuModel.fromJson(Map<String, dynamic> json) {
    return ProfileMenuModel(
      str: json['str'] ?? false,
      kontak: json['kontak'] ?? false,
      kesehatan: json['kesehatan'] ?? false,
      dokumen: json['dokumen'] ?? false,
      keluarga: json['keluarga'] ?? false,
      pendidikan: json['pendidikan'] ?? false,
      rekening: json['rekening'] ?? false,
      pengalaman: json['pengalaman'] ?? false,
      seragam: json['seragam'] ?? false,
      sertifikasi: json['sertifikasi'] ?? false,
      kontrak: json['kontrak'] ?? false,
      hobi: json['hobi'] ?? false,
      kgb: json['kgb'] ?? false,
      pangkat: json['pangkat'] ?? false,
      absenwithfoto: json['absenwithfoto'] ?? false,
      absentugaswithfoto: json['absentugaswithfoto'] ?? false,
      customLemburPage: json['customLemburPage'] ?? false,
      quotaCutiTukarLembur: json['quotaCutiTukarLembur'] ?? false,
      quotaLemburGantiUang: json['quotaLemburGantiUang'] ?? false,
      perpus: json['perpus'] ?? false,
      serahTerima: json['serahTerima'] ?? false,
      approvalDinamis: json['approvalDinamis'] ?? false,
      slipGajiStatis: json['slipGajiStatis'] ?? false,
      kategoriBios: json['kategoriBios'] ?? false,
      absen: json['absen'] ?? true,
      absensiSaya: json['absensiSaya'] ?? true,
      absensiDivisi: json['absensiDivisi'] ?? true,
      slipGaji: json['slipGaji'] ?? true,
      absensiIstirahat: json['absensiIstirahat'] ?? false,
      downloadSlipGaji: json['downloadSlipGaji'] ?? false,
      jenisIzin: json['jenisIzin'] ?? false,
      loginEmail: json['loginEmail'] ?? false,
      cutiDayPayment: json['cutiDayPayment'] ?? false,
      cutiUnpaidLeave: json['cutiUnpaidLeave'] ?? false,
      cutiSetengahHari: json['cutiSetengahHari'] ?? false,
      cutiTukarLembur: json['cutiTukarLembur'] ?? false,
      lembur: json['lembur'] ?? false,
      tukarJadwal: json['tukarJadwal'] ?? false,
      absensiQr: json['absensiQr'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'str': str,
      'kontak': kontak,
      'kesehatan': kesehatan,
      'dokumen': dokumen,
      'keluarga': keluarga,
      'pendidikan': pendidikan,
      'rekening': rekening,
      'pengalaman': pengalaman,
      'seragam': seragam,
      'sertifikasi': sertifikasi,
      'kontrak': kontrak,
      'hobi': hobi,
      'kgb': kgb,
      'pangkat': pangkat,
      'absenwithfoto': absenwithfoto,
      'absentugaswithfoto': absentugaswithfoto,
      'quotaCutiTukarLembur': quotaCutiTukarLembur,
      'quotaLemburGantiUang': quotaLemburGantiUang,
      'perpus': perpus,
      'serahTerima': serahTerima,
      'approvalDinamis': approvalDinamis,
      'slipGajiStatis': slipGajiStatis,
      'kategoriBios': kategoriBios,
      'absen': absen,
      'absensiSaya': absensiSaya,
      'absensiDivisi': absensiDivisi,
      'slipGaji': slipGaji,
      'absensiIstirahat': absensiIstirahat,
      'downloadSlipGaji': downloadSlipGaji,
      'jenisIzin': jenisIzin,
      'loginEmail': loginEmail,
      'cutiDayPayment': cutiDayPayment,
      'cutiUnpaidLeave': cutiUnpaidLeave,
      'cutiSetengahHari': cutiSetengahHari,
      'cutiTukarLembur': cutiTukarLembur,
      'lembur': lembur,
      'tukarJadwal': tukarJadwal,
      'absensiQr': absensiQr,
    };
  }

  static ProfileMenuModel getEmpty() {
    return ProfileMenuModel(
      str: false,
      kontak: false,
      kesehatan: false,
      dokumen: false,
      keluarga: false,
      pendidikan: false,
      rekening: false,
      pengalaman: false,
      seragam: false,
      sertifikasi: false,
      kontrak: false,
      hobi: false,
      kgb: false,
      pangkat: false,
      absenwithfoto: false,
      absentugaswithfoto: false,
      customLemburPage: false,
      quotaCutiTukarLembur: false,
      quotaLemburGantiUang: false,
      perpus: false,
      serahTerima: false,
      approvalDinamis: false,
      slipGajiStatis: false,
      kategoriBios: false,
      absen: true,
      absensiSaya: true,
      absensiDivisi: true,
      slipGaji: true,
      absensiIstirahat: false,
      downloadSlipGaji: false,
      jenisIzin: false,
      loginEmail: false,
      cutiDayPayment: false,
      cutiUnpaidLeave: false,
      cutiSetengahHari: false,
      cutiTukarLembur: false,
      lembur: false,
      tukarJadwal: false,
      absensiQr: false,
    );
  }
}
