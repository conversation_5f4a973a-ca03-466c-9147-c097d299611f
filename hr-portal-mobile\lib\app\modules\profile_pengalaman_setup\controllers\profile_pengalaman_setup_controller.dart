import 'dart:convert';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../models/profile_pengalaman_model.dart';

class ProfilePengalamanSetupController extends GetxController {
  late SetupPageController formCon;

  final pengalaman = InputTextController();
  final dariTanggal = InputDatetimeController();
  final sampaiTanggal = InputDatetimeController();
  final keterangan = InputTextController(
    type: InputTextType.paragraf,
  );

  @override
  void onInit() {
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PegawaiPengalaman/$id',
      urlApiPost: () => '/api/PegawaiPengalaman',
      urlApiPut: (id) => '/api/PegawaiPengalaman/$id',
      urlApiDelete: (id) => '/api/PegawaiPengalaman/$id',
      bodyApi: (id) => {
        'id_Pegawai': MahasConfig.profile!.id,
        'nama': pengalaman.value,
        'daritanggal': MahasFormat.dateToString(dariTanggal.value),
        'sampaitanggal': MahasFormat.dateToString(sampaiTanggal.value),
        'keterangan': keterangan.value,
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (!pengalaman.isValid) return false;
        if (!dariTanggal.isValid) return false;
        if (!sampaiTanggal.isValid) return false;
        if (!keterangan.isValid) return false;
        return true;
      },
      apiToView: (json) {
        PengalamanModel model = PengalamanModel.fromJson(json);
        pengalaman.value = model.nama;
        keterangan.value = model.keterangan;
        dariTanggal.value = model.daritanggal;
        sampaiTanggal.value = model.sampaitanggal;
      },
    );

    super.onInit();
  }
}
