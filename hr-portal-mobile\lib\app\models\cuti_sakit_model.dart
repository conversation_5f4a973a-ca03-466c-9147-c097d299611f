import 'dart:convert';

import 'package:hr_portal/app/mahas/services/mahas_format.dart';

class CutiSakitModel {
  int? id;
  int? idDivisi;
  DateTime? tanggalInput;
  bool? approveKadiv;
  bool? approveManager;
  DateTime? dariTanggal;
  DateTime? sampaiTanggal;
  int? berapaHari;
  String? alasanTolak;
  String? alasan;
  int? idPegawaiRequest;
  String? keterangan;
  bool? rawatInap;
  String? pathFoto;
  String? divisi;
  String? pegawaiRequest;
  String? pegawaiKadiv;
  String? pegawaiManager;
  int? idPegawaiKadiv;
  int? idPegawaiManager;

  CutiSakitModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static CutiSakitModel fromDynamic(dynamic dynamicData) {
    final model = CutiSakitModel();

    model.id = dynamicData['id'];
    model.idDivisi = dynamicData['id_Divisi'];
    model.tanggalInput =
        MahasFormat.stringToDateTime(dynamicData['tanggalInput']);
    model.approveKadiv = dynamicData['approveKadiv'];
    model.approveManager = dynamicData['approveManager'];
    model.dariTanggal =
        MahasFormat.stringToDateTime(dynamicData['dariTanggal']);
    model.sampaiTanggal =
        MahasFormat.stringToDateTime(dynamicData['sampaiTanggal']);
    model.berapaHari = dynamicData['berapaHari'];
    model.alasanTolak = dynamicData['alasanTolak'];
    model.alasan = dynamicData['alasan'];
    model.keterangan = dynamicData['keterangan'];
    model.rawatInap = MahasFormat.dynamicToBool(dynamicData['rawatInap']);
    model.idPegawaiRequest = dynamicData['id_Pegawai_Request'];
    model.divisi = dynamicData['divisi'];
    model.pathFoto = dynamicData['pathFoto'];
    model.pegawaiRequest = dynamicData['pegawaiRequest'];
    model.pegawaiKadiv = dynamicData['pegawaiKadiv'];
    model.pegawaiManager = dynamicData['pegawaiManager'];
    model.idPegawaiKadiv = dynamicData['id_Pegawai_Kadiv'];
    model.idPegawaiManager = dynamicData['id_Pegawai_Manager'];

    return model;
  }
}
