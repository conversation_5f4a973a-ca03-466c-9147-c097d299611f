import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class EticketModel {
  int? id;
  DateTime? tanggal;
  DateTime? tanggalselesai;
  DateTime? tanggalbatal;
  String? no;
  String? judul;
  String? deskripsi;
  int? idDivisi;
  int? idPegawai;
  int? idPegawaiSelesai;
  int? idPegawaiBatal;
  bool? selesai;
  bool? batal;
  String? divisi;
  String? pegawai;
  String? pegawaiselesai;
  String? pegawaibatal;
  String? feedback;
  List<EticketDetailpegawaiModel>? detailpegawai;

  EticketModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static EticketModel fromDynamic(dynamic dynamicData) {
    final model = EticketModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.tanggal = MahasFormat.dynamicToDateTime(dynamicData['tanggal']);
    model.tanggalselesai =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalSelesai']);
    model.tanggalbatal =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalBatal']);
    model.no = dynamicData['no'];
    model.judul = dynamicData['judul'];
    model.deskripsi = dynamicData['deskripsi'];
    model.feedback = dynamicData['feedback'];
    model.idDivisi = MahasFormat.dynamicToInt(dynamicData['id_Divisi']);
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.idPegawaiSelesai =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Selesai']);
    model.idPegawaiBatal =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Batal']);
    model.selesai = MahasFormat.dynamicToBool(dynamicData['selesai']);
    model.batal = MahasFormat.dynamicToBool(dynamicData['batal']);
    model.divisi = dynamicData['divisi'];
    model.pegawai = dynamicData['pegawai'];
    model.pegawaiselesai = dynamicData['pegawaiSelesai'];
    model.pegawaibatal = dynamicData['pegawaiBatal'];
    if (dynamicData['detailPegawai'] != null) {
      final detailT = dynamicData['detailPegawai'] as List;
      model.detailpegawai = [];
      for (var i = 0; i < detailT.length; i++) {
        model.detailpegawai!
            .add(EticketDetailpegawaiModel.fromDynamic(detailT[i]));
      }
    }

    return model;
  }
}

class EticketDetailpegawaiModel {
  int? idPegawai;
  String? pegawai;

  EticketDetailpegawaiModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static EticketDetailpegawaiModel fromDynamic(dynamic dynamicData) {
    final model = EticketDetailpegawaiModel();

    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.pegawai = dynamicData['pegawai'];

    return model;
  }
}

class LookupEtiketPegawaiModel {
  int? id;
  String? nama;

  LookupEtiketPegawaiModel();
  LookupEtiketPegawaiModel.init(this.id, this.nama);

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static LookupEtiketPegawaiModel fromDynamic(dynamic dynamicData) {
    final model = LookupEtiketPegawaiModel();

    model.id = dynamicData['id'];
    model.nama = dynamicData['nama'];

    return model;
  }
}
