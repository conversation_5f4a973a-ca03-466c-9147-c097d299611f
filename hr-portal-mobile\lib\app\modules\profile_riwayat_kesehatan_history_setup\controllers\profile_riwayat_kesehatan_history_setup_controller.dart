import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/models/api_result_model.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../models/profile_riwayat_kesehatan_model.dart';
import '../../../routes/app_pages.dart';

class ProfileRiwayatKesehatanHistorySetupController extends GetxController {
  late SetupPageController formCon;
  late bool reqDelete;
  late bool showStatus;
  late bool withActionBack;

  final fileCon = InputFileController(
    type: FileType.custom,
    extension: ['pdf'],
    tipe: InputFileType.pdf,
  );
  final nomorCon = InputTextController();
  final tanggalCon = InputDatetimeController();
  final alasanTolakCon = InputTextController();

  RxBool approvalButtonsVisible = false.obs;
  RxBool approvalFromNotif = false.obs;
  RxBool allowED = false.obs;
  late dynamic statusApprovalHRD = null.obs;
  RxString status = "".obs;
  RxString namaHRD = "".obs;
  RxString namaPegawaiRequest = "".obs;
  RxString alasanTolakHRD = "".obs;
  Rx<DateTime> tglPermintaan = DateTime.now().obs;
  RxInt idPegawaiRiwayatKesehatan = 0.obs;

  @override
  void onInit() async {
    //init
    String notifParam = Get.parameters['approval'].toString();
    approvalFromNotif.value = notifParam == "true" ? true : false;
    String showStatusParam = Get.parameters['showStatus'].toString();
    showStatus = showStatusParam == "true" ? true : false;
    String withActionBackParam = Get.parameters['withActionBack'].toString();
    withActionBack = withActionBackParam == "true" ? true : false;

    //formcon
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PegawaiRiwayatKesehatan/History/$id',
      urlApiPost: () => '/api/PegawaiRiwayatKesehatan/History',
      urlApiPut: (id) => '/api/PegawaiRiwayatKesehatan/History/$id',
      urlApiDelete: (id) =>
          '/api/PegawaiRiwayatKesehatan/History?id=$id&idDivisi=${MahasConfig.selectedDivisi}',
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "nomor": nomorCon.value,
        "tanggal": MahasFormat.dateToString(tanggalCon.value),
        "berkasPendukung": fileCon.value,
        "id_PegawaiRiwayatKesehatan": idPegawaiRiwayatKesehatan.value,
      },
      allowDelete: allowED.value,
      allowEdit: allowED.value,
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      apiToView: (json) {
        RiwayatkesehatanModel model = RiwayatkesehatanModel.fromJson(json);

        //mahascomponent
        nomorCon.value = model.nomor;
        tanggalCon.value = model.tanggal;
        fileCon.value = model.berkaspendukung;

        //display non mahas
        namaHRD.value = model.pegawaiapprovalnama ?? "";
        alasanTolakHRD.value = model.alasanTolak ?? "";
        namaPegawaiRequest.value = model.pegawainama ?? "";
        statusApprovalHRD = model.approved;
        tglPermintaan.value = model.tanggalRequest ?? DateTime.now();
        idPegawaiRiwayatKesehatan.value = model.id ?? 0;
        reqDelete = model.status == "DELETE" ? true : false;
        status.value = model.status!;

        //cek approval for allowedit or delete
        cekApproval();

        //approval
        if (approvalFromNotif.value && statusApprovalHRD == null) {
          approvalButtonsVisible.value = true;
        }
      },
      onBeforeSubmit: () {
        if (!nomorCon.isValid) return false;
        if (!tanggalCon.isValid) return false;
        if (!fileCon.isValid) return false;
        return true;
      },
    );

    super.onInit();
  }

  //backAction
  Future<bool> _back() async {
    Get.until(
        (route) => route.settings.name == Routes.PROFILE_RIWAYAT_KESEHATAN);
    Get.toNamed(Routes.PROFILE_RIWAYAT_KESEHATAN_HISTORY);
    return true;
  }

  //cek approval parameters come from notifikasi
  void cekApproval() {
    if (approvalFromNotif.value || statusApprovalHRD != null) {
      allowED.value = false;
    } else {
      allowED.value = true;
    }
    if (withActionBack) {
      formCon.backAction = () {
        return _back();
      };
      formCon.deleteOnTap = (id) {
        return _back();
      };
    }
    formCon.setState(() {
      reqDelete ? formCon.allowEdit = false : formCon.allowEdit = allowED.value;
      formCon.allowDelete = allowED.value;
      if (reqDelete) {
        formCon.deleteCaption = "Batal Hapus";
        formCon.deleteDescription =
            "Yakin akan membatalkan permintaan hapus data ini?";
      }
    });
  }

  Future<void> approvalAction(bool approve) async {
    if (EasyLoading.isShow) {
      EasyLoading.dismiss();
    }
    EasyLoading.show();
    String id = Get.parameters['id'].toString();
    final body = {
      'approve': approve,
      'alasanTolak': alasanTolakCon.value,
    };
    ApiResultModel? r;
    String url = '/api/PegawaiRiwayatKesehatan/History/Approve/$id';
    r = await HttpApi.put(
      url,
      body: body,
    );
    if (r.success) {
      r = await HttpApi.get(
        '/api/PegawaiRiwayatKesehatan/History/$id',
      );
      if (r.success) {
        RiwayatkesehatanModel model = RiwayatkesehatanModel.fromJson(r.body);
        statusApprovalHRD = model.approved;
        alasanTolakHRD.value = model.alasanTolak ?? "";
        approvalButtonsVisible.value = false;
        update();
      }
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }
}
