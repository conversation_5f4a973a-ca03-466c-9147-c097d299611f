import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/models/cuti_tahunan_model.dart';
import 'package:intl/intl.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/models/api_result_model.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../models/cuti_setengah_hari.dart';
import '../../../models/pegawai_approval.dart';

class PermintaanJadwalCutiSetengahHariSetupController extends GetxController {
  late SetupPageController formCon;
  final alasanCon = InputTextController(type: InputTextType.paragraf);
  final jenisCon = InputDropdownController(items: [
    DropdownItem.init("Meninggalkan Pekerjaan", "Meninggalkan Pekerjaan"),
    DropdownItem.init("Telat Datang", "Telat Datang"),
  ]);
  final tanggalCutiCon = InputDatetimeController();
  final jamCon = InputDatetimeController();

  final tanggalMulaiCon = InputDatetimeController();
  final tanggalSelesaiCon = InputDatetimeController();
  final jumlahCutiCon = InputTextController(type: InputTextType.number);
  final sisaCutiCon = InputTextController(type: InputTextType.number);
  final tanggalSisaSelesaiCon = InputDatetimeController();
  final totalCutiCon = InputTextController(type: InputTextType.number);

  final pegawaiCutiCon = InputTextController();
  final alasanTolakCon = InputTextController();
  final String nama = MahasConfig.profile!.nama!;
  late String approve;
  late bool allowED;
  Rxn<bool> accKadiv = Rxn<bool>();
  Rxn<bool> accManager = Rxn<bool>();
  RxBool isVisible = false.obs;
  late RxString pegawaiKadiv = ''.obs;
  late RxString pegawaiManager = ''.obs;
  late String kadiv;
  late bool isKadiv;
  final isDinamisApprove = MahasConfig.profileMenu.approvalDinamis;
  RxBool isStop = false.obs;

  RxBool batal = false.obs;
  RxList<PegawaiApprovalModel> modelApproval = <PegawaiApprovalModel>[].obs;
  int? id;
  RxString statusTolak = ''.obs;
  RxInt idPegawaiRequest = 0.obs;
  RxString tglPengajuan = ''.obs;

  @override
  void onInit() {
    cekApproval();
    cekKadiv();
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PermintaanJadwal/CutiSetengahHari/$id',
      urlApiPost: () => '/api/PermintaanJadwal/CutiSetengahHari',
      urlApiPut: (id) => '/api/PermintaanJadwal/CutiSetengahHari/$id',
      urlApiDelete: (id) => '/api/PermintaanJadwal/CutiSetengahHari/$id',
      allowDelete: allowED,
      allowEdit: allowED,
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "tanggalJam":
            "${MahasFormat.dateTimeOfDayToString(tanggalCutiCon.value, jamCon.value)}",
        "keterangan": jenisCon.value,
        "alasan": alasanCon.value,
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (jumlahCutiCon.value == 0 &&
            MahasConfig.dinamisForm.cutiTahunan?.quotaVisible == true) {
          Helper.dialogWarning("Quota Cuti Anda Sudah Habis");
          return false;
        }
        DateTime tanggalSekarang = DateTime.now();
        DateTime tanggalCuti = DateTime(tanggalCutiCon.value.year,
            tanggalCutiCon.value.month, tanggalCutiCon.value.day);
        if (tanggalCuti.difference(tanggalSekarang).inDays < -1) {
          Helper.dialogWarning("Pengajuan Cuti maksimal H+1 dari tanggal cuti");
          return false;
        }
        if (!alasanCon.isValid) return false;
        return true;
      },
      successMessage: Helper.succesPengajuanMessage(),
      apiToView: (json) {
        CutiSetengahHariModel model = CutiSetengahHariModel.fromJson(json);
        id = model.id;
        alasanCon.value = model.alasan;
        tanggalCutiCon.value = model.tanggaljam;
        jenisCon.value = model.keterangan;
        jamCon.value = TimeOfDay.fromDateTime(model.tanggaljam!);

        var format = DateFormat("dd MMMM yyyy HH:mm");
        tglPengajuan.value = format.format(model.tanggalinput!);

        accManager.value = model.approvemanager;
        accKadiv.value = model.approvekadiv;
        pegawaiKadiv.value = model.pegawaikadiv!;
        pegawaiManager.value = model.pegawaimanager!;

        pegawaiCutiCon.value = model.pegawairequest;
        statusTolak.value = model.alasantolak ?? '';
        idPegawaiRequest.value = model.idPegawaiRequest!;

        // approval
        if (isDinamisApprove == false && allowED == false) {
          if ((model.approvekadiv == null &&
                  model.idPegawaiKadiv == MahasConfig.profile!.id) ||
              (model.approvekadiv == true &&
                  model.approvemanager == null &&
                  model.idPegawaiManager == MahasConfig.profile!.id)) {
            isVisible.value = true;
          }
        }

        if (isDinamisApprove == true && allowED == false) {
          getDataApproval();
        }
      },
      onInit: () async {
        if (MahasConfig.dinamisForm.cutiTahunan?.quotaVisible == true &&
            allowED == true) {
          await getQuotaCutiTahunan();
        }
      },
    );
    super.onInit();
  }

  Future<void> getQuotaCutiTahunan() async {
    var url = '/api/PermintaanJadwal/CutiTahunan/QuotaCutiTahunan';
    var r = await HttpApi.get(url);
    if (r.success) {
      var model = QuotaCutiTahunanModel.fromJson(r.body);

      tanggalMulaiCon.value = model.tanggalberlaku;
      tanggalSelesaiCon.value = DateTime(model.tanggalselesai!.year,
          model.tanggalberlaku!.month + 6, model.tanggalberlaku!.day);
      jumlahCutiCon.value = model.jumlahcuti.toString();
      sisaCutiCon.value = model.sisacuti.toString();
      tanggalSisaSelesaiCon.value = model.tanggalsisaselesai;
      totalCutiCon.value = model.totalcuti.toString();
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal mendapatkan data, silahkan cek koneksi internet");
    } else {
      Helper.dialogWarning(r.message);
    }
  }

  void cekApproval() {
    approve = Get.parameters['approval'].toString();
    if (approve == "approval") {
      allowED = false;
    } else {
      allowED = true;
    }
  }

  void cekKadiv() {
    kadiv = Get.parameters['isKadiv'].toString();
    if (kadiv == "false") {
      isKadiv = false;
    } else {
      isKadiv = true;
    }
  }

  Future<void> approvalOnPress(bool approve) async {
    String urlKadiv = '/api/PermintaanJadwal/CutiSetengahHari/Approve/Kadiv/';
    String urlManager =
        '/api/PermintaanJadwal/CutiSetengahHari/Approve/Manager/';
    String urlApprove = '/api/PermintaanJadwal/CutiSetengahHari/Approve/';
    var action = await Helper.approvalAction(
      id.toString(),
      approve,
      alasanTolakCon.value,
      accKadiv.value,
      urlKadiv,
      urlManager,
      isDinamis: isDinamisApprove,
      urlApprove: urlApprove,
    );
    if (action.success) {
      final url = '/api/PermintaanJadwal/CutiSetengahHari/$id';
      ApiResultModel r;
      r = await HttpApi.get(url);
      if (r.success) {
        CutiSetengahHariModel model = CutiSetengahHariModel.fromJson(r.body);
        accKadiv.value = model.approvekadiv;
        accManager.value = model.approvemanager;
        statusTolak.value = model.alasantolak ?? '';
        if (isDinamisApprove) {
          getDataApproval();
        }
        isVisible.value = false;
        update();
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan approve, silahkan cek koneksi internet");
    } else if (!action.success) {
      Helper.dialogWarning(
        "Gagal melakukan approval",
        resMassage: action.message,
        noInternetMassage:
            "Gagal melakukan approval, silahkan cek koneksi internet",
      );
    }
  }

  Future<void> getDataApproval() async {
    EasyLoading.show();
    final url = '/api/PermintaanJadwal/CutiSetengahHari/Approval/$id';
    final r = await HttpApi.get(url);
    modelApproval.clear();
    if (r.success) {
      if (r.body != null) {
        var data = json.decode(r.body);
        for (var e in data['datas']) {
          modelApproval.add(PegawaiApprovalModel.fromDynamic(e));
        }
        bool canApprove = modelApproval.any((item) =>
            item.idPegawaiapprove == MahasConfig.profile?.id &&
            item.approve == null);
        if (canApprove) {
          isVisible.value = true;
        }
        bool notApprove = modelApproval.any((item) => item.approve == false);
        if (notApprove) {
          isVisible.value = false;
        }
        isStop.value = modelApproval.any((item) => item.approve == false);
      } else {
        Helper.dialogWarning(r.message);
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan aksi, silahkan cek koneksi internet");
    } else if (r.statusCode == 404) {
      Helper.dialogWarning("Tidak Ada Data Pegawai Approval");
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }
}
