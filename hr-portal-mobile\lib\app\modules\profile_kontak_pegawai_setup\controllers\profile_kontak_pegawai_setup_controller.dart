import 'dart:convert';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/models/api_list_resut_model.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../models/jenis_kontak_model.dart';
import '../../../models/profile_kontak_model.dart';
import '../../../routes/app_pages.dart';

class ProfileKontakPegawaiSetupController extends GetxController {
  late SetupPageController formCon;
  final jenisKontakCon = InputDropdownController();
  final namaCon = InputTextController();

  RxInt idPegawaiKontak = 0.obs;
  RxString namaLabel = 'Nama'.obs;

  @override
  void onInit() {
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PegawaiJenisKontak/$id',
      urlApiPost: () => '/api/PegawaiJenisKontak',
      urlApiPut: (id) => '/api/PegawaiJenisKontak/$id',
      urlApiDelete: (id) => '/api/PegawaiJenisKontak/$id',
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "id_Kontak": jenisKontakCon.value,
        "nama": namaCon.value,
        "id_PegawaiKontak": idPegawaiKontak.value,
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      apiToView: (json) {
        ProfilekontakModel model = ProfilekontakModel.fromJson(json);

        //mahascomponent
        jenisKontakCon.value = model.idJeniskontak;
        namaCon.value = model.nama;
        namaLabel.value = jenisKontakCon.label ?? "Nama";

        //non mahas
        idPegawaiKontak.value = model.id ?? 0;
      },
      onBeforeSubmit: () {
        if (!jenisKontakCon.isValid) return false;
        if (!namaCon.isValid) return false;
        return true;
      },
      onInit: () async {
        await getKontak();
      },
    );

    if (MahasConfig.dinamisForm.kontak?.hashistory ?? MahasConfig.hasHistory) {
      formCon.onSuccessSubmit = (r) {
        var idHistory = json.decode(r.body)['id'];
        Get.toNamed(
          Routes.PROFILE_KONTAK_PEGAWAI_HISTORY_SETUP,
          parameters: {
            'id': idHistory.toString(),
            'showStatus': true.toString(),
            'withActionBack': true.toString(),
          },
        );
      };
      formCon.deleteOnTap = (id) {
        Get.toNamed(
          Routes.PROFILE_KONTAK_PEGAWAI_HISTORY_SETUP,
          parameters: {
            'id': id.toString(),
            'showStatus': true.toString(),
            'withActionBack': true.toString(),
          },
        );
      };
    }

    super.onInit();
  }

  Future<void> getKontak() async {
    if (EasyLoading.isShow) return;
    EasyLoading.show();
    var r = await HttpApi.get("/api/JenisKontak");
    if (r.success) {
      RxList<JenisKontakModel> listModel = RxList<JenisKontakModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(JenisKontakModel.fromDynamic(e));
      }
      if (jenisKontakCon.items.isNotEmpty) {
        jenisKontakCon.items.clear();
      }
      if (listModel.isNotEmpty && jenisKontakCon.items.isEmpty) {
        jenisKontakCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id, label: e.namalabel),
            )
            .toList();
      }
      jenisKontakCon.onChangedVoid = () {
        getLabel();
      };
    } else {
      Helper.fetchErrorMessage(r);
    }
    EasyLoading.dismiss();
  }

  void getLabel() {
    namaLabel.value = jenisKontakCon.label ?? "Nama";
  }
}
