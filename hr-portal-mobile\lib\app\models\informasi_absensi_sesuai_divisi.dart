import 'dart:convert';

import 'package:flutter/material.dart';

import '../mahas/services/mahas_format.dart';

class InformasiAbsensiKeluarMasukModel {
  List<InformasiabsensiModel>? masuk;
  List<InformasiabsensiModel>? keluar;

  InformasiAbsensiKeluarMasukModel();

  static InformasiAbsensiKeluarMasukModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static InformasiAbsensiKeluarMasukModel fromDynamic(dynamic dynamicData) {
    final model = InformasiAbsensiKeluarMasukModel();

    if (dynamicData['masuk'] != null) {
      final detailT = dynamicData['masuk'] as List;
      model.masuk = [];
      for (var i = 0; i < detailT.length; i++) {
        model.masuk!.add(InformasiabsensiModel.fromDynamic(detailT[i]));
      }
    }
    if (dynamicData['keluar'] != null) {
      final detailT = dynamicData['keluar'] as List;
      model.keluar = [];
      for (var i = 0; i < detailT.length; i++) {
        model.keluar!.add(InformasiabsensiModel.fromDynamic(detailT[i]));
      }
    }

    return model;
  }
}

class InformasiabsensiModel {
  int? id;
  String? lokasiabsensi;
  DateTime? tanggaljam;
  String? status;
  double? latitude;
  double? longitude;
  int? idPegawai;
  String? pegawai;
  int? idShift;
  String? namaShift;
  TimeOfDay? jamMulai;
  TimeOfDay? jamSelesai;
  String? keterangan;

  InformasiabsensiModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static InformasiabsensiModel fromDynamic(dynamic dynamicData) {
    final model = InformasiabsensiModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.lokasiabsensi = dynamicData['lokasiAbsensi'];
    model.tanggaljam = MahasFormat.dynamicToDateTime(dynamicData['tanggalJam']);
    model.status = dynamicData['status'];
    model.latitude = MahasFormat.dynamicToDouble(dynamicData['latitude']);
    model.longitude = MahasFormat.dynamicToDouble(dynamicData['longitude']);
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.pegawai = dynamicData['pegawai'];
    model.idShift = MahasFormat.dynamicToInt(dynamicData['id_Shift']);
    model.namaShift = dynamicData['namaShift'];
    model.jamMulai = MahasFormat.stringToTime(dynamicData['jamMulai']);
    model.jamSelesai = MahasFormat.stringToTime(dynamicData['jamSelesai']);
    model.keterangan = dynamicData['keterangan'];

    return model;
  }
}
