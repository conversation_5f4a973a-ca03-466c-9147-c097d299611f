import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import '../../../mahas/mahas_colors.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/routes/app_pages.dart';
import '../../../controllers/auth_controller.dart';
import '../../../mahas/models/menu_item_model.dart';

class ProfileController extends GetxController {
  final List<MenuItemModel> menus = [];
  final AuthController authCon = AuthController.instance;

  void dataPribadiOnPress() {
    Get.toNamed(Routes.PROFILE_DATA_PRIBADI);
  }

  void divisiOnPress() {
    Get.toNamed(Routes.PROFILE_DIVISI);
  }

  void jenisSertifikasiOnPress() {
    Get.toNamed(Routes.PROFILE_JENIS_SERTIFIKASI);
  }

  void keluargaOnPress() {
    Get.toNamed(Routes.PROFILE_KELUARGA);
  }

  void riwayatKesehatanOnPress() {
    Get.toNamed(Routes.PROFILE_RIWAYAT_KESEHATAN);
  }

  void hobiOnPress() {
    Get.toNamed(Routes.PROFILE_HOBI);
  }

  void pengalamanOnPress() {
    Get.toNamed(Routes.PROFILE_PENGALAMAN);
  }

  void rekeningOnPress() {
    Get.toNamed(Routes.PROFILE_REKENING);
  }

  void riwayatPendidikanOnPress() {
    Get.toNamed(Routes.PROFILE_RIWAYAT_PENDIDIKAN);
  }

  void kontakPegawaiOnPress() {
    Get.toNamed(Routes.PROFILE_KONTAK_PEGAWAI);
  }

  void seragamOnPress() {
    Get.toNamed(Routes.PROFILE_SERAGAM);
  }

  void dokumenOnPress() {
    Get.toNamed(Routes.PROFILE_DOKUMEN);
  }

  void kontrakOnPress() {
    Get.toNamed(Routes.PROFILE_KONTRAK);
  }

  void strOnPress() {
    Get.toNamed(Routes.PROFILE_PEGAWAI_STR);
  }

  void kgbOnPress() {
    Get.toNamed(Routes.PROFILE_PEGAWAI_KGB);
  }

  void pangkatOnPress() {
    Get.toNamed(Routes.PROFILE_PEGAWAI_PANGKAT);
  }

  void kategoriBiosOnPress() {
    Get.toNamed(Routes.PROFILE_KATEGORIBIOS);
  }

  void hapusLoginAkunOnPress() async {
    if (MahasConfig.demoLogin) {
      Helper.dialogWarning('Demo Account tidak dapat di hapus');
      return;
    }
    if (EasyLoading.isShow) return;
    var r = await Helper.dialogQuestion(
      message: 'Anda yakin akan menghapus akun ini?',
      textConfirm: 'Hapus',
      color: MahasColors.red,
    );
    if (r == true) {
      EasyLoading.show();
      await authCon.deleteAccount();
      EasyLoading.dismiss();
    }
  }

  void signOutOnPress() {
    authCon.signOut();
  }

  @override
  void onInit() {
    menus.add(MenuItemModel(
        'Data Pribadi', FontAwesomeIcons.idCard, dataPribadiOnPress));
    if (MahasConfig.profileMenu.kontak == true) {
      menus.add(MenuItemModel('Kontak Pegawai', FontAwesomeIcons.addressBook,
          kontakPegawaiOnPress));
    }
    if (MahasConfig.profileMenu.kesehatan == true) {
      menus.add(MenuItemModel('Riwayat Kesehatan',
          FontAwesomeIcons.notesMedical, riwayatKesehatanOnPress));
    }
    if (MahasConfig.profileMenu.pendidikan == true) {
      menus.add(MenuItemModel('Riwayat Pendidikan', FontAwesomeIcons.laptopFile,
          riwayatPendidikanOnPress));
    }
    if (MahasConfig.profileMenu.keluarga == true) {
      menus.add(MenuItemModel(
          'Data Keluarga', FontAwesomeIcons.peopleRoof, keluargaOnPress));
    }
    if (MahasConfig.profileMenu.hobi == true) {
      menus.add(
          MenuItemModel('Hobi', FontAwesomeIcons.personWalking, hobiOnPress));
    }
    if (MahasConfig.profileMenu.rekening == true) {
      menus.add(MenuItemModel(
          'Rekening', FontAwesomeIcons.fileInvoiceDollar, rekeningOnPress));
    }
    if (MahasConfig.profileMenu.sertifikasi == true) {
      menus.add(MenuItemModel('Jenis Sertifikasi',
          FontAwesomeIcons.fileCircleCheck, jenisSertifikasiOnPress));
    }
    if (MahasConfig.profileMenu.pengalaman == true) {
      menus.add(MenuItemModel(
          'Pengalaman', FontAwesomeIcons.personCircleCheck, pengalamanOnPress));
    }
    if (MahasConfig.profileMenu.seragam == true) {
      menus.add(MenuItemModel(
          'Seragam', FontAwesomeIcons.userSecret, seragamOnPress));
    }
    if (MahasConfig.profileMenu.dokumen == true) {
      menus
          .add(MenuItemModel('Dokumen', FontAwesomeIcons.file, dokumenOnPress));
    }
    if (MahasConfig.profileMenu.kontrak == true) {
      menus.add(MenuItemModel(
          'Kontrak', FontAwesomeIcons.fileSignature, kontrakOnPress));
    }
    if (MahasConfig.profileMenu.str == true) {
      menus.add(MenuItemModel(
          'STR dan SIP', FontAwesomeIcons.fileContract, strOnPress));
    }
    if (MahasConfig.profileMenu.kgb == true) {
      menus.add(MenuItemModel('Kenaikan Gaji Berkala',
          FontAwesomeIcons.moneyCheckDollar, kgbOnPress));
    }
    if (MahasConfig.profileMenu.pangkat == true) {
      menus.add(MenuItemModel(
          'History Pangkat', FontAwesomeIcons.chartLine, pangkatOnPress));
    }
    if (MahasConfig.profileMenu.kategoriBios == true) {
      menus.add(MenuItemModel(
          'Kategori BIOS', FontAwesomeIcons.bookBible, kategoriBiosOnPress));
    }
    menus.add(MenuItemModel('Divisi', FontAwesomeIcons.users, divisiOnPress));
    menus.add(MenuItemModel(
        'Hapus Login Akun', FontAwesomeIcons.trash, hapusLoginAkunOnPress));
    menus.add(MenuItemModel(
        'Sign Out', FontAwesomeIcons.rightFromBracket, signOutOnPress));

    super.onInit();
  }
}
