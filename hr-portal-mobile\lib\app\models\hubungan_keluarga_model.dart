import 'dart:convert';

import '../mahas/services/mahas_format.dart';

class HubunganKeluargaModel {
  int? id;
  String? nama;

  HubunganKeluargaModel();

  static HubunganKeluargaModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static HubunganKeluargaModel fromDynamic(dynamic dynamicData) {
    final model = HubunganKeluargaModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.nama = dynamicData['nama'];

    return model;
  }
}
