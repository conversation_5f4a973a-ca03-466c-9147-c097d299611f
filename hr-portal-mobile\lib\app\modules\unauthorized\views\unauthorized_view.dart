import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/constant/environment_constant.dart';

import '../controllers/unauthorized_controller.dart';

class UnauthorizedView extends GetView<UnauthorizedController> {
  const UnauthorizedView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        color: Colors.white,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                height: 200,
                child: Image.asset(
                  EnvironmentConstant.imageLogo,
                  scale: 1,
                ),
              ),
              Text(
                controller.isiPesanCon,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 20),
              ),
              const SizedBox(
                height: 20,
              ),
              ElevatedButton(
                onPressed: controller.singOutOnPress,
                child: const Text("Sign out"),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
