// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options_staging.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBJqhT6Z-h_Oh6TPcOTveNFMJMu9MSbbtg',
    appId: '1:171917817792:web:f6dcb73da4433fe637fa25',
    messagingSenderId: '171917817792',
    projectId: 'hr-portal-staging',
    authDomain: 'hr-portal-staging.firebaseapp.com',
    storageBucket: 'hr-portal-staging.firebasestorage.app',
    measurementId: 'G-WDZK42C14M',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCGtXYeZ1KoFi4h-LMFdAQNMDmBU15kz1c',
    appId: '1:171917817792:android:8b63c9687c32a38e37fa25',
    messagingSenderId: '171917817792',
    projectId: 'hr-portal-staging',
    storageBucket: 'hr-portal-staging.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCOj5qu11m96gH49Xz5iwO8kJ_xDL-vxDw',
    appId: '1:171917817792:ios:bf088a84ad3e38ed37fa25',
    messagingSenderId: '171917817792',
    projectId: 'hr-portal-staging',
    storageBucket: 'hr-portal-staging.firebasestorage.app',
    androidClientId: '171917817792-t5qok4mghkjp8j7d9t0b6v650f4vtkoc.apps.googleusercontent.com',
    iosClientId: '171917817792-jsufcs4ri8cc6d95g2uq4ad351mu1ap4.apps.googleusercontent.com',
    iosBundleId: 'com.sanata.hrportal.staging',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCOj5qu11m96gH49Xz5iwO8kJ_xDL-vxDw',
    appId: '1:171917817792:ios:bf088a84ad3e38ed37fa25',
    messagingSenderId: '171917817792',
    projectId: 'hr-portal-staging',
    storageBucket: 'hr-portal-staging.firebasestorage.app',
    androidClientId: '171917817792-t5qok4mghkjp8j7d9t0b6v650f4vtkoc.apps.googleusercontent.com',
    iosClientId: '171917817792-jsufcs4ri8cc6d95g2uq4ad351mu1ap4.apps.googleusercontent.com',
    iosBundleId: 'com.sanata.hrportal.staging',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBJqhT6Z-h_Oh6TPcOTveNFMJMu9MSbbtg',
    appId: '1:171917817792:web:d379b3c4dcb0dd6237fa25',
    messagingSenderId: '171917817792',
    projectId: 'hr-portal-staging',
    authDomain: 'hr-portal-staging.firebaseapp.com',
    storageBucket: 'hr-portal-staging.firebasestorage.app',
    measurementId: 'G-W4FF9BJ1DZ',
  );

}