// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBQxgP_mjYOjukc1GZRi585Mpv1oi8eR30',
    appId: '1:673110614921:web:8ff07541ac56fc11d1b758',
    messagingSenderId: '673110614921',
    projectId: 'hr-portal-sanata',
    authDomain: 'hr-portal-sanata.firebaseapp.com',
    storageBucket: 'hr-portal-sanata.appspot.com',
    measurementId: 'G-KBNN725QPX',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCCoL4g-tuziDr59FVOf9ZXFSIqITaVPjs',
    appId: '1:673110614921:android:03b6d410803aa817d1b758',
    messagingSenderId: '673110614921',
    projectId: 'hr-portal-sanata',
    storageBucket: 'hr-portal-sanata.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAb0uwtGcnkdjhT1ku62jAOauPq_vJ4WlQ',
    appId: '1:673110614921:ios:f99835b58db0fc12d1b758',
    messagingSenderId: '673110614921',
    projectId: 'hr-portal-sanata',
    storageBucket: 'hr-portal-sanata.appspot.com',
    iosClientId: '673110614921-ue416g6106nnmra6n0uhl18q0ve2nai2.apps.googleusercontent.com',
    iosBundleId: 'com.sanata.hrportal',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyAb0uwtGcnkdjhT1ku62jAOauPq_vJ4WlQ',
    appId: '1:673110614921:ios:f99835b58db0fc12d1b758',
    messagingSenderId: '673110614921',
    projectId: 'hr-portal-sanata',
    storageBucket: 'hr-portal-sanata.appspot.com',
    iosClientId: '673110614921-ue416g6106nnmra6n0uhl18q0ve2nai2.apps.googleusercontent.com',
    iosBundleId: 'com.sanata.hrportal',
  );
}
