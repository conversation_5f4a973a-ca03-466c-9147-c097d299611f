import 'dart:convert';

class FotoProfileModel {
  String? fotoprofile;

  FotoProfileModel();

  static FotoProfileModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static FotoProfileModel fromDynamic(dynamic dynamicData) {
    final model = FotoProfileModel();

    model.fotoprofile = dynamicData['fotoProfile'];

    return model;
  }
}
