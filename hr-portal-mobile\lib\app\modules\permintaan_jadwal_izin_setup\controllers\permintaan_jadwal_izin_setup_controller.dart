// ignore_for_file: unused_local_variable

import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_datetime_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_dropdown_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_file_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_lookup_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_text_component.dart';
import 'package:hr_portal/app/mahas/components/pages/setup_page_component.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:hr_portal/app/mahas/models/api_result_model.dart';
import 'package:hr_portal/app/mahas/services/http_api.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';
import 'package:hr_portal/app/models/izin_model.dart';
import 'package:hr_portal/app/models/jenis_izin_model.dart';

import '../../../mahas/services/helper.dart';
import '../../../models/pegawai_approval.dart';

class PermintaanJadwalIzinSetupController extends GetxController {
  late SetupPageController formCon;
  final pegawaiIzinCon = InputLookupController<LookupizinModel>(
    fromDynamic: (e) => LookupizinModel.fromDynamic(e),
    itemText: (e) => e.nama ?? "",
    itemValue: (e) => e.id,
    urlApi: (index, filter) =>
        '/api/PermintaanJadwal/Izin/Pegawai?pageIndex=$index&filter=$filter&id_divisi=${MahasConfig.selectedDivisi}',
  );
  final harikCon = InputTextController(
    type: InputTextType.number,
  );

  final dariTglCon = InputDatetimeController();
  final alasanCon = InputTextController(
    type: InputTextType.paragraf,
  );
  final jenisIzinCon = InputDropdownController();
  final berapaHariCon = InputTextController(type: InputTextType.number);
  final sampaiTanggalCon = InputDatetimeController();

  final berkasPendukungCon = InputFileController(
    type: FileType.custom,
    extension: ['pdf'],
    tipe: InputFileType.pdf,
  );

  RxInt quotaJenis = 0.obs;
  String tittle = MahasConfig.dinamisForm.izin?.tittle ?? "Izin";
  String tittleQuotaIzin =
      MahasConfig.dinamisForm.izin?.tittleQuotaIzin ?? "Quota Izin";

  RxBool isReadOnly = false.obs;
  RxBool showAlasan = false.obs;
  RxBool wajibDokumen = false.obs;

  void onJenisIzinChanged(DropdownItem? selectedItem) {
    if (selectedItem != null) {
      if (!selectedItem.text
          .toString()
          .contains(RegExp("lain-lain", caseSensitive: false))) {
        isReadOnly.value = true;
        showAlasan.value = false;
      } else {
        isReadOnly.value = false;
        showAlasan.value = true;
      }
      alasanCon.value = null;
    }
  }

  final alasanTolakCon = InputTextController();
  final String nama = MahasConfig.profile!.nama!;
  late String approve;
  late bool allowED;
  Rxn<bool> accKadiv = Rxn<bool>();
  Rxn<bool> accManager = Rxn<bool>();
  RxBool isVisible = false.obs;
  RxBool isBatal = false.obs;
  late RxString pegawaiKadiv = ''.obs;
  late RxString pegawaiManager = ''.obs;
  late String kadiv;
  late String management;
  late bool isKadiv;
  late bool isManagement;
  RxString statusTolak = ''.obs;
  RxInt idPegawaiRequest = 0.obs;
  final isDinamisApprove = MahasConfig.profileMenu.approvalDinamis;
  final jenisIzin = MahasConfig.profileMenu.jenisIzin;
  RxBool isStop = false.obs;
  RxList<PegawaiApprovalModel> modelApproval = <PegawaiApprovalModel>[].obs;
  int? id;

  @override
  void onInit() {
    jenisIzin == false ? showAlasan.value = true : showAlasan.value = false;
    cekKadiv();
    cekManagement();
    cekApproval();
    jenisIzinCon.onChanged = (v) {
      onJenisIzinChanged(v);
      harikCon.value = v?.label ?? '0';
      quotaJenis.value = int.parse(v?.label ?? '0');
      wajibDokumen.value = v?.additionalValue ?? false;
    };
    if (isKadiv == true || isManagement == true) {
      pegawaiIzinCon.onChanged =
          () => idPegawaiRequest.value = pegawaiIzinCon.value?.id ?? 0;
    }
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PermintaanJadwal/Izin/$id',
      urlApiPost: () => '/api/PermintaanJadwal/Izin',
      urlApiPut: (id) => '/api/PermintaanJadwal/Izin/$id',
      urlApiDelete: (id) => '/api/PermintaanJadwal/Izin/$id',
      allowDelete: allowED,
      allowEdit: allowED,
      bodyApi: (id) => {
        "id_Pegawai": pegawaiIzinCon.value?.id,
        "id_Divisi": MahasConfig.selectedDivisi,
        "berapaHari": berapaHariCon.value,
        "alasan": alasanCon.value,
        "dariTanggal": MahasFormat.dateToString(dariTglCon.value),
        "id_JenisIzin": jenisIzinCon.value,
        "berkasPendukung": berkasPendukungCon.value
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (isKadiv == true || isManagement == true) {
          if (!pegawaiIzinCon.isValid) return false;
        }
        if (!dariTglCon.isValid) return false;
        if (!berapaHariCon.isValid) return false;
        if (dariTglCon.value
            .isBefore(DateTime.now().subtract(const Duration(days: 1)))) {
          Helper.dialogWarning("Tanggal Cuti tidak boleh kurang dari hari ini");
          return false;
        }
        if (!jenisIzinCon.isValid) return false;
        if (jenisIzin == true) {
          if (berapaHariCon.value > quotaJenis.value) {
            Helper.dialogWarning(
                "${jenisIzinCon.text} Tidak Boleh Lebih Dari \n${quotaJenis.value} Hari!");
            return false;
          }
        }
        String hari = berapaHariCon.value.toString();
        if (!RegExp(r'^[1-9]+$').hasMatch(hari)) {
          Helper.dialogWarning("Jumlah Hari tidak valid");
          return false;
        }
        if (!alasanCon.isValid) return false;
        if (wajibDokumen.value && berkasPendukungCon.value == null) {
          Helper.dialogWarning("Berkas Pendukung Wajib Diisi pada Jenis ini");
          return false;
        }
        if ((!jenisIzin ||
                jenisIzinCon.text
                    .toString()
                    .contains(RegExp("lain-lain", caseSensitive: false))) &&
            berapaHariCon.value < 1) {
          Helper.dialogWarning("Izin Tidak Boleh Kurang Dari \n1 Hari!");
          return false;
        }
        return true;
      },
      successMessage: Helper.succesPengajuanMessage(),
      apiToView: (json) {
        IzinModel model = IzinModel.fromJson(json);
        id = model.id;
        if (isKadiv == true || isManagement == true) {
          pegawaiIzinCon.value = LookupizinModel.init(
              model.idPegawaiRequest, model.pegawairequest);
        }
        berapaHariCon.value = model.berapahari;
        dariTglCon.value = model.daritanggal;
        sampaiTanggalCon.value =
            model.daritanggal?.add(Duration(days: model.berapahari! - 1));
        alasanCon.value = model.alasan;

        jenisIzinCon.value = model.idJenisIzin;
        berkasPendukungCon.value = model.berkasPendukung;
        showAlasan.value = model.alasan != "" ? true : false;

        accManager.value = model.approvemanager;
        accKadiv.value = model.approvekadiv;
        pegawaiKadiv.value = model.pegawaikadiv!;
        pegawaiManager.value = model.pegawaimanager!;

        statusTolak.value = model.alasantolak ?? '';
        idPegawaiRequest.value = model.idPegawaiRequest!;

        // approval
        if (isDinamisApprove == false && allowED == false) {
          if ((model.approvekadiv == null &&
                  model.idPegawaiKadiv == MahasConfig.profile!.id) ||
              (model.approvekadiv == true &&
                  model.approvemanager == null &&
                  model.idPegawaiManager == MahasConfig.profile!.id)) {
            isVisible.value = true;
          }
        }

        // batal
        if (isDinamisApprove == false && allowED == false) {
          if (model.approvekadiv == true &&
              model.approvemanager == true &&
              model.idPegawaiManager == MahasConfig.profile!.id) {
            if (MahasConfig.dinamisForm.approval?.bisaBatal == true) {
              isBatal.value = true;
            }
          }
        }

        if (isDinamisApprove == true && allowED == false) {
          getDataApproval();
        }
      },
      onInit: () async {
        await getJenisIzin();
      },
    );
    cekValidasi();
    super.onInit();
  }

  void cekKadiv() {
    kadiv = Get.parameters['isKadiv'].toString();
    if (kadiv == "false") {
      isKadiv = false;
    } else {
      isKadiv = true;
    }
  }

  void cekManagement() {
    management = Get.parameters['isManagement'].toString();
    if (management == "false") {
      isManagement = false;
    } else {
      isManagement = true;
    }
  }

  void cekApproval() {
    approve = Get.parameters['approval'].toString();
    if (approve == "approval") {
      allowED = false;
    } else {
      allowED = true;
    }
  }

  void cekValidasi() {
    dariTglCon.onChanged = () {
      if (sampaiTanggalCon.value != null) {
        berapaHariCon.value =
            sampaiTanggalCon.value.difference(dariTglCon.value).inDays + 1;
      }
    };
    sampaiTanggalCon.onChanged = () {
      if (dariTglCon.value != null) {
        berapaHariCon.value =
            sampaiTanggalCon.value.difference(dariTglCon.value).inDays + 1;
      }
    };
    sampaiTanggalCon.onCheck = (date) {
      if (dariTglCon.value == null) {
        Helper.dialogWarning("Pilih Tanggal Mulai Terlebih Dahulu");
        return false;
      }
      DateTime dariTanggal = dariTglCon.value;
      DateTime sampaiTanggal = date ?? DateTime.now();
      if (sampaiTanggal.isBefore(dariTanggal)) {
        Helper.dialogWarning(
            "Tanggal Selesai tidak boleh kurang dari Tanggal Mulai");
        return false;
      }
      return true;
    };
    sampaiTanggalCon.onClear = () {
      berapaHariCon.value = 0;
    };
    dariTglCon.onClear = () {
      berapaHariCon.value = 0;
    };
  }

  Future<void> batalOnPress() async {
    var id = Get.parameters['id'].toString();
    String urlManager = '/api/PermintaanJadwal/Izin/Batal/Manager/';
    var action = await Helper.batalAction(
      id,
      alasanTolakCon.value,
      urlManager,
    );
    if (action.success) {
      final url = '/api/PermintaanJadwal/Izin/$id';
      ApiResultModel r;
      r = await HttpApi.get(url);
      if (action.success) {
        IzinModel model = IzinModel.fromJson(r.body);
        accKadiv.value = model.approvekadiv;
        accManager.value = model.approvemanager;
        statusTolak.value = model.alasantolak ?? '';
        isBatal.value = false;
        update();
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan aksi, silahkan cek koneksi internet");
    } else if (!action.success) {
      Helper.dialogWarning(
        "Gagal melakukan aksi",
        resMassage: action.message,
        noInternetMassage:
            "Gagal melakukan aksi, silahkan cek koneksi internet",
      );
    }
  }

  Future<void> approvalOnPress(bool approve) async {
    var id = Get.parameters['id'].toString();
    String urlKadiv = '/api/PermintaanJadwal/Izin/Approve/Kadiv/';
    String urlManager = '/api/PermintaanJadwal/Izin/Approve/Manager/';
    String urlApprove = '/api/PermintaanJadwal/Izin/Approve/';
    var action = await Helper.approvalAction(
      id,
      approve,
      alasanTolakCon.value,
      accKadiv.value,
      urlKadiv,
      urlManager,
      isDinamis: isDinamisApprove,
      urlApprove: urlApprove,
    );
    if (action.success) {
      final url = '/api/PermintaanJadwal/Izin/$id';
      ApiResultModel r;
      r = await HttpApi.get(url);
      if (r.success) {
        IzinModel model = IzinModel.fromJson(r.body);
        accKadiv.value = model.approvekadiv;
        accManager.value = model.approvemanager;
        statusTolak.value = model.alasantolak ?? '';
        if (isDinamisApprove) {
          getDataApproval();
        }
        isVisible.value = false;
        update();
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan approve, silahkan cek koneksi internet");
    } else if (!action.success) {
      Helper.dialogWarning(
        "Gagal melakukan approval",
        resMassage: action.message,
        noInternetMassage:
            "Gagal melakukan approval, silahkan cek koneksi internet",
      );
    }
  }

  Future<void> getDataApproval() async {
    EasyLoading.show();
    final url = '/api/PermintaanJadwal/Izin/Approval/$id';
    final r = await HttpApi.get(url);
    modelApproval.clear();
    if (r.success) {
      if (r.body != null) {
        var data = json.decode(r.body);
        for (var e in data['datas']) {
          modelApproval.add(PegawaiApprovalModel.fromDynamic(e));
        }
        bool canApprove = modelApproval.any((item) =>
            item.idPegawaiapprove == MahasConfig.profile?.id &&
            item.approve == null);
        if (canApprove) {
          isVisible.value = true;
        }
        bool notApprove = modelApproval.any((item) => item.approve == false);
        if (notApprove) {
          isVisible.value = false;
        }
        isStop.value = modelApproval.any((item) => item.approve == false);
      } else {
        Helper.dialogWarning(r.message);
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan aksi, silahkan cek koneksi internet");
    } else if (r.statusCode == 404) {
      Helper.dialogWarning("Tidak Ada Data Pegawai Approval");
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }

  Future<void> getJenisIzin() async {
    var r = await HttpApi.get("/api/PermintaanJadwal/Izin/JenisIzin");
    if (r.success) {
      RxList<JenisIzinModel> listModel = RxList<JenisIzinModel>();
      var model = jsonDecode(r.body);
      for (var e in (model ?? [])) {
        listModel.add(JenisIzinModel.fromDynamic(e));
      }
      if (r.body != null && jenisIzinCon.items.isEmpty) {
        jenisIzinCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem(
                text: e.nama ?? '',
                value: e.id,
                label: e.durasiLibur.toString(),
                additionalValue: e.wajibDokumen,
              ),
            )
            .toList();
      }
    } else {
      Helper.fetchErrorMessage(r);
    }
    EasyLoading.dismiss();
  }
}
