import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_datetime_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_text_component.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/mahas/components/mahas_themes.dart';
import 'package:hr_portal/app/mahas/components/pages/setup_page_component.dart';

import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/components/inputs/input_radio_component.dart';
import '../../../mahas_complement/status_request.dart';
import '../controllers/permintaan_jadwal_cuti_sakit_setup_controller.dart';

class PermintaanJadwalCutiSakitSetupView
    extends GetView<PermintaanJadwalCutiSakitSetupController> {
  const PermintaanJadwalCutiSakitSetupView({super.key});

  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      title: controller.title,
      controller: controller.formCon,
      children: () => [
        Visibility(
          visible: controller.formCon.isState != SetupPageState.create,
          child: Column(
            children: [
              Obx(() => Text(
                  "Tanggal/Waktu pengajuan : ${controller.tglPengajuan.value}")),
              const SizedBox(
                height: 10,
              ),
            ],
          ),
        ),
        controller.isKadiv == true &&
                controller.formCon.isState == SetupPageState.detail
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  InputTextComponent(
                    label: "Pegawai Request",
                    controller: controller.pegawaiCutiCon,
                    editable: false,
                    marginBottom: 5,
                  ),
                  InkWell(
                    onTap: () async => await Helper.fotoProfilOnTap(
                        controller.idPegawaiRequest.value),
                    child: SizedBox(
                      height: 30,
                      child: Text(
                        "Lihat Foto Profil Pegawai",
                        style: MahasThemes.link,
                        textAlign: TextAlign.left,
                      ),
                    ),
                  ),
                ],
              )
            : const SizedBox(),
        Obx(() => InputDatetimeComponent(
              label: "Dari Tanggal",
              controller: controller.dariTanggalCon,
              required: true,
              editable: controller.isApproveManager.value == true
                  ? false
                  : controller.formCon.editable,
            )),
        InputDatetimeComponent(
          label: "Sampai Tanggal",
          controller: controller.sampaiTanggalCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Jumlah Hari",
          required: false,
          controller: controller.berapaHariCon,
          editable: false,
        ),
        InputTextComponent(
          label: "Diagnosa",
          required: true,
          controller: controller.alasanCon,
          editable: controller.formCon.editable,
        ),
        InputRadioComponent(
          controller: controller.rawatInapCon,
          editable: controller.formCon.editable,
          required: true,
          label: 'Rawat Inap',
        ),
        Obx(() => InputTextComponent(
              label: "Keterangan",
              required: controller.requiredKeterangan.value,
              controller: controller.keteranganCon,
              editable: controller.formCon.editable,
            )),
        InputFileComponent(
          controller: controller.fileCon,
          label: "Upload Dokumen",
          editable: controller.formCon.editable,
          required: false,
        ),
        const SizedBox(
          height: 20,
        ),
        Obx(
          () => ApprovalStatusContainer(
            isEditable: controller.formCon.editable,
            isVisible: controller.isVisible,
            isBatal: controller.isBatal,
            allowED: controller.allowED,
            isDinamisApprove: controller.isDinamisApprove,
            alasanTolakController: controller.alasanTolakCon,
            onTolakPressed: () {
              controller.approvalOnPress(false);
            },
            onTerimaPressed: () {
              controller.approvalOnPress(true);
            },
            batalOnPress: () {
              var status = controller.isBatal.isTrue ? 'batal' : 'tolak';
              alert(status);
            },
            pegawaiKadiv: controller.pegawaiKadiv.value,
            pegawaiManager: controller.pegawaiManager.value,
            accKadiv: controller.accKadiv.value,
            accManager: controller.accManager.value,
            statusTolak: controller.statusTolak.value,
            modelApproval: controller.modelApproval,
            isStop: controller.isStop.value,
          ),
        ),
      ],
    );
  }

  Future<dynamic> alert(String status) {
    return Get.dialog(
      AlertDialog(
        content: SizedBox(
          width: 1000,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              InputTextComponent(
                label: status == "tolak" ? "Alasan Ditolak" : "Alasan Batal",
                controller: controller.alasanTolakCon,
                required: true,
              ),
            ],
          ),
        ),
        contentPadding:
            const EdgeInsets.only(bottom: 0, top: 20, right: 10, left: 10),
        actionsPadding:
            const EdgeInsets.only(top: 0, bottom: 0, left: 10, right: 10),
        actions: [
          TextButton(
            child: Text(
              "Close",
              style: MahasThemes.muted,
            ),
            onPressed: () {
              Get.back(result: false);
            },
          ),
          TextButton(
            child: const Text(
              "Simpan",
            ),
            onPressed: () {
              if (controller.alasanTolakCon.isValid) {
                if (status == "tolak") {
                  controller.approvalOnPress(false);
                } else if (status == "batal") {
                  controller.batalOnPress();
                }
                Get.back(result: true);
              }
            },
          ),
        ],
      ),
    );
  }
}
