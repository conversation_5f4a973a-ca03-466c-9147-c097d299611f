import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_checkbox_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../controllers/profile_rekening_setup_controller.dart';

class ProfileRekeningSetupView extends GetView<ProfileRekeningSetupController> {
  const ProfileRekeningSetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      controller: controller.formCon,
      title: 'Informasi',
      children: () => [
        InputTextComponent(
          label: "No Rekening",
          controller: controller.noRekeningCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDropdownComponent(
          label: "Nama Bank",
          controller: controller.namaBankCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Atas Nama",
          controller: controller.atasNamaCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputCheckboxComponent(
          controller: controller.aktifCon,
          label: 'Aktif',
          isSwitch: true,
          editable: controller.formCon.editable,
        ),
        InputCheckboxComponent(
          label: "Prioritas",
          controller: controller.prioritasCon,
          isSwitch: true,
          editable: controller.formCon.editable,
        ),
      ],
    );
  }
}
