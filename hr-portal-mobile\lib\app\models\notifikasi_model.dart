import 'dart:convert';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';

class NotifikasiModel {
  int? id;
  DateTime? tanggal;
  String? judul;
  String? deskripsi;
  String? menu;
  int? menuid;
  bool? dibaca;
  DateTime? tanggaldibaca;

  NotifikasiModel();

  static NotifikasiModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static NotifikasiModel fromDynamic(dynamic dynamicData) {
    final model = NotifikasiModel();

    model.id = dynamicData['id'];
    model.tanggal = MahasFormat.stringToDateTime(dynamicData['tanggal']);
    model.judul = dynamicData['judul'];
    model.deskripsi = dynamicData['deskripsi'];
    model.menu = dynamicData['menu'];
    var jsonMenuId = json.decode(dynamicData['menuId']);
    model.menuid = jsonMenuId['Id'] ?? 0;
    model.dibaca = dynamicData['dibaca'];
    model.tanggaldibaca =
        MahasFormat.stringToDateTime(dynamicData['tanggalDibaca']);

    return model;
  }
}
