import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_file_component.dart';
import 'package:hr_portal/app/mahas/components/mahas_themes.dart';
import 'package:hr_portal/app/mahas/components/pages/setup_page_component.dart';
import 'package:hr_portal/app/mahas/mahas_colors.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_radio_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/mahas_config.dart';
import '../controllers/absen_surat_tugas_setup_controller.dart';

class AbsenSuratTugasSetupView extends GetView<AbsenSuratTugasSetupController> {
  const AbsenSuratTugasSetupView({super.key});

  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      title: '${MahasConfig.dinamisForm.absensi?.title ?? 'Absensi'} Tugas',
      controller: controller.formCon,
      children: () => [
        InputTextComponent(
          label:
              "Lokasi ${MahasConfig.dinamisForm.absensi?.title ?? 'Absensi'}",
          required: true,
          controller: controller.lokasiAbsensiCon,
          editable: controller.formCon.editable,
        ),
        InputRadioComponent(
          label: MahasConfig.dinamisForm.absensi?.title ?? 'Absensi',
          required: true,
          controller: controller.masukCon,
          editable: controller.formCon.editable,
        ),
        Visibility(
          visible: controller.formCon.isState != SetupPageState.create,
          child: Column(
            children: [
              InputDatetimeComponent(
                label: "Tanggal",
                required: true,
                controller: controller.tanggalCon,
                editable: false,
              ),
              InputDatetimeComponent(
                type: InputDatetimeType.time,
                label: "Jam",
                required: true,
                controller: controller.jamCon,
                editable: false,
              ),
            ],
          ),
        ),
        InputTextComponent(
          label: "Keterangan",
          required: true,
          controller: controller.keteranganCon,
          editable: controller.formCon.editable,
        ),
        Visibility(
          visible: MahasConfig.profileMenu.absentugaswithfoto == true,
          child: InputFileComponent(
            controller: controller.fileCon,
            label: "Foto",
            editable: controller.formCon.editable,
            required: true,
          ),
        ),
        Obx(
          () {
            return Visibility(
              visible: controller.isVisible.value,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Expanded(
                    child: ElevatedButton(
                        onPressed: () {
                          alert();
                        },
                        style: ElevatedButton.styleFrom(
                            backgroundColor: MahasColors.red),
                        child: const Text("Tolak")),
                  ),
                  const SizedBox(
                    width: 20,
                  ),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        controller.approvalOnPress(true);
                      },
                      child: const Text("Terima"),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  Future<dynamic> alert() {
    return Get.dialog(
      AlertDialog(
        content: SizedBox(
          width: 1000,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              InputTextComponent(
                label: "Alasan Ditolak",
                controller: controller.alasanTolakCon,
              ),
            ],
          ),
        ),
        contentPadding:
            const EdgeInsets.only(bottom: 0, top: 20, right: 10, left: 10),
        actionsPadding:
            const EdgeInsets.only(top: 0, bottom: 0, left: 10, right: 10),
        actions: [
          TextButton(
            child: Text(
              "Close",
              style: MahasThemes.muted,
            ),
            onPressed: () {
              Get.back(result: false);
            },
          ),
          TextButton(
            child: const Text(
              "Simpan",
            ),
            onPressed: () {
              controller.approvalOnPress(false);
              Get.back(result: true);
            },
          ),
        ],
      ),
    );
  }
}
