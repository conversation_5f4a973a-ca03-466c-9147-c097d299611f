import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/others/list_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../models/profile_rekening_model.dart';
import '../controllers/profile_rekening_controller.dart';

class ProfileRekeningView extends GetView<ProfileRekeningController> {
  const ProfileRekeningView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Rekening'),
        centerTitle: true,
        actions: [
          GetBuilder<ProfileRekeningController>(
            builder: (controller) {
              return PopupMenuButton(
                onSelected: controller.popupMenuButtonOnSelected,
                itemBuilder: (context) {
                  List<PopupMenuItem<String>> r = [];
                  r.add(
                    const PopupMenuItem(
                      value: 'Tambah',
                      child: Text('Tambah'),
                    ),
                  );
                  if (MahasConfig.hasHistory) {
                    r.add(
                      const PopupMenuItem(
                        value: 'History',
                        child: Text('History'),
                      ),
                    );
                  }

                  return r;
                },
              );
            },
          ),
        ],
      ),
      body: ListComponent(
        controller: controller.listCon,
        itemBuilder: (ProfileRekeningModel e) {
          return InkWell(
            onTap: () => controller.itemOnTab(e.id!),
            child: Padding(
              padding:
                  const EdgeInsets.only(left: 12, right: 12, top: 8, bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 5),
                        Text("Nama Bank : ${e.namabank ?? "-"}",
                            style: MahasThemes.title),
                        Text("Nomor Rekening : ${e.norekening ?? "-"}"),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
