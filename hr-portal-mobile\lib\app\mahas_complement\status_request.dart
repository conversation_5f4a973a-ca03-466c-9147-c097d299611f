import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas_complement/mahas_colors.dart';

import '../mahas/components/inputs/input_text_component.dart';
import '../mahas/components/mahas_themes.dart';
import '../mahas/mahas_colors.dart';
import '../mahas/mahas_config.dart';
import '../models/pegawai_approval.dart';

class ApprovalStatusContainer extends StatefulWidget {
  final InputTextController alasanTolakController;
  final String? textStatus;
  final bool isEditable;
  final RxBool isVisible;
  final RxBool isBatal;
  final bool allowED;
  final bool isDinamisApprove;
  final VoidCallback onTolakPressed;
  final VoidCallback batalOnPress;
  final VoidCallback onTerimaPressed;
  final String pegawaiKadiv;
  final String pegawaiManager;
  final String? titleCardKadiv;
  final String? titleCardManager;
  final String? statusTolak;
  final bool? accKadiv;
  final bool? accManager;
  final List<PegawaiApprovalModel> modelApproval;
  final bool isStop;
  final double? padding;

  const ApprovalStatusContainer({
    super.key,
    required this.alasanTolakController,
    this.textStatus,
    required this.isEditable,
    required this.isVisible,
    required this.isBatal,
    required this.allowED,
    required this.isDinamisApprove,
    required this.onTolakPressed,
    required this.batalOnPress,
    required this.onTerimaPressed,
    required this.pegawaiKadiv,
    required this.pegawaiManager,
    this.statusTolak,
    this.titleCardKadiv,
    this.titleCardManager,
    required this.accKadiv,
    required this.accManager,
    required this.modelApproval,
    required this.isStop,
    this.padding,
  });

  @override
  ApprovalStatusContainerState createState() => ApprovalStatusContainerState();
}

class ApprovalStatusContainerState extends State<ApprovalStatusContainer> {
  Future<dynamic> alert(BuildContext context) {
    return Get.dialog(
      AlertDialog(
        content: SizedBox(
          width: 1000,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              InputTextComponent(
                label: "Alasan Ditolak",
                controller: widget.alasanTolakController,
                required: true,
              ),
            ],
          ),
        ),
        contentPadding:
            const EdgeInsets.only(bottom: 0, top: 20, right: 10, left: 10),
        actionsPadding:
            const EdgeInsets.only(top: 0, bottom: 0, left: 10, right: 10),
        actions: [
          TextButton(
            child: Text(
              "Close",
              style: MahasThemes.muted,
            ),
            onPressed: () {
              Get.back(result: false);
            },
          ),
          TextButton(
            child: const Text(
              "Simpan",
            ),
            onPressed: () {
              if (widget.alasanTolakController.isValid) {
                widget.onTolakPressed();
                Get.back(result: true);
              }
            },
          ),
        ],
      ),
    );
  }

  Column approvalNotDinamis() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 10),
        Text(
          widget.textStatus ?? 'Status Request :',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 5),
        Visibility(
          visible: MahasConfig.dinamisForm.approval?.hanyaManager == false,
          child: Visibility(
            visible: widget.pegawaiKadiv != "",
            child: CardStatus(
              pangkatApproval:
                  widget.titleCardKadiv ?? MahasConfig.kadivmanager.kadiv!,
              namaApproval: widget.pegawaiKadiv,
              isAcc: widget.accKadiv,
              statusTolak: widget.statusTolak,
            ),
          ),
        ),
        CardStatus(
          pangkatApproval:
              widget.titleCardManager ?? MahasConfig.kadivmanager.manager!,
          namaApproval: widget.pegawaiManager,
          isAcc: widget.accManager,
          isStop: widget.accKadiv == false,
          statusTolak: widget.statusTolak,
        ),
      ],
    );
  }

  Column approvalDinamis() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 10),
        Text(
          widget.textStatus ?? 'Status Request :',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 5),
        Obx(() => ListView.builder(
              itemCount: widget.modelApproval.length,
              shrinkWrap: true,
              physics: const ScrollPhysics(),
              itemBuilder: (BuildContext context, int index) {
                var item = widget.modelApproval[index];
                return CardStatus(
                  pangkatApproval: "Tingkat ${item.tingkatan}",
                  namaApproval: item.pegawaiapprovenama ?? "",
                  isAcc: item.approve,
                  isStop: widget.isStop,
                  statusTolak: widget.statusTolak,
                );
              },
            )),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: widget.padding ?? 0),
      child: Column(
        children: [
          Obx(
            () => Visibility(
              visible: widget.isVisible.value,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => alert(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: MahasColors.red,
                      ),
                      child: const Text("Tolak"),
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: widget.onTerimaPressed,
                      child: const Text("Terima"),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Obx(
            () => Visibility(
              visible: widget.isBatal.value,
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: widget.batalOnPress,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: MahasColors.red,
                  ),
                  child: const Text("Batal"),
                ),
              ),
            ),
          ),
          Visibility(
            visible: !widget.allowED,
            child: widget.isDinamisApprove
                ? approvalDinamis()
                : approvalNotDinamis(),
          ),
        ],
      ),
    );
  }
}

class CardStatus extends StatelessWidget {
  final String pangkatApproval;
  final String namaApproval;
  final bool? isAcc;
  final bool? isStop;
  final String? statusTolak;

  const CardStatus({
    super.key,
    required this.pangkatApproval,
    required this.namaApproval,
    this.isAcc,
    this.isStop,
    this.statusTolak,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: MahasColor.primaryGrey,
            ),
            borderRadius: BorderRadius.circular(MahasThemes.borderRadius),
          ),
          padding: const EdgeInsets.all(10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Visibility(
                visible: MahasConfig.dinamisForm.approval?.visibleTitle == true,
                child: Text(
                  pangkatApproval,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Text(namaApproval),
              if (isAcc == null && isStop == true)
                const Row(
                  children: [
                    Text(
                      "Tidak dilanjutkan",
                    ),
                  ],
                )
              else if (isAcc == true)
                const Row(
                  children: [
                    Icon(
                      FontAwesomeIcons.check,
                      size: 12,
                      color: MahasColor.colorGreen,
                    ),
                    Text(
                      ' Diterima',
                      style: TextStyle(
                        color: MahasColor.colorGreen,
                      ),
                    ),
                  ],
                )
              else if (isAcc == false)
                const Row(
                  children: [
                    Icon(
                      FontAwesomeIcons.circleXmark,
                      size: 12,
                      color: MahasColors.red,
                    ),
                    Text(
                      " Ditolak",
                      style: TextStyle(color: MahasColors.red),
                    ),
                  ],
                )
              else if (isAcc == null)
                const Row(
                  children: [
                    Icon(
                      FontAwesomeIcons.clock,
                      size: 12,
                    ),
                    Text(" Menunggu")
                  ],
                ),
              if (isAcc == false)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Alasan : " "${statusTolak ?? "-"}",
                    ),
                  ],
                ),
            ],
          ),
        ),
        const SizedBox(
          height: 10,
        ),
      ],
    );
  }
}
