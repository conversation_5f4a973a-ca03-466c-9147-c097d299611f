{"project_info": {"project_number": "762631444819", "project_id": "hr-portal-sanata-dev", "storage_bucket": "hr-portal-sanata-dev.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:762631444819:android:3ac190c74751781de5e9a6", "android_client_info": {"package_name": "com.sanata.hrportal.dev"}}, "oauth_client": [{"client_id": "762631444819-86e2j8vopmt4o0mh91n6qrtrkhefhger.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sanata.hrportal.dev", "certificate_hash": "5dd67a72a107d0566d7b79bda78d1451e1f81a1f"}}, {"client_id": "762631444819-udbljed5pg2ido8lpra6te6lnlj8nh46.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sanata.hrportal.dev", "certificate_hash": "d66b098b2b99d0dca4fedcd8955251569e83ee15"}}, {"client_id": "762631444819-slmpsfkoqbh2ql6p9klb4v4ii18ncq5o.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAjVZI7nrq1moVqmFF-tYGhcA8bmLm-YPE"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "762631444819-slmpsfkoqbh2ql6p9klb4v4ii18ncq5o.apps.googleusercontent.com", "client_type": 3}, {"client_id": "762631444819-uebma5sepvgn63sb9rkq9v203rm7npk7.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.sanata.hrportal.dev"}}]}}}], "configuration_version": "1"}