import 'dart:convert';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/mahas/services/http_api.dart';
import 'package:hr_portal/app/models/informasi_jenis_izin_model.dart';

class InformasiQuotaIzinController extends GetxController {
  var models = RxList<InformasiquotajenisizinModel>();
  var isLoading = false.obs;

  Future<void> onRefresh() async {
    if (isLoading.isTrue) return;
    isLoading.value = true;
    var r = await HttpApi.get('/api/Informasi/QuotaJenisIzin');
    if (r.success) {
      final datas = json.decode(r.body);
      models.clear();
      for (var e in datas) {
        models.add(InformasiquotajenisizinModel.fromDynamic(e));
      }
    } else {
      Helper.fetchErrorMessage(r, httpMethod: HttpMethod.get);
    }
    isLoading.value = false;
  }

  @override
  void onInit() {
    super.onInit();
    onRefresh();
  }
}
