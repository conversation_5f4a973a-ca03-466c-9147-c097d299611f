import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class KgbModel {
  int? id;
  int? idPegawai;
  String? nomorkep;
  DateTime? terhitungmulaitanggalberkala;
  DateTime? kgbberikutnya;

  KgbModel();

  static KgbModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static KgbModel fromDynamic(dynamic dynamicData) {
    final model = KgbModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.nomorkep = dynamicData['nomorKEP'];
    model.terhitungmulaitanggalberkala = MahasFormat.dynamicToDateTime(
        dynamicData['terhitungMulaiTanggalBerkala']);
    model.kgbberikutnya =
        MahasFormat.dynamicToDateTime(dynamicData['kgbBerikutnya']);

    return model;
  }
}
