import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:get/get.dart';

import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/others/list_component.dart';
import '../../../models/profile_seragam_model.dart';
import '../controllers/profile_seragam_controller.dart';

class ProfileSeragamView extends GetView<ProfileSeragamController> {
  const ProfileSeragamView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Seragam Pegawai'),
        centerTitle: true,
        actions: <Widget>[
          Padding(
            padding: const EdgeInsets.only(right: 10),
            child: GestureDetector(
              onTap: controller.addOnPress,
              child: const Icon(
                FontAwesomeIcons.plus,
                size: 24,
              ),
            ),
          ),
        ],
      ),
      body: ListComponent(
        controller: controller.listCon,
        itemBuilder: (SeragamModel e) {
          return ListTile(
            title: Text(e.namaSeragam ?? "-", style: MahasThemes.title),
            subtitle: Text(e.ukuran ?? "-", style: MahasThemes.muted),
            onTap: () => controller.itemOnTab(e.id!),
          );
        },
      ),
    );
  }
}
