import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_dropdown_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_radio_component.dart';

import '../../../mahas/components/inputs/input_checkbox_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas_complement/input_detail_setup_component.dart';
import '../../../models/profile_keluarga_model.dart';
import '../controllers/profile_keluarga_setup_controller.dart';

class ProfileKeluargaSetupView extends GetView<ProfileKeluargaSetupController> {
  const ProfileKeluargaSetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      controller: controller.formCon,
      title: '<PERSON> Keluarga',
      children: () => [
        InputDropdownComponent(
          label: "Hubungan Keluarga",
          controller: controller.hubunganKeluargaCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: 'Nama',
          controller: controller.namaCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputRadioComponent(
          controller: controller.jenisKelaminCon,
          label: 'Jenis Kelamin',
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          controller: controller.alamatDomisiliCon,
          label: 'Alamat Domisili',
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          controller: controller.noBpjsCon,
          label: 'No BPJS',
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          controller: controller.noBpjsTkCon,
          label: 'No BPJS TK',
          editable: controller.formCon.editable,
        ),
        InputCheckboxComponent(
          controller: controller.ditanggungPegawaiCon,
          editable: controller.formCon.editable,
          label: 'Ditanggung Pegawai',
          isSwitch: true,
        ),
        InputDetailSetupComponent<ProfilekeluargaDetailkeluargakontakModel>(
          label: "Kontak",
          controller: controller.detailSetupCon,
          editable: controller.formCon.editable,
          required: true,
          formBuilder: (e) => Column(
            children: [
              // form detail
              InputDropdownComponent(
                controller: controller.jenisKontakCon,
                label: 'Jenis Kontak',
                required: true,
                editable: controller.formCon.editable,
              ),
              Obx(
                () => InputTextComponent(
                  controller: controller.ketKontakCon,
                  label: controller.namaLabel.value,
                  required: true,
                  editable: controller.formCon.editable,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
