import 'dart:convert';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_checkbox_component.dart';
import '../../../mahas/components/inputs/input_detail_setup_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_radio_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/models/api_list_resut_model.dart';
import '../../../mahas/models/api_result_model.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../models/hubungan_keluarga_model.dart';
import '../../../models/jenis_kontak_model.dart';
import '../../../models/profile_keluarga_model.dart';
import '../../../routes/app_pages.dart';

class ProfileKeluargaHistorySetupController extends GetxController {
  late SetupPageController formCon;
  late bool reqDelete;
  late bool showStatus;
  late bool withActionBack;

  final hubunganKeluargaCon = InputDropdownController();
  final namaCon = InputTextController();
  final jenisKelaminCon = InputRadioController(
    items: [
      RadioButtonItem(text: "Laki-laki", value: 1),
      RadioButtonItem(text: "Perempuan", value: 2),
    ],
  );
  final alamatDomisiliCon = InputTextController(type: InputTextType.paragraf);
  final noBpjsCon = InputTextController();
  final noBpjsTkCon = InputTextController();
  final ditanggungPegawaiCon = InputCheckboxController();
  final alasanTolakCon = InputTextController();

  // setup list detail
  late InputDetailSetupControler<ProfilekeluargaDetailkeluargakontakModel>
      detailSetupCon;
  final jenisKontakCon = InputDropdownController();
  final ketKontakCon = InputTextController();
  RxString namaLabel = 'Nama'.obs;

  RxBool approvalButtonsVisible = false.obs;
  RxBool approvalFromNotif = false.obs;
  RxBool allowED = false.obs;
  late dynamic statusApprovalHRD = null.obs;
  RxString status = "".obs;
  RxString namaHRD = "".obs;
  RxString namaPegawaiRequest = "".obs;
  RxString alasanTolakHRD = "".obs;
  Rx<DateTime> tglPermintaan = DateTime.now().obs;
  RxInt idPegawaiKontak = 0.obs;

  @override
  void onInit() async {
    //init
    String notifParam = Get.parameters['approval'].toString();
    approvalFromNotif.value = notifParam == "true" ? true : false;
    String showStatusParam = Get.parameters['showStatus'].toString();
    showStatus = showStatusParam == "true" ? true : false;
    String withActionBackParam = Get.parameters['withActionBack'].toString();
    withActionBack = withActionBackParam == "true" ? true : false;

    detailSetUp();
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PegawaiKeluarga/History/$id',
      urlApiPost: () => '/api/PegawaiKeluarga/History/',
      urlApiPut: (id) => '/api/PegawaiKeluarga/History/$id',
      urlApiDelete: (id) =>
          '/api/PegawaiKeluarga/History?id=$id&idDivisi=${MahasConfig.selectedDivisi}',
      allowDelete: allowED.value,
      allowEdit: allowED.value,
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "id_PegawaiKeluarga": idPegawaiKontak.value,
        "id_HubunganKeluarga": hubunganKeluargaCon.value,
        "nama": namaCon.value,
        "jenisKelamin": jenisKelaminCon.value == 1 ? "Laki-laki" : "Perempuan",
        "alamatDomisili": alamatDomisiliCon.value,
        "noBPJS": noBpjsCon.value,
        "noBPJSTK": noBpjsTkCon.value,
        "ditanggungPegawai": ditanggungPegawaiCon.checked,
        "detailPegawaiJenisKontak": detailSetupCon.values
            .map((e) => {
                  "id_JenisKontak": e.value.idJeniskontak,
                  "nama": e.value.nama,
                })
            .toList()
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (!hubunganKeluargaCon.isValid) return false;
        if (!namaCon.isValid) return false;
        if (!jenisKelaminCon.isValid) return false;
        if (!alamatDomisiliCon.isValid) return false;
        if (!noBpjsCon.isValid) return false;
        if (!noBpjsTkCon.isValid) return false;

        return true;
      },
      apiToView: (json) {
        ProfilekeluargaModel model = ProfilekeluargaModel.fromJson(json);

        // component utama
        hubunganKeluargaCon.value = model.idHubungankeluarga;
        namaCon.value = model.nama;
        jenisKelaminCon.value = null;
        if (model.jeniskelamin!.isNotEmpty) {
          jenisKelaminCon.value = model.jeniskelamin == "Laki-laki" ? 1 : 2;
        }
        alamatDomisiliCon.value = model.alamatdomisili;
        noBpjsCon.value = model.nobpjs;
        noBpjsTkCon.value = model.nobpjstk;
        if (model.ditanggungpegawai == true) {
          ditanggungPegawaiCon.checked = true;
        } else {
          ditanggungPegawaiCon.checked = false;
        }
        detailSetupCon.clear();
        for (var e in model.detailkeluargakontak!) {
          detailSetupCon.addValue(
            ProfilekeluargaDetailkeluargakontakModel.init(
              e.idJeniskontak,
              e.nama,
              e.namajeniskontak,
            ),
          );
        }

        //display non mahas
        namaHRD.value = model.pegawaiapprovalnama ?? "";
        alasanTolakHRD.value = model.alasanTolak ?? "";
        namaPegawaiRequest.value = model.pegawaiNama ?? "";
        statusApprovalHRD = model.approval;
        tglPermintaan.value = model.tglpermintaan ?? DateTime.now();
        idPegawaiKontak.value = model.id ?? 0;
        reqDelete = model.status == "DELETE" ? true : false;
        status.value = model.status!;

        //cek approval for allowedit or delete
        cekApproval();

        //approval
        if (approvalFromNotif.value && statusApprovalHRD == null) {
          approvalButtonsVisible.value = true;
        }
      },
      onInit: () async {
        await getKontak();
      },
    );

    super.onInit();
  }

  //start chain get data
  Future<void> getKontak() async {
    EasyLoading.show();
    var r = await HttpApi.get("/api/JenisKontak");
    if (r.success) {
      RxList<JenisKontakModel> listModel = RxList<JenisKontakModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(JenisKontakModel.fromDynamic(e));
      }
      if (r.body != null && jenisKontakCon.items.isEmpty) {
        jenisKontakCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id, label: e.namalabel),
            )
            .toList();
      }
      jenisKontakCon.onChangedVoid = () {
        getLabel();
      };
    } else {
      Helper.fetchErrorMessage(r);
      EasyLoading.dismiss();
    }
    await getHubunganKeluarga();
  }

  void getLabel() {
    namaLabel.value = jenisKontakCon.label ?? "Nama";
  }

  Future<void> getHubunganKeluarga() async {
    var r = await HttpApi.get("/api/HubunganKeluarga");
    if (r.success) {
      RxList<HubunganKeluargaModel> listModel = RxList<HubunganKeluargaModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(HubunganKeluargaModel.fromDynamic(e));
      }
      if (r.body != null && hubunganKeluargaCon.items.isEmpty) {
        hubunganKeluargaCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      }
    } else {
      Helper.fetchErrorMessage(r);
    }
    EasyLoading.dismiss();
  }
  //end chain get data

  void detailSetUp() {
    detailSetupCon =
        InputDetailSetupControler<ProfilekeluargaDetailkeluargakontakModel>(
      itemKey: (e) => e.id,
      fromDynamic: (e) =>
          ProfilekeluargaDetailkeluargakontakModel.fromDynamic(e),
      itemText: (e) => e.nama ?? "-",
      onOpenForm: (id, e) {
        jenisKontakCon.value = e?.idJeniskontak;
        ketKontakCon.value = e?.nama;
        namaLabel.value = jenisKontakCon.label ?? "Nama";
      },
      onFormInsert: (id) {
        if (!jenisKontakCon.isValid) return null;
        if (!ketKontakCon.isValid) return null;
        var r = ProfilekeluargaDetailkeluargakontakModel();
        r.id = id;
        r.idJeniskontak = jenisKontakCon.value;
        r.namajeniskontak = jenisKontakCon.text;
        r.nama = ketKontakCon.value;

        return r;
      },
    );
  }

  //backAction
  Future<bool> _back() async {
    Get.until((route) => route.settings.name == Routes.PROFILE_KELUARGA);
    Get.toNamed(Routes.PROFILE_KELUARGA_HISTORY);
    return true;
  }

  //cek approval parameters come from notifikasi
  void cekApproval() {
    if (approvalFromNotif.value || statusApprovalHRD != null) {
      allowED.value = false;
    } else {
      allowED.value = true;
    }
    if (withActionBack) {
      formCon.backAction = () {
        return _back();
      };
      formCon.deleteOnTap = (id) {
        return _back();
      };
    }
    formCon.setState(() {
      reqDelete ? formCon.allowEdit = false : formCon.allowEdit = allowED.value;
      formCon.allowDelete = allowED.value;
      if (reqDelete) {
        formCon.deleteCaption = "Batal Hapus";
        formCon.deleteDescription =
            "Yakin akan membatalkan permintaan hapus data ini?";
      }
    });
  }

  Future approvalAction(bool approve) async {
    if (!alasanTolakCon.isValid) {
      Helper.dialogWarning("Alasan tidak boleh kosong");
    } else {
      if (EasyLoading.isShow) {
        EasyLoading.dismiss();
      }
      EasyLoading.show();
      String id = Get.parameters['id'].toString();
      final body = {
        'approve': approve,
        'alasanTolak': alasanTolakCon.value,
      };
      ApiResultModel? r;
      String url = '/api/PegawaiKeluarga/History/Approve/$id';
      r = await HttpApi.put(
        url,
        body: body,
      );
      if (r.success) {
        r = await HttpApi.get(
          '/api/PegawaiKeluarga/History/$id',
        );
        if (r.success) {
          ProfilekeluargaModel model = ProfilekeluargaModel.fromJson(r.body);
          statusApprovalHRD = model.approval;
          alasanTolakHRD.value = model.alasanTolak ?? "";
          approvalButtonsVisible.value = false;
          update();
        } else {
          Helper.fetchErrorMessage(r);
        }
      } else {
        Helper.fetchErrorMessage(r, httpMethod: HttpMethod.post);
      }
      EasyLoading.dismiss();
    }
  }
}
