import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';
import 'package:hr_portal/app/models/profile_riwayat_kesehatan_model.dart';

import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/others/list_component.dart';
import '../../../mahas/mahas_colors.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas_complement/mahas_colors.dart';
import '../controllers/profile_riwayat_kesehatan_controller.dart';

class ProfileRiwayatKesehatanView
    extends GetView<ProfileRiwayatKesehatanController> {
  const ProfileRiwayatKesehatanView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Riwayat Kesehatan'),
        centerTitle: true,
        actions: [
          GetBuilder<ProfileRiwayatKesehatanController>(
            builder: (controller) {
              return PopupMenuButton(
                onSelected: controller.popupMenuButtonOnSelected,
                itemBuilder: (context) {
                  List<PopupMenuItem<String>> r = [];
                  r.add(
                    const PopupMenuItem(
                      value: 'Tambah',
                      child: Text('Tambah'),
                    ),
                  );
                  if (MahasConfig.hasHistory) {
                    r.add(
                      const PopupMenuItem(
                        value: 'History',
                        child: Text('History'),
                      ),
                    );
                  }

                  return r;
                },
              );
            },
          ),
        ],
      ),
      body: ListComponent(
        separatorBuilder: (context, index, length) => const SizedBox(),
        controller: controller.listCon,
        itemBuilder: (RiwayatkesehatanModel e) {
          return Card(
            elevation: 3,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(MahasThemes.borderRadius)),
            child: ListTile(
              title: Text("Riwayat Kesehatan", style: MahasThemes.title),
              subtitle: Text("Nomor : ${e.nomor ?? "-"}"),
              leading: Container(
                color: MahasColors.primary,
                width: 8,
              ),
              minLeadingWidth: 0,
              minVerticalPadding: 0,
              horizontalTitleGap: 10,
              trailing: Text(
                MahasFormat.displayDate(e.tanggal),
                style: MahasColor.muted,
              ),
              onTap: () => controller.itemOnTab(e.id!),
              contentPadding: const EdgeInsets.symmetric(
                vertical: 0,
                horizontal: 10,
              ),
            ),
          );
        },
      ),
    );
  }
}
