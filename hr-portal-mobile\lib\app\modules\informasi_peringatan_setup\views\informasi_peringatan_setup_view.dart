import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../controllers/informasi_peringatan_setup_controller.dart';

class InformasiPeringatanSetupView
    extends GetView<InformasiPeringatanSetupController> {
  const InformasiPeringatanSetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      title: 'Peringatan',
      controller: controller.formCon,
      children: () => [
        InputTextComponent(
          label: "Judul",
          required: true,
          controller: controller.judulCon,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Peringatan",
          required: true,
          controller: controller.peringatanCon,
          editable: controller.formCon.editable,
        ),
        InputDatetimeComponent(
          label: "Tanggal",
          required: true,
          controller: controller.tanggalCon,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Keterangan",
          required: true,
          controller: controller.keteranganCon,
          editable: controller.formCon.editable,
        ),
        InputFileComponent(
          controller: controller.fileCon,
          label: "Berkas",
          editable: controller.formCon.editable,
          required: true,
        ),
      ],
    );
  }
}
