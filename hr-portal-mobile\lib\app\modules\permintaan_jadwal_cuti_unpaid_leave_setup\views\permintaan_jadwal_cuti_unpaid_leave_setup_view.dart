import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/components/inputs/input_lookup_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas_complement/status_request.dart';
import '../controllers/permintaan_jadwal_cuti_unpaid_leave_setup_controller.dart';

class PermintaanJadwalCutiUnpaidLeaveSetupView
    extends GetView<PermintaanJadwalCutiUnpaidLeaveSetupController> {
  const PermintaanJadwalCutiUnpaidLeaveSetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      title: "Izin Tanpa Ditanggung Perusahaan",
      controller: controller.formCon,
      children: () => [
        Visibility(
          visible: controller.formCon.isState != SetupPageState.create,
          child: Column(
            children: [
              Obx(() => Text(
                  "Tanggal/Waktu pengajuan : ${controller.tglPengajuan.value}")),
              const SizedBox(
                height: 10,
              ),
            ],
          ),
        ),
        controller.isKadiv == true &&
                controller.formCon.isState == SetupPageState.detail
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  InputTextComponent(
                    label: "Pegawai Request",
                    controller: controller.pegawaiCutiCon,
                    editable: false,
                    marginBottom: 5,
                  ),
                  InkWell(
                    onTap: () async => await Helper.fotoProfilOnTap(
                        controller.idPegawaiRequest.value),
                    child: SizedBox(
                      height: 30,
                      child: Text(
                        "Lihat Foto Profil Pegawai",
                        style: MahasThemes.link,
                        textAlign: TextAlign.left,
                      ),
                    ),
                  ),
                ],
              )
            : const SizedBox(),
        InputDatetimeComponent(
          label: "Tanggal Bekerja",
          controller: controller.tglKerjaCon,
          required: false,
          editable: false,
        ),
        InputTextComponent(
          label: "Masa Kerja",
          required: false,
          controller: controller.lamakerjaCon,
          editable: false,
        ),
        InputDatetimeComponent(
          label: "Dari Tanggal",
          controller: controller.dariTanggalCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDatetimeComponent(
          label: "Sampai Tanggal",
          controller: controller.sampaiTanggalCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Jumlah Hari",
          required: false,
          controller: controller.berapaHariCon,
          editable: false,
        ),
        InputTextComponent(
          label: "Alasan",
          required: true,
          controller: controller.alasanCon,
          editable: controller.formCon.editable,
        ),
        const Text(
          "Delegasikan kepada",
          textAlign: TextAlign.left,
        ),
        InputLookupComponent(
          label: "Nama Pegawai",
          required: true,
          controller: controller.staffPenggantiCon,
          editable: controller.formCon.editable,
          marginBottom: 5,
        ),
        InputTextComponent(
          label: "No Handphone",
          required: true,
          controller: controller.noHpCon,
          editable: controller.formCon.editable,
        ),
        InputFileComponent(
          controller: controller.fileCon,
          label: "Dokumen Handover",
          editable: controller.formCon.editable,
          required: false,
        ),
        const SizedBox(
          height: 20,
        ),
        Obx(
          () => ApprovalStatusContainer(
            isEditable: controller.formCon.editable,
            isVisible: controller.isVisible,
            isBatal: controller.isBatal,
            allowED: controller.allowED,
            isDinamisApprove: controller.isDinamisApprove,
            alasanTolakController: controller.alasanTolakCon,
            onTolakPressed: () {
              controller.approvalOnPress(false);
            },
            onTerimaPressed: () {
              controller.approvalOnPress(true);
            },
            batalOnPress: () {
              var status = controller.isBatal.isTrue ? 'batal' : 'tolak';
              alert(status);
            },
            pegawaiKadiv: controller.pegawaiKadiv.value,
            pegawaiManager: controller.pegawaiManager.value,
            accKadiv: controller.accKadiv.value,
            accManager: controller.accManager.value,
            statusTolak: controller.statusTolak.value,
            modelApproval: controller.modelApproval,
            isStop: controller.isStop.value,
          ),
        ),
      ],
    );
  }

  Future<dynamic> alert(String status) {
    return Get.dialog(
      AlertDialog(
        content: SizedBox(
          width: 1000,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              InputTextComponent(
                label: status == "tolak" ? "Alasan Ditolak" : "Alasan Batal",
                controller: controller.alasanTolakCon,
                required: true,
              ),
            ],
          ),
        ),
        contentPadding:
            const EdgeInsets.only(bottom: 0, top: 20, right: 10, left: 10),
        actionsPadding:
            const EdgeInsets.only(top: 0, bottom: 0, left: 10, right: 10),
        actions: [
          TextButton(
            child: Text(
              "Close",
              style: MahasThemes.muted,
            ),
            onPressed: () {
              Get.back(result: false);
            },
          ),
          TextButton(
            child: const Text(
              "Simpan",
            ),
            onPressed: () {
              if (controller.alasanTolakCon.isValid) {
                if (status == "tolak") {
                  controller.approvalOnPress(false);
                } else if (status == "batal") {
                  controller.approvalOnPress(false);
                }
                Get.back(result: true);
              }
            },
          ),
        ],
      ),
    );
  }
}
