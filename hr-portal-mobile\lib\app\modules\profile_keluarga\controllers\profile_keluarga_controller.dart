import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../models/profile_keluarga_model.dart';
import '../../../routes/app_pages.dart';

class ProfileKeluargaController extends GetxController {
  final listCon = ListComponentController<ProfilekeluargaModel>(
    urlApi: (index, filter) => '/api/PegawaiKeluarga/List?pageIndex=$index',
    fromDynamic: ProfilekeluargaModel.fromDynamic,
    allowSearch: false,
  );

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PROFILE_KELUARGA_SETUP,
      parameters: {
        'id': id.toString(),
      },
    )?.then((value) {
      listCon.refresh();
    });
  }

  void popupMenuButtonOnSelected(String v) async {
    if (v == 'Tambah') {
      Get.toNamed(Routes.PROFILE_KELUARGA_SETUP)?.then(
        (value) {
          listCon.refresh();
        },
      );
    } else if (v == 'History') {
      Get.toNamed(
        Routes.PROFILE_KELUARGA_HISTORY,
        parameters: {
          'showStatus': true.toString(),
        },
      )?.then(
        (value) {
          listCon.refresh();
        },
      );
    }
  }
}
