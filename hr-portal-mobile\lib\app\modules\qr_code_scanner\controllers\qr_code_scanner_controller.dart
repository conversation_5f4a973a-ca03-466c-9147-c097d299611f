import 'dart:convert';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/routes/app_pages.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

import '../../../mahas/services/http_api.dart';
import '../../../models/absensi_model.dart';

class AbsensiInfo {
  String? os;
  String? device;
  double? latitude;
  double? longitude;

  AbsensiInfo({
    this.os,
    this.device,
    this.latitude,
    this.longitude,
  });
}

class QrCodeScannerController extends GetxController {
  MobileScannerController cameraController = MobileScannerController(
    detectionSpeed: DetectionSpeed.noDuplicates,
  );
  String jenis = "serah_terima";
  String note = "Scan QR COde yang terdapat pada Halaman Mutasi Barang";
  AbsensiInfo absensiInfo = AbsensiInfo();

  @override
  void onInit() {
    if (Get.arguments != null) {
      jenis = Get.arguments["jenis"];
      if (jenis == "absenQR") {
        note = "Scan QR COde yang terdapat pada Halaman Absen";
        absensiInfo.device = Get.arguments["device"];
        absensiInfo.os = Get.arguments["os"];
        absensiInfo.latitude = Get.arguments["latitude"];
        absensiInfo.longitude = Get.arguments["longitude"];
      }
    }
    super.onInit();
  }

  void codeOnDetect(BarcodeCapture capture) {
    final String barcode = capture.barcodes.first.rawValue.toString();
    if (jenis == "serah_terima") {
      _serahTerima(barcode);
    } else if (jenis == "absenQR") {
      _absensiQr(barcode);
    }
  }

  void _serahTerima(String barcode) async {
    Get.offAndToNamed(
      Routes.INFORMASI_SERAH_TERIMA_MUTASI_BARANG_SETUP,
      parameters: {"id": barcode},
    );
  }

  void _absensiQr(String barcode) async {
    try {
      Map<String, dynamic> jsonMap = jsonDecode(barcode);
      AbsensiQrModel absensiQrModel = AbsensiQrModel.fromDynamic(jsonMap);
      if (absensiQrModel.berlaku != null) {
        if (absensiQrModel.berlaku!.isBefore(DateTime.now())) {
          Helper.dialogWarning(
            "QR Code yang anda scan sudah tidak berlaku",
          );
          return;
        }
        EasyLoading.show();
        var body = {
          "latitude": absensiInfo.latitude,
          "longitude": absensiInfo.longitude,
          "masuk": absensiQrModel.absensi == "masuk" ? true : false,
          "device": absensiInfo.device,
          "os": absensiInfo.os
        };
        var r = await HttpApi.post(
          "/api/Absensi/AbsensiQr",
          body: body,
        );
        if (r.success) {
          EasyLoading.dismiss();
          Get.back(
            result: absensiQrModel.absensi == "masuk" ? "true" : "false",
          );
          Helper.dialogSuccess(
            absensiQrModel.absensi == "masuk"
                ? "Absensi Datang Berhasil!"
                : "Absensi Pulang Berhasil!",
          );
        } else {
          EasyLoading.dismiss();
          Helper.dialogWarning(r.message);
        }
      }
    } catch (e) {
      Helper.dialogWarning(
          "QR Code yang anda scan tidak valid, Silakan dekatkan perangkat ke kode QR untuk pemindaian yang akurat");
    }
  }
}
