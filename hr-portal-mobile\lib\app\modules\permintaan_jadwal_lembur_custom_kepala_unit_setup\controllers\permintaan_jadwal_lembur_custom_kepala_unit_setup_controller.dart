import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_datetime_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_lookup_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_text_component.dart';
import 'package:hr_portal/app/mahas/components/pages/setup_page_component.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:hr_portal/app/mahas/models/api_result_model.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/mahas/services/http_api.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';
import 'package:hr_portal/app/mahas_complement/helper.dart';
import 'package:hr_portal/app/models/lembur_model.dart';
import 'package:hr_portal/app/models/pegawai_approval.dart';
import 'package:hr_portal/app/models/penugasan_lembur_model.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

import '../../../models/absensi_model.dart';

class PermintaanJadwalLemburCustomKepalaUnitSetupController
    extends GetxController {
  late SetupPageController formCon;
  final staffLemburCon = InputLookupController<LookUpPenugasanLemburModel>(
    fromDynamic: (e) => LookUpPenugasanLemburModel.fromDynamic(e),
    itemText: (e) => e.nama ?? "",
    itemValue: (e) => e.id,
    urlApi: (index, filter) =>
        '/api/PenugasanLembur/Pegawai?pageIndex=$index&filter=$filter&id_divisi=${MahasConfig.selectedDivisi}',
  );
  final tanggalMulaiCon = InputDatetimeController();
  final jamMulaiCon = InputDatetimeController();
  final lamaHariCon = InputTextController(type: InputTextType.number);
  final jamCon = InputTextController(type: InputTextType.timeHour);
  final menitCon = InputTextController(type: InputTextType.timeMinutes);
  final keteranganLemburCon = InputTextController(type: InputTextType.paragraf);
  final keteranganLemburPegawaiCon =
      InputTextController(type: InputTextType.paragraf);
  final alasanTolakCon = InputTextController();
  late String approve;
  RxInt idPegawaiRequest = 0.obs;
  bool isViewPegawai = Get.arguments["pegawai"];
  RxBool isKadiv = false.obs;
  late bool allowED;
  Rxn<bool> accKadiv = Rxn<bool>();
  Rxn<bool> accManager = Rxn<bool>();
  RxBool isVisible = false.obs;
  late RxString pegawaiManager = ''.obs;
  RxString alasanTolak = ''.obs;
  RxBool isBatal = false.obs;
  // final isDinamisApprove = MahasConfig.profileMenu.approvalDinamis;
  RxBool isStop = false.obs;
  RxList<PegawaiApprovalModel> modelApproval = <PegawaiApprovalModel>[].obs;
  int? id;
  RxString urlGambar = "".obs;
  List<Absen> listAbsensiLembur = [];
  Rxn<DateTime> jamMasukAbsensiLembur = Rxn<DateTime>();
  Rxn<DateTime> jamKeluarAbsensiLembur = Rxn<DateTime>();

  @override
  void onInit() {
    cekApproval();
    staffLemburCon.onChanged =
        () => idPegawaiRequest.value = staffLemburCon.value!.id!;
    formCon = SetupPageController(
      allowEdit: true,
      allowDelete: isKadiv.value,
      urlApiGet: (id) => '/api/PenugasanLembur/$id',
      urlApiPost: () => '/api/PenugasanLembur',
      urlApiPut: (id) => '/api/PenugasanLembur/$id',
      urlApiDelete: (id) => '/api/PermintaanJadwal/Lembur/$id',
      bodyApi: (id) => {
        "id_Pegawai": staffLemburCon.value!.id,
        "id_Divisi": MahasConfig.selectedDivisi,
        "tanggalJam": MahasFormat.dateTimeOfDayToString(
            tanggalMulaiCon.value, jamMulaiCon.value),
        "lamaHari": lamaHariCon.value,
        "lamaJam": jamCon.value,
        "lamaMenit": menitCon.value,
        "keterangan": keteranganLemburCon.value,
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (!staffLemburCon.isValid) return false;
        if (!tanggalMulaiCon.isValid) return false;
        if (!jamMulaiCon.isValid) return false;
        if (!lamaHariCon.isValid) return false;
        if (!jamCon.isValid) return false;
        if (!menitCon.isValid) return false;
        if (!keteranganLemburCon.isValid) return false;

        return true;
      },
      apiToView: (json) {
        PenugasanLemburModel model = PenugasanLemburModel.fromJson(json);
        id = model.id;
        urlGambar.value = model.urlReport ?? "";
        staffLemburCon.value =
            LookUpPenugasanLemburModel.init(model.idPegawai, model.namaPegawai);
        tanggalMulaiCon.value = model.tanggalLembur;
        jamMulaiCon.value = model.jamLembur;
        lamaHariCon.value = model.lamaHari;
        jamCon.value = model.lamaJam;
        menitCon.value = model.lamaMenit;
        keteranganLemburCon.value = model.keterangan;

        pegawaiManager.value = model.namaPegawaiKadiv ?? "";
        accManager.value = model.approve;
        accKadiv.value = true;
        idPegawaiRequest.value = model.idPegawai ?? 0;

        alasanTolak.value = model.alasanTolak ?? '';
        keteranganLemburPegawaiCon.value = model.keteranganLembur;

        if (allowED == false) {
          if ((model.approve == null &&
              model.idPegawaiKadiv == MahasConfig.profile!.id)) {
            isVisible.value = true;
          }
        }

        // batal
        if (MahasConfig.dinamisForm.approval?.bisaBatal == true) {
          if (allowED == false) {
            if (model.approve == true &&
                model.idPegawaiKadiv == MahasConfig.profile!.id) {
              isBatal.value = false;
            }
          }
        }
        isKadiv.value = model.idPegawaiKadiv == MahasConfig.profile!.id;

        if (isViewPegawai) {
          getDataAbsenLembur();
        }

        formCon.allowEdit = isKadiv.value;

        if (model.approve == true) {
          formCon.allowEdit = false;
        }
      },
      onInit: () {
        formCon.editable = false;
      },
    );

    super.onInit();
  }

  Future<void> approvalOnPress(bool approve) async {
    var id = Get.parameters['id'].toString();
    EasyLoading.show();
    var action = await HttpApi.put(
      '/api/PenugasanLembur/Approve/$id',
      body: {
        'id': id,
        'id_Pegawai': idPegawaiRequest.value,
        'approve': approve,
        'alasanTolak': alasanTolakCon.value,
      },
    );
    if (action.success) {
      final url = '/api/PenugasanLembur/$id';
      ApiResultModel r;
      r = await HttpApi.get(url);
      if (r.success) {
        PenugasanLemburModel model = PenugasanLemburModel.fromJson(r.body);
        accManager.value = model.approve;
        alasanTolak.value = model.alasanTolak ?? '';
        isVisible.value = false;
        formCon.updateSetState(() {
          formCon.allowEdit = false;
        });
        update();
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan approve, silahkan cek koneksi internet");
    } else if (!action.success) {
      Helper.dialogWarning(
        "Gagal melakukan approval",
        resMassage: action.message,
        noInternetMassage:
            "Gagal melakukan approval, silahkan cek koneksi internet",
      );
    }
    EasyLoading.dismiss();
  }

  Future<void> batalOnPress() async {
    var id = Get.parameters['id'].toString();
    String urlManager = '/api/PermintaanJadwal/Lembur/Batal/Manager/';
    var action = await Helper.batalAction(
      id,
      alasanTolakCon.value,
      urlManager,
    );
    if (action.success) {
      final url = '/api/PermintaanJadwal/Lembur/$id';
      ApiResultModel r;
      r = await HttpApi.get(url);
      if (action.success) {
        LemburModel model = LemburModel.fromJson(r.body);
        accManager.value = model.approvemanager;
        alasanTolak.value = model.alasanTolak ?? '';
        isBatal.value = false;
        update();
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan aksi, silahkan cek koneksi internet");
    } else if (!action.success) {
      Helper.dialogWarning(
        "Gagal melakukan aksi",
        resMassage: action.message,
        noInternetMassage:
            "Gagal melakukan aksi, silahkan cek koneksi internet",
      );
    }
  }

  void cekApproval() {
    approve = Get.parameters['approval'].toString();
    if (approve == "approval") {
      allowED = false;
    } else {
      allowED = true;
    }
  }

  void suratPenugasanOnTap() {
    HelperComp.dialogFullScreen(
      Expanded(
        child: SfPdfViewer.network(
          urlGambar.value,
          onDocumentLoadFailed: (details) {
            showErrorDialog(details.error, details.description);
          },
        ),
      ),
    );
  }

  void showErrorDialog(String error, String description) {
    Get.back();
    Helper.dialogWarning("Tidak Dapat Membuka File Ini");
  }

  Future<void> getDataAbsenLembur() async {
    var r = await HttpApi.get('/api/PenugasanLembur/AbsensiLembur/$id');
    if (r.success) {
      List<dynamic> datas = json.decode(r.body);
      if (listAbsensiLembur.isNotEmpty) {
        listAbsensiLembur.clear();
      }
      for (var obj in datas) {
        listAbsensiLembur.add(Absen.fromJson(obj));
      }
      for (var i = 0; i < listAbsensiLembur.length; i++) {
        if (listAbsensiLembur[i].status == "Keluar") {
          jamKeluarAbsensiLembur.value = listAbsensiLembur[i].tanggalJam;
        } else if (listAbsensiLembur[i].status == "Masuk") {
          jamMasukAbsensiLembur.value = listAbsensiLembur[i].tanggalJam;
        }
      }
    } else if (r.message!.contains(RegExp('A network error'))) {
      Helper.dialogWarning(
          "Tidak ada koneksi internet, coba beberapa saat lagi!");
    } else if (r.message!.contains(RegExp('failed host lookup'))) {
      Helper.dialogWarning(
          "Tidak ada koneksi internet, coba beberapa saat lagi!");
    } else if (r.message!.contains(RegExp('unexpected end of stream'))) {
      Helper.dialogWarning(
          "Koneksi internet Anda terlalu lambat, coba beberapa saat lagi!");
    }
  }
}
