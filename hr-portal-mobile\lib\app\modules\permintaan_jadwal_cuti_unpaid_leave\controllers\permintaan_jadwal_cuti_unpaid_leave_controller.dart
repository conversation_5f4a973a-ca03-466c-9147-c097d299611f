import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../models/cuti_unpaid_leave_model.dart';
import '../../../models/divisi_model.dart';
import '../../../routes/app_pages.dart';

class PermintaanJadwalCutiUnpaidLeaveController extends GetxController {
  final listCon = ListComponentController<CutiUnpaiLeaveModel>(
    urlApi: (index, filter) =>
        '/api/PermintaanJadwal/CutiUnpaidLeave/List?pageIndex=$index',
    fromDynamic: CutiUnpaiLeaveModel.fromDynamic,
    allowSearch: false,
  );

  List<DivisiModel>? listDivisi;
  RxBool isKadiv = false.obs;
  bool isDinamis = MahasConfig.profileMenu.approvalDinamis;

  void addOnPress() {
    Get.toNamed(Routes.PERMINTAAN_JADWAL_CUTI_UNPAID_LEAVE_SETUP)
        ?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }

  itemOnTab(int id) {
    Get.toNamed(
      Routes.PERMINTAAN_JADWAL_CUTI_UNPAID_LEAVE_SETUP,
      parameters: {'id': id.toString(), 'isKadiv': isKadiv.value.toString()},
    )?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }

  @override
  void onInit() {
    getDivisi();
    super.onInit();
  }

  getDivisi() async {
    var r = await HttpApi.get('/api/Profile/Divisi');
    if (r.success) {
      listDivisi = divisiModelFromJson(r.body);
      for (var i = 0; i < listDivisi!.length; i++) {
        if (MahasConfig.selectedDivisi == listDivisi![i].id) {
          if (MahasConfig.profile!.nama == listDivisi![i].kadiv ||
              MahasConfig.profile!.nama == listDivisi![i].manager) {
            isKadiv.value = true;
          }
        }
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning("Gagal memuat data, silahkan cek koneksi internet");
    } else {
      Helper.dialogWarning(r.message);
    }
  }

  void getDataApproval() {
    listCon.refresh();
  }
}
