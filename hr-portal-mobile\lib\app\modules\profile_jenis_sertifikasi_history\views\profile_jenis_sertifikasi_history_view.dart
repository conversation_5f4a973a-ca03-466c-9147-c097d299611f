import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/models/profile_sertifikasi_model.dart';

import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/others/list_component.dart';
import '../../../mahas/mahas_colors.dart';
import '../controllers/profile_jenis_sertifikasi_history_controller.dart';

class ProfileJenisSertifikasiHistoryView
    extends GetView<ProfileJenisSertifikasiHistoryController> {
  const ProfileJenisSertifikasiHistoryView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('History Jenis <PERSON>'),
        centerTitle: true,
      ),
      body: ListComponent<SertifikasiModel>(
        controller: controller.listCon,
        itemBuilder: (e) {
          return ListTile(
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text("Jenis Sertifikasi : ${e.namasertifikasi ?? "-"}",
                        style: MahasThemes.title),
                    Text(
                        e.approved == null
                            ? "Menunggu"
                            : e.approved == false
                                ? "Ditolak"
                                : "Diterima",
                        style: MahasThemes.muted)
                  ],
                ),
                Text("Nama Sertifikasi : ${e.nama ?? "-"}"),
              ],
            ),
            subtitle: e.status == "ADD"
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const Icon(
                        FontAwesomeIcons.plus,
                        size: 15,
                        color: MahasColors.green,
                      ),
                      const SizedBox(
                        width: 5,
                      ),
                      Text(
                        "Tambah",
                        style: MahasThemes.muted
                            .copyWith(color: MahasColors.green),
                      ),
                    ],
                  )
                : e.status == "UPDATE"
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Icon(
                            FontAwesomeIcons.penToSquare,
                            size: 15,
                            color: MahasColors.warning,
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          Text(
                            "Ubah",
                            style: MahasThemes.muted
                                .copyWith(color: MahasColors.warning),
                          ),
                        ],
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Icon(
                            FontAwesomeIcons.trash,
                            size: 15,
                            color: MahasColors.danger,
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          Text(
                            "Hapus",
                            style: MahasThemes.muted
                                .copyWith(color: MahasColors.danger),
                          ),
                        ],
                      ),
            onTap: () => controller.itemOnTab(e.id!),
          );
        },
      ),
    );
  }
}
