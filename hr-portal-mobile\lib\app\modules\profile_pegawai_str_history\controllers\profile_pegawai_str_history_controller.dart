import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../models/profile_pegawai_str_model.dart';
import '../../../routes/app_pages.dart';

class ProfilePegawaiStrHistoryController extends GetxController {
  final listCon = ListComponentController<PegawaistrModel>(
    urlApi: (index, filter) => '/api/PegawaiStr/History/List?pageIndex=$index',
    fromDynamic: PegawaistrModel.fromDynamic,
    allowSearch: false,
  );

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PROFILE_PEGAWAI_STR_HISTORY_SETUP,
      parameters: {
        'id': id.toString(),
        'showStatus': true.toString(),
      },
    )?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }
}
