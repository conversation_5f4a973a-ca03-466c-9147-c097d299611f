import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../models/profile_riwayat_kesehatan_model.dart';
import '../../../routes/app_pages.dart';

class ProfileRiwayatKesehatanHistoryController extends GetxController {
  final listCon = ListComponentController<RiwayatkesehatanModel>(
    urlApi: (index, filter) =>
        '/api/PegawaiRiwayatKesehatan/History/List?pageIndex=$index',
    fromDynamic: RiwayatkesehatanModel.fromDynamic,
    allowSearch: false,
  );

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PROFILE_RIWAYAT_KESEHATAN_HISTORY_SETUP,
      parameters: {
        'id': id.toString(),
        'showStatus': true.toString(),
      },
    )?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }
}
