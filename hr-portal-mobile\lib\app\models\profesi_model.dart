import 'dart:convert';

import '../mahas/services/mahas_format.dart';

class ProfesiModel {
  int? id;
  String? nama;

  ProfesiModel();

  static ProfesiModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static ProfesiModel fromDynamic(dynamic dynamicData) {
    final model = ProfesiModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.nama = dynamicData['nama'];

    return model;
  }
}
