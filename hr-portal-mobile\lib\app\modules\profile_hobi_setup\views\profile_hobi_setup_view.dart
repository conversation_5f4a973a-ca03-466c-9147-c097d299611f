import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../controllers/profile_hobi_setup_controller.dart';

class ProfileHobiSetupView extends GetView<ProfileHobiSetupController> {
  const ProfileHobiSetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      controller: controller.formCon,
      title: "<PERSON><PERSON>",
      children: () => [
        InputTextComponent(
          label: "<PERSON><PERSON>",
          controller: controller.hobiCon,
          required: true,
          editable: controller.formCon.editable,
        ),
      ],
    );
  }
}
