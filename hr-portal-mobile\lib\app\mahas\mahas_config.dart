import 'package:hr_portal/app/constant/environment_constant.dart';
import 'package:hr_portal/app/models/profile_model.dart';
import 'package:hr_portal/app/models/update_app_values_model.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../models/form_dinamis_model.dart';
import '../models/kadiv_manager_model.dart';
import '../models/menu_profile_model.dart';

class MahasConfig {
  static PackageInfo? packageInfo;
  static MahasEnvironmentType currentEnv = MahasEnvironmentType.devsanata;
  static UpdateappvaluesModel updateAppValues = UpdateappvaluesModel();
  static String appName = "";
  static int? selectedDivisi;
  static ProfileModel? profile;
  static KadivmanagerModel kadivmanager = KadivmanagerModel();
  static AtasanModel atasan = AtasanModel();
  static DinamisFormModel dinamisForm = DinamisFormModel();
  static String urlApi = '';
  static bool? hasInternet;
  static bool apiStaging = false;
  static bool updateRequired = false;
  // static String urlApi = 'https://localhost:7159';
  static bool isLaravelBackend = false;
  static bool demoLogin = false;
  static bool hasHistory = true;
  static ProfileMenuModel profileMenu = ProfileMenuModel.getEmpty();
  static List<String> noInternetErrorMessage = [
    'A network error',
    'failed host lookup',
    'user was not linked',
    'unexpected end of stream',
    'network_error',
    'connection failed',
    'clientexception',
    'socketexception',
  ];
}
