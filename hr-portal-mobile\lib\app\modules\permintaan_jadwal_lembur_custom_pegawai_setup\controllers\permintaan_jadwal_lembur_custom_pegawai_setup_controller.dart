import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/others/list_component.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:hr_portal/app/mahas/models/api_list_resut_model.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/mahas/services/http_api.dart';
import 'package:hr_portal/app/models/divisi_model.dart';
import 'package:hr_portal/app/models/penugasan_lembur_model.dart';
import 'package:hr_portal/app/routes/app_pages.dart';

import '../../approval/controllers/approval_controller.dart';

class PermintaanJadwalLemburCustomPegawaiSetupController
    extends GetxController {
  final listCon = ListComponentController<PenugasanLemburModel>(
    urlApi: (index, filter) => '/api/PenugasanLembur/List?pageIndex=$index',
    fromDynamic: PenugasanLemburModel.fromDynamic,
    allowSearch: false,
  );

  RxInt selectedIndex = 0.obs;
  final List<MenuItemModel> menus = [];
  RxList<PenugasanLemburModel> listData = <PenugasanLemburModel>[].obs;
  List<DivisiModel>? listDivisi;
  RxBool isKadiv = false.obs;
  RxBool isLoading = true.obs;
  RxBool isEmpty = false.obs;
  RxList<PenugasanLemburModel> listModel = RxList<PenugasanLemburModel>();
  var model = Rxn<PenugasanLemburModel>();

  @override
  void onInit() {
    getDivisi();
    onRefresh();
    menus.add(MenuItemModel('Menunggu', listMenunggu));
    menus.add(MenuItemModel('Diterima', listDiterima));
    menus.add(MenuItemModel('Ditolak', listDitolak));
    super.onInit();
  }

  getDivisi() async {
    var r = await HttpApi.get('/api/Profile/Divisi');
    if (r.success) {
      listDivisi = divisiModelFromJson(r.body);
      for (var i = 0; i < listDivisi!.length; i++) {
        if (MahasConfig.selectedDivisi == listDivisi![i].id) {
          if (MahasConfig.profile!.nama == listDivisi![i].kadiv ||
              MahasConfig.profile!.nama == listDivisi![i].manager) {
            isKadiv.value = true;
          }
        }
      }
    }
  }

  void itemOnTab(int id) {
    Get.toNamed(Routes.PERMINTAAN_JADWAL_LEMBUR_CUSTOM_KEPALA_UNIT_SETUP,
        parameters: {"approve": "approve", "id": id.toString()},
        arguments: {"pegawai": true})?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }

  Future<void> onRefresh() async {
    listModel.clear();
    var r = await HttpApi.get('/api/PenugasanLembur/List');
    if (r.success) {
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(PenugasanLemburModel.fromDynamic(e));
      }
      isEmpty.value = false;
    } else {
      Helper.fetchErrorMessage(r, httpMethod: HttpMethod.get);
      isEmpty.value = true;
    }
    if (selectedIndex.value == 0) {
      listMenunggu();
    } else if (selectedIndex.value == 1) {
      listDiterima();
    } else {
      listDitolak();
    }
    isLoading.value = false;
  }

  void listMenunggu() {
    if (listModel.isEmpty) {
      return;
    }
    listData.clear();
    if (listModel.isNotEmpty) {
      listData.addAll(listModel.where((e) => e.approve == null).toList());
    }
  }

  void listDiterima() {
    if (listModel.isEmpty) {
      return;
    }
    listData.clear();
    if (listModel.isNotEmpty) {
      listData.addAll(listModel.where((e) => e.approve == true).toList());
    }
  }

  void listDitolak() {
    if (listModel.isEmpty) {
      return;
    }
    listData.clear();
    if (listModel.isNotEmpty) {
      listData.addAll(listModel.where((e) => e.approve == false).toList());
    }
  }
}
