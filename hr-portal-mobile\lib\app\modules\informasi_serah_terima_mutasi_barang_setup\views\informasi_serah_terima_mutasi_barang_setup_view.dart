import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/others/empty_component.dart';
import 'package:hr_portal/app/mahas/components/others/shimmer_component.dart';
import 'package:hr_portal/app/mahas/mahas_colors.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

import '../controllers/informasi_serah_terima_mutasi_barang_setup_controller.dart';

class InformasiSerahTerimaMutasiBarangSetupView
    extends GetView<InformasiSerahTerimaMutasiBarangSetupController> {
  const InformasiSerahTerimaMutasiBarangSetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: controller.backAction,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('<PERSON><PERSON><PERSON>'),
          centerTitle: true,
          actions: [
            InkWell(
              onTap: controller.showQRCode,
              child: const Padding(
                padding: EdgeInsets.symmetric(horizontal: 10),
                child: Icon(
                  Icons.qr_code_2_rounded,
                  size: 30,
                  color: MahasColors.light,
                ),
              ),
            )
          ],
        ),
        body: Obx(
          () => controller.isLoadingData.value
              ? const ShimmerComponent()
              : SafeArea(
                  child: Column(
                    children: [
                      Expanded(
                        child: controller.mutasiBarangModel.value.urlFaktur ==
                                    null ||
                                controller
                                    .mutasiBarangModel.value.urlFaktur!.isEmpty
                            ? const EmptyComponent()
                            : SfPdfViewer.network(
                                controller.mutasiBarangModel.value.urlFaktur ??
                                    "",
                                onDocumentLoadFailed: (error) {
                                  Helper.dialogWarning(
                                      "Terjadi kesalahan\nTidak dapat memuat dokumen");
                                },
                              ),
                      ),
                      Visibility(
                        visible: controller.buttonApproveVisible.value,
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          width: Get.width,
                          child: ElevatedButton(
                            onPressed: controller.approveOnTap,
                            child: const Text("Terima"),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
        ),
      ),
    );
  }
}
