import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../models/profile_sertifikasi_model.dart';
import '../../../routes/app_pages.dart';

class ProfileJenisSertifikasiHistoryController extends GetxController {
  final listCon = ListComponentController<SertifikasiModel>(
    urlApi: (index, filter) =>
        '/api/PegawaiSertifikasi/History/List?pageIndex=$index',
    fromDynamic: SertifikasiModel.fromDynamic,
    allowSearch: false,
  );

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PROFILE_JENIS_SERTIFIKASI_HISTORY_SETUP,
      parameters: {
        'id': id.toString(),
        'showStatus': true.toString(),
      },
    )?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }
}
