import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_detail_component.dart';
import '../../../mahas/components/inputs/input_detail_setup_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas_complement/status_request.dart';
import '../../../models/cuti_day_payment_model.dart';
import '../controllers/permintaan_jadwal_cuti_day_payment_setup_controller.dart';

class PermintaanJadwalCutiDayPaymentSetupView
    extends GetView<PermintaanJadwalCutiDayPaymentSetupController> {
  const PermintaanJadwalCutiDayPaymentSetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      title: "Cuti Day Payment",
      controller: controller.formCon,
      childrenPadding: false,
      children: () => [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 0),
          child: Column(
            children: [
              Visibility(
                visible: controller.formCon.isState != SetupPageState.create,
                child: Column(
                  children: [
                    Obx(() => Text(
                        "Tanggal/Waktu pengajuan : ${controller.tglPengajuan.value}")),
                    const SizedBox(
                      height: 10,
                    ),
                  ],
                ),
              ),
              controller.isKadiv == true &&
                      controller.formCon.isState == SetupPageState.detail
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        InputTextComponent(
                          label: "Pegawai Request",
                          controller: controller.pegawaiCutiCon,
                          editable: false,
                          marginBottom: 5,
                        ),
                        InkWell(
                          onTap: () async => await Helper.fotoProfilOnTap(
                              controller.idPegawaiRequest.value),
                          child: SizedBox(
                            height: 30,
                            child: Text(
                              "Lihat Foto Profil Pegawai",
                              style: MahasThemes.link,
                              textAlign: TextAlign.left,
                            ),
                          ),
                        ),
                      ],
                    )
                  : const SizedBox(),
              InputDropdownComponent(
                label: "Jenis Pengganti DP",
                controller: controller.jenisCon,
                required: true,
                editable: controller.formCon.editable,
              ),
              InputTextComponent(
                label: "Keterangan",
                required: true,
                controller: controller.keteranganCon,
                editable: controller.formCon.editable,
              ),
              InputDropdownComponent(
                label: "Shift Untuk DP",
                controller: controller.shiftGantiCon,
                required: true,
                editable: controller.formCon.editable,
              ),
            ],
          ),
        ),
        controller.formCon.isState == SetupPageState.detail
            ? InputDetailComponent(
                label: "Tanggal DP",
                editable: false,
                controller: controller.detailApproveCon,
              )
            : InputDetailSetupComponent<CutiDayPaymentDetailModel>(
                label: "Tanggal DP",
                controller: controller.detailSetupCon,
                editable: controller.formCon.editable,
                required: true,
                formBuilder: (e) => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // form detail
                    InputDatetimeComponent(
                      label: "Tanggal DP",
                      controller: controller.tanggalCon,
                      required: true,
                      editable: controller.formCon.editable,
                    ),
                    Obx(() => Visibility(
                          visible: controller.shiftModel.value?.id != null,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Shift : ${controller.shiftModel.value?.shift}",
                              ),
                              Text(
                                "Jam Masuk : ${MahasFormat.timeToString(controller.shiftModel.value?.jammulai) ?? '-'}",
                              ),
                              Text(
                                "Jam Keluar : ${MahasFormat.timeToString(controller.shiftModel.value?.jamselesai) ?? '-'}",
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                            ],
                          ),
                        ))
                  ],
                ),
              ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 0),
          child: Column(
            children: [
              InputTextComponent(
                label: "Jumlah Hari",
                required: false,
                controller: controller.berapaHariCon,
                editable: false,
              ),
            ],
          ),
        ),
        controller.formCon.isState == SetupPageState.detail
            ? InputDetailComponent(
                label: "Tanggal Pengganti",
                editable: false,
                controller: controller.detailPenggantiApproveCon,
              )
            : InputDetailSetupComponent<CutiDayPaymentDetailModel>(
                label: "Tanggal Pengganti",
                controller: controller.detailPenggantiSetupCon,
                editable: controller.formCon.editable,
                required: true,
                formBuilder: (e) => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // form detail
                    InputDatetimeComponent(
                      label: "Tanggal Pengganti",
                      controller: controller.tanggalPenggantiCon,
                      required: true,
                      editable: controller.formCon.editable,
                    ),
                    Obx(() => Visibility(
                          visible: controller.shiftBaseModel.value?.id != null,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Shift : ${controller.shiftBaseModel.value?.shift}",
                              ),
                              Text(
                                "Jam Masuk : ${MahasFormat.timeToString(controller.shiftBaseModel.value?.jammulai) ?? '-'}",
                              ),
                              Text(
                                "Jam Keluar : ${MahasFormat.timeToString(controller.shiftBaseModel.value?.jamselesai) ?? '-'}",
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                            ],
                          ),
                        ))
                  ],
                ),
              ),
        Padding(
          padding: const EdgeInsets.all(10.0),
          child: InputTextComponent(
            label: "Jumlah Hari Pengganti",
            required: false,
            controller: controller.berapaHariPenggantiCon,
            editable: false,
          ),
        ),
        Obx(() => ApprovalStatusContainer(
              padding: 10,
              isEditable: controller.formCon.editable,
              isVisible: controller.isVisible,
              isBatal: controller.batal,
              allowED: controller.allowED,
              isDinamisApprove: controller.isDinamisApprove,
              alasanTolakController: controller.alasanTolakCon,
              onTolakPressed: () {
                controller.approvalOnPress(false);
              },
              onTerimaPressed: () {
                controller.approvalOnPress(true);
              },
              batalOnPress: () {
                var status = controller.batal.isTrue ? 'batal' : 'tolak';
                alert(status);
              },
              pegawaiKadiv: controller.pegawaiKadiv.value,
              pegawaiManager: controller.pegawaiManager.value,
              accKadiv: controller.accKadiv.value,
              accManager: controller.accManager.value,
              statusTolak: controller.statusTolak.value,
              modelApproval: controller.modelApproval,
              isStop: controller.isStop.value,
            )),
      ],
    );
  }

  Future<dynamic> alert(String status) {
    return Get.dialog(
      AlertDialog(
        content: SizedBox(
          width: 1000,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              InputTextComponent(
                label: status == "tolak" ? "Alasan Ditolak" : "Alasan Batal",
                controller: controller.alasanTolakCon,
                required: true,
              ),
            ],
          ),
        ),
        contentPadding:
            const EdgeInsets.only(bottom: 0, top: 20, right: 10, left: 10),
        actionsPadding:
            const EdgeInsets.only(top: 0, bottom: 0, left: 10, right: 10),
        actions: [
          TextButton(
            child: Text(
              "Close",
              style: MahasThemes.muted,
            ),
            onPressed: () {
              Get.back(result: false);
            },
          ),
          TextButton(
            child: const Text(
              "Simpan",
            ),
            onPressed: () {
              if (controller.alasanTolakCon.isValid) {
                if (status == "tolak") {
                  controller.approvalOnPress(false);
                } else if (status == "batal") {
                  controller.approvalOnPress(false);
                }
                Get.back(result: true);
              }
            },
          ),
        ],
      ),
    );
  }
}
