import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../controllers/profile_seragam_setup_controller.dart';

class ProfileSeragamSetupView extends GetView<ProfileSeragamSetupController> {
  const ProfileSeragamSetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      controller: controller.formCon,
      title: "Seragam Pegawai",
      children: () => [
        InputDropdownComponent(
          label: "Nama <PERSON>agam",
          controller: controller.jenisSeragamCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Kode Inventory",
          controller: controller.kodeInventoryCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Ukuran",
          controller: controller.ukuranCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Jumlah",
          controller: controller.jumlahCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDatetimeComponent(
          label: "Tanggal terima",
          controller: controller.tglTerimaCon,
          required: true,
          editable: controller.formCon.editable,
        ),
      ],
    );
  }
}
