import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class SertifikasiModel {
  int? id;
  int? idPegawaisertifikasi;
  int? idPegawai;
  String? pegawainama;
  int? idJenissertifikasi;
  String? namasertifikasi;
  String? nama;
  String? nosertifikasi;
  DateTime? tanggal;
  DateTime? berlakusampai;
  String? berkaspendukung;
  int? idPegawaiapproval;
  String? pegawaiapprovalnama;
  bool? approved;
  String? alasantolak;
  DateTime? tanggalapproval;
  DateTime? tanggalrequest;
  String? status;

  SertifikasiModel();

  static SertifikasiModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static SertifikasiModel fromDynamic(dynamic dynamicData) {
    final model = SertifikasiModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idPegawaisertifikasi =
        MahasFormat.dynamicToInt(dynamicData['id_PegawaiSertifikasi']);
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.pegawainama = dynamicData['pegawaiNama'];
    model.idJenissertifikasi =
        MahasFormat.dynamicToInt(dynamicData['id_JenisSertifikasi']);
    model.namasertifikasi = dynamicData['namaSertifikasi'];
    model.nama = dynamicData['nama'];
    model.nosertifikasi = dynamicData['noSertifikasi'];
    model.tanggal = MahasFormat.dynamicToDateTime(dynamicData['tanggal']);
    model.berlakusampai =
        MahasFormat.dynamicToDateTime(dynamicData['berlakuSampai']);
    model.berkaspendukung = dynamicData['berkasPendukung'];
    model.idPegawaiapproval =
        MahasFormat.dynamicToInt(dynamicData['id_PegawaiApproval']);
    model.pegawaiapprovalnama = dynamicData['pegawaiApprovalNama'];
    model.approved = MahasFormat.dynamicToBool(dynamicData['approved']);
    model.alasantolak = dynamicData['alasanTolak'];
    model.tanggalapproval =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalApproval']);
    model.tanggalrequest =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalRequest']);
    model.status = dynamicData['status'];

    return model;
  }
}
