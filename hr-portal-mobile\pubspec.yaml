name: hr_portal
version: 4.0.18+37
publish_to: none
description: A new Flutter project.
environment:
  sdk: ">=3.0.6 <=4.0.0"

dependencies:
  cupertino_icons: ^1.0.2
  geolocator: ^12.0.0
  get: ^4.6.6
  firebase_core: ^3.8.1
  google_sign_in: ^6.3.0
  sign_in_with_apple: ^6.1.0
  crypto: ^3.0.2
  firebase_auth: ^5.3.4
  firebase_messaging: ^15.1.6
  firebase_remote_config: ^5.2.0
  firebase_analytics: ^11.3.6
  flutter_local_notifications: ^18.0.1
  http: ^1.1.0
  get_storage: ^2.0.3
  device_info_plus: ^10.1.2
  package_info_plus: ^8.0.0
  google_fonts: ^6.1.0
  flutter_easyloading: ^3.0.5
  font_awesome_flutter: ^10.2.1
  intl: ^0.19.0
  shimmer: ^3.0.0
  overlay_support: ^2.0.1
  table_calendar: ^3.0.8
  uuid: ^4.1.0
  modal_bottom_sheet: ^3.0.0-pre
  timezone: ^0.9.0
  logger: ^2.3.0
  url_launcher: ^6.3.1
  url_launcher_ios: ^6.3.2
  connectivity_plus: ^6.0.3
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  file_picker: ^10.0.0
  image_picker: ^1.1.2
  image_cropper: ^9.1.0
  internet_connection_checker_plus: ^2.1.0
  ensure_visible_when_focused: ^1.1.1
  mobile_scanner: ^6.0.5
  qr_flutter: 4.1.0
  syncfusion_flutter_pdfviewer: 27.2.5
  flutter:
    sdk: flutter
  path_provider: ^2.1.3
  permission_handler: ^11.3.1
  open_file: ^3.5.10
  flutter_pdfview: ^1.4.0
  path: ^1.9.0

dev_dependencies:
  flutter_lints: ^4.0.0
  flutter_launcher_icons: ^0.13.1
  hive_generator: ^2.0.0
  build_runner: ^2.3.3
  flutter_test:
    sdk: flutter

flutter:
  assets:
    - assets/images/
    - assets/icons/
  fonts:
    - family: FontAwesome5
      fonts:
        - asset: assets/fonts/FontAwesome5.ttf
  uses-material-design: true
