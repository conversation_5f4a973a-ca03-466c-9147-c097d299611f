import 'dart:convert';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/pages/setup_page_component.dart';
import 'package:hr_portal/app/mahas/models/api_result_model.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/mahas/services/http_api.dart';
import 'package:hr_portal/app/models/absensi_surat_tugas_model.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/components/inputs/input_radio_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../models/pegawai_approval.dart';

class AbsenSuratTugasSetupController extends GetxController {
  late SetupPageController formCon;

  final lokasiAbsensiCon = InputTextController();
  final keteranganCon = InputTextController(type: InputTextType.paragraf);
  final masukCon = InputRadioController(items: [
    RadioButtonItem.autoId("Datang", true),
    RadioButtonItem.autoId("Pulang", false),
  ]);
  final fileCon = InputFileController(
    urlImage: true,
    tipe: InputFileType.camera,
  );
  final tanggalCon = InputDatetimeController();
  final jamCon = InputDatetimeController();

  final alasanTolakCon = InputTextController();
  final String nama = MahasConfig.profile!.nama!;
  late String approve;
  late bool allowED;
  late dynamic accManager = false.obs;
  late dynamic accKadiv = true.obs;
  RxBool isVisible = false.obs;
  late RxString pegawaiKadiv = ''.obs;
  late RxString pegawaiManager = ''.obs;
  late String url;
  final isDinamisApprove = MahasConfig.profileMenu.approvalDinamis;
  int? id;
  RxBool isStop = false.obs;
  RxList<PegawaiApprovalModel> modelApproval = <PegawaiApprovalModel>[].obs;

  //check device dan os
  late String _device;
  late String _os;

  @override
  void onInit() {
    if (MahasConfig.profileMenu.absentugaswithfoto == true) {
      url = '/api/AbsensiTugas/Foto/';
    } else {
      url = '/api/AbsensiTugas?id=';
    }
    _checkUserDevice();
    cekApproval();

    formCon = SetupPageController(
      urlApiGet: (id) => '$url$id',
      urlApiPost: () => url,
      urlApiPut: (id) => '/api/AbsensiTugas/$id',
      allowDelete: false,
      allowEdit: false,
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "lokasi": lokasiAbsensiCon.value,
        "keterangan": keteranganCon.value,
        "masuk": masukCon.value,
        "device": _device,
        "os": _os,
        "fileAbsensiPhoto": fileCon.value,
        "fileName": fileCon.name
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (!lokasiAbsensiCon.isValid) return false;
        if (!masukCon.isValid) return false;
        if (!keteranganCon.isValid) return false;
        if (MahasConfig.profileMenu.absentugaswithfoto == true) {
          if (!fileCon.isValid) return false;
        }
        return true;
      },
      apiToView: (json) {
        AbsensitugasModel model = AbsensitugasModel.fromJson(json);
        id = model.id;
        lokasiAbsensiCon.value = model.lokasi;
        masukCon.value = model.status == "Masuk" ? true : false;
        tanggalCon.value = model.tanggaljam;
        jamCon.value = TimeOfDay.fromDateTime(model.tanggaljam!);
        keteranganCon.value = model.keterangan;
        accManager = model.approvemanager;
        fileCon.value = model.pathfoto;

        // approval
        if (isDinamisApprove == false && allowED == false) {
          if ((model.approvemanager == null &&
              model.idPegawaiManager == MahasConfig.profile!.id)) {
            isVisible.value = true;
          }
        }

        if (isDinamisApprove == true && allowED == false) {
          getDataApproval();
        }
      },
    );
    if (EasyLoading.isShow) return;
    super.onInit();
  }

  void cekApproval() {
    approve = Get.parameters['approval'].toString();
    if (approve == "approval") {
      allowED = false;
    } else {
      allowED = true;
    }
  }

  _checkUserDevice() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (!kIsWeb) {
      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        _device = ('${androidInfo.brand} ${androidInfo.device}').toString();
        _os = androidInfo.version.release.toString();
      } else if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        _device = iosInfo.utsname.machine.toString();
        _os = iosInfo.systemVersion.toString();
      } else if (kIsWeb) {
        WebBrowserInfo webBrowserInfo = await deviceInfo.webBrowserInfo;
        _device = webBrowserInfo.userAgent.toString();
        _os = webBrowserInfo.platform.toString();
      }
    }
  }

  Future<void> approvalOnPress(bool approve) async {
    var id = Get.parameters['id'].toString();
    String urlKadiv = '/api/AbsensiTugas/Approve/Manager/';
    String urlManager = '/api/AbsensiTugas/Approve/Manager/';
    String urlApprove = '/api/AbsensiTugas/Approve/';
    var action = await Helper.approvalAction(
      id,
      approve,
      alasanTolakCon.value,
      accKadiv,
      urlKadiv,
      urlManager,
      isDinamis: isDinamisApprove,
      urlApprove: urlApprove,
    );
    if (action.success) {
      final uri = '$url$id';
      ApiResultModel r;
      r = await HttpApi.get(uri);
      if (r.success) {
        AbsensitugasModel model = AbsensitugasModel.fromJson(r.body);
        accManager = model.approvemanager;
        if (isDinamisApprove) {
          getDataApproval();
        }
        isVisible.value = false;
        update();
      } else {
        Helper.fetchErrorMessage(r);
      }
    } else {
      Helper.fetchErrorMessage(action, httpMethod: HttpMethod.post);
    }
  }

  Future<void> getDataApproval() async {
    EasyLoading.show();
    final url = '/api/AbsensiTugas/Approval/$id';
    final r = await HttpApi.get(url);
    modelApproval.clear();
    if (r.success) {
      if (r.body != null) {
        var data = json.decode(r.body);
        for (var e in data['datas']) {
          modelApproval.add(PegawaiApprovalModel.fromDynamic(e));
        }
        bool canApprove = modelApproval.any((item) =>
            item.idPegawaiapprove == MahasConfig.profile?.id &&
            item.approve == null);
        if (canApprove) {
          isVisible.value = true;
        }
        bool notApprove = modelApproval.any((item) => item.approve == false);
        if (notApprove) {
          isVisible.value = false;
        }
        isStop.value = modelApproval.any((item) => item.approve == false);
      } else {
        Helper.dialogWarning(r.message);
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan aksi, silahkan cek koneksi internet");
    } else if (r.statusCode == 404) {
      Helper.dialogWarning("Tidak Ada Data Pegawai Approval");
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }
}
