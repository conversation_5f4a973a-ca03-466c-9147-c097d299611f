import 'dart:convert';

import '../mahas/services/mahas_format.dart';

class JenisSertifikasiModel {
  int? id;
  String? nama;

  JenisSertifikasiModel();

  static JenisSertifikasiModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static JenisSertifikasiModel fromDynamic(dynamic dynamicData) {
    final model = JenisSertifikasiModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.nama = dynamicData['nama'];

    return model;
  }
}
