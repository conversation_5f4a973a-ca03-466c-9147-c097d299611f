import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/others/empty_component.dart';
import '../../../mahas/mahas_colors.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../mahas_complement/mahas_colors.dart';
import '../controllers/gaji_setup_controller.dart';

class GajiSetupView extends GetView<GajiSetupController> {
  const GajiSetupView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          title: const Text('Gaji'),
          centerTitle: true,
          actions: <Widget>[
            Visibility(
              visible: MahasConfig.profileMenu.downloadSlipGaji,
              child: Padding(
                padding: const EdgeInsets.only(right: 20),
                child: GestureDetector(
                  onTap: () {
                    controller.downloadReport();
                  },
                  child: const Icon(
                    FontAwesomeIcons.download,
                    size: 24,
                  ),
                ),
              ),
            ),
          ]),
      body: MahasConfig.profileMenu.slipGajiStatis == false
          ? slipGaji()
          : slipGajiStatis(),
    );
  }

  Obx slipGajiStatis() {
    return Obx(
      () => Container(
        padding: const EdgeInsets.all(10),
        child: controller.isLoading.value
            ? const Center(child: CircularProgressIndicator())
            : controller.isEmpty.value == true
                ? EmptyComponent(
                    onPressed: controller.getSlipStatis,
                  )
                : Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 10),
                    height: 160,
                    decoration: BoxDecoration(
                      border: Border.all(
                        width: 1.0,
                        color: Colors.grey[900]!,
                      ),
                      borderRadius: const BorderRadius.all(
                        Radius.circular(20.0),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text("Bulan", style: MahasThemes.muted),
                            Text("${controller.modelStatis.value?.bulan}"),
                          ],
                        ),
                        const SizedBox(
                          height: 5,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text("Tahun", style: MahasThemes.muted),
                            Text("${controller.modelStatis.value?.tahun}"),
                          ],
                        ),
                        const SizedBox(
                          height: 25,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text("Penghasilan Bruto", style: MahasThemes.muted),
                            Text(
                                "Rp. ${MahasFormat.toCurrency(controller.modelStatis.value?.penghasilanbruto)}")
                          ],
                        ),
                        const SizedBox(
                          height: 5,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text("Total Potongan", style: MahasThemes.muted),
                            Text(
                                "Rp. ${MahasFormat.toCurrency(controller.modelStatis.value?.totalpotongan)}")
                          ],
                        ),
                        const SizedBox(
                          height: 5,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text("Penghasilan Bersih", style: MahasThemes.h1),
                            Text(
                                "Rp. ${MahasFormat.toCurrency(controller.modelStatis.value?.penghasilanbersih)}")
                          ],
                        ),
                      ],
                    ),
                  ),
      ),
    );
  }

  Obx slipGaji() {
    return Obx(
      () => Container(
        padding: const EdgeInsets.all(10),
        child: controller.isLoading.value
            ? const Center(child: CircularProgressIndicator())
            : controller.isEmpty.value == true
                ? EmptyComponent(
                    onPressed: controller.getSlip,
                  )
                : Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 10),
                        height: 160,
                        decoration: BoxDecoration(
                          border: Border.all(
                            width: 1.0,
                            color: Colors.grey[900]!,
                          ),
                          borderRadius: const BorderRadius.all(
                            Radius.circular(20.0),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              controller.model.value?.pegawai ?? "-",
                              style: MahasThemes.title,
                            ),
                            Row(
                              children: [
                                Text("Jabatan : ", style: MahasThemes.muted),
                                Text(controller.model.value?.jabatan ?? "-"),
                              ],
                            ),
                            const SizedBox(
                              height: 15,
                            ),
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text("Mulai Bekerja",
                                          style: MahasThemes.muted),
                                      Text(
                                        MahasFormat.displayDate(
                                            controller.model.value?.mulaikerja),
                                      ),
                                      const SizedBox(
                                        height: 15,
                                      ),
                                      Text("Izin", style: MahasThemes.muted),
                                      Text(
                                          "${controller.model.value?.absensiizin}"),
                                    ],
                                  ),
                                ),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text("Hari Kerja",
                                          style: MahasThemes.muted),
                                      Text(
                                          "${controller.model.value?.harikerja} Hari"),
                                      const SizedBox(
                                        height: 15,
                                      ),
                                      Text("Cuti", style: MahasThemes.muted),
                                      Text(
                                          "${controller.model.value?.absensicuti}"),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 15,
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 5, vertical: 5),
                        height: 52,
                        // width: 100,
                        decoration: const BoxDecoration(
                          color: MahasColor.greyInputan,
                          borderRadius: BorderRadius.all(
                            Radius.circular(14.0),
                          ),
                        ),
                        child: Row(
                          children: List.generate(
                            controller.menus.length,
                            (index) {
                              var item = controller.menus[index];

                              return Expanded(
                                child: InkWell(
                                  onTap: () {
                                    controller.selectedIndex.value = index;
                                    controller.kategoriOntap();
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12.0,
                                      vertical: 8.0,
                                    ),
                                    decoration: BoxDecoration(
                                      color: index ==
                                              controller.selectedIndex.value
                                          ? MahasColors.primary
                                          : MahasColor.greyInputan,
                                      borderRadius: const BorderRadius.all(
                                        Radius.circular(8.0),
                                      ),
                                    ),
                                    child: Center(
                                      child: Text(
                                        item.title,
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 11.0,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 15,
                      ),
                      Expanded(
                        child: ListView.builder(
                          itemCount: controller.pengeluaran.length,
                          physics: const ScrollPhysics(),
                          itemBuilder: (BuildContext context, int i) {
                            var item = controller.pengeluaran[i];
                            return Column(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[100],
                                    borderRadius: BorderRadius.all(
                                      Radius.circular(MahasThemes.borderRadius),
                                    ),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text("Jenis", style: MahasThemes.muted),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text("${item.jenisdetailslipgaji}"),
                                          Text(
                                              "Rp. ${MahasFormat.toCurrency(item.nilai)}"),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                const Padding(padding: EdgeInsets.all(5)),
                              ],
                            );
                          },
                        ),
                      ),
                      const SizedBox(
                        height: 15,
                      ),
                      Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text("Total Pemasukan", style: MahasThemes.muted),
                              Text(
                                  "Rp. ${MahasFormat.toCurrency(controller.totalPemasukan)}")
                            ],
                          ),
                          const SizedBox(
                            height: 5,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text("Total Pengeluaran",
                                  style: MahasThemes.muted),
                              Text(
                                  "-Rp. ${MahasFormat.toCurrency(controller.totalPengeluaran)}")
                            ],
                          ),
                          const SizedBox(
                            height: 5,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                "Total Gaji",
                                style: MahasThemes.h1,
                              ),
                              Text(
                                  "Rp. ${MahasFormat.toCurrency(controller.totalGaji)}")
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
      ),
    );
  }
}
