import 'dart:convert';

import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_checkbox_component.dart';
import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../models/pangkat_history_model.dart';

class ProfilePegawaiPangkatSetupController extends GetxController {
  late SetupPageController formCon;

  final pangkatCon = InputTextController();
  final nomorKepCon = InputTextController();
  final terhitungMulaiCon = InputDatetimeController();
  final aktifCon = InputCheckboxController();

  @override
  void onInit() {
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PegawaiPangkat/History/$id',
      allowEdit: false,
      allowDelete: false,
      bodyApi: (id) => {},
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (!nomorKepCon.isValid) return false;
        if (!terhitungMulaiCon.isValid) return false;

        return true;
      },
      apiToView: (json) {
        PangkathistoryModel model = PangkathistoryModel.fromJson(json);
        pangkatCon.value = model.idPangkatText;
        nomorKepCon.value = model.nomorkep;
        terhitungMulaiCon.value = model.terhitungmulaitanggal;
        aktifCon.checked = model.aktif!;
      },
    );

    super.onInit();
  }
}
