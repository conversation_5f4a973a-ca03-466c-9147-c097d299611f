import 'dart:convert';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_text_component.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';

import '../../../mahas/components/inputs/input_detail_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/models/api_result_model.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../models/etikcet_model.dart';

class EticketController extends GetxController {
  late SetupPageController formCon;
  final judulCon = InputTextController();
  final detailCon = InputDetailControler<LookupEtiketPegawaiModel>(
    itemKey: (e) => e.id,
    fromDynamic: (e) => LookupEtiketPegawaiModel.fromDynamic(e),
    itemText: (e) => e.nama ?? "",
    urlApi: (index, filter) =>
        '/api/ETicket/Pegawai?pageIndex=$index&id_divisi=null&filter=$filter',
  );
  final deskripsiCon = InputTextController(type: InputTextType.paragraf);
  final feedbackCon = InputTextController(type: InputTextType.paragraf);
  RxBool editableFeedBack = true.obs;

  late String approve;
  late bool allowED;
  RxBool isVisible = false.obs;
  final alasanTolakCon = InputTextController();

  @override
  void onInit() {
    cekApproval();
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/ETicket/$id',
      urlApiPost: () => '/api/ETicket',
      urlApiPut: (id) => '/api/ETicket/$id',
      urlApiDelete: (id) => '/api/ETicket/$id',
      allowDelete: allowED,
      allowEdit: allowED,
      bodyApi: (id) => {
        "judul": judulCon.value,
        "deskripsi": deskripsiCon.value,
        "id_Divisi": null,
        "detail": detailCon.values
            .map((e) => {
                  "id_Pegawai": e.id,
                })
            .toList()
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (!judulCon.isValid) return false;
        if (!deskripsiCon.isValid) return false;
        if (!detailCon.isValid) return false;
        return true;
      },
      apiToView: (json) {
        EticketModel model = EticketModel.fromJson(json);
        judulCon.value = model.judul;
        deskripsiCon.value = model.deskripsi;
        feedbackCon.value = model.feedback;
        detailCon.clear();
        for (var i = 0; i < model.detailpegawai!.length; i++) {
          detailCon.addValue(LookupEtiketPegawaiModel.init(
              model.detailpegawai![i].idPegawai,
              model.detailpegawai![i].pegawai));
        }

        if (model.idPegawai != MahasConfig.profile?.id) {
          allowED = false;
          updateState();
        }

        if (model.feedback != null) {
          editableFeedBack.value = false;
        }

        // approval
        if (allowED == false) {
          if ((model.selesai == null && model.batal == null)) {
            isVisible.value = true;
          }
        }
      },
    );

    super.onInit();
  }

  void updateState() {
    formCon.updateSetState(() {
      formCon.allowDelete = allowED;
      formCon.allowEdit = allowED;
    });
  }

  void cekApproval() {
    approve = Get.parameters['approval'].toString();
    if (approve == "approval") {
      allowED = false;
    } else {
      allowED = true;
    }
  }

  void batalOnPress() {
    var id = Get.parameters['id'].toString();
    batal(id);
  }

  void selesaiOnPress() {
    var id = Get.parameters['id'].toString();
    selesai(id);
  }

  void selesai(String id) async {
    if (!feedbackCon.isValid) return;
    var data = {
      "feedback": feedbackCon.value,
    };
    if (EasyLoading.isShow) return;
    EasyLoading.show();

    ApiResultModel? r;

    final url = '/api/ETicket/Selesai?id=$id';
    r = await HttpApi.put(url, body: data);

    if (r.success == true) {
      isVisible.value = false;
      editableFeedBack.value = false;
      formCon.updateSetState(() {
        formCon.backRefresh = true;
      });
    } else if (r.success == false) {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }

  void batal(String id) async {
    if (!feedbackCon.isValid) return;
    var data = {
      "feedback": feedbackCon.value,
    };
    if (EasyLoading.isShow) return;
    EasyLoading.show();

    ApiResultModel? r;

    final url = '/api/ETicket/Batal?id=$id';
    r = await HttpApi.put(url, body: data);

    if (r.success == true) {
      isVisible.value = false;
      editableFeedBack.value = false;
      formCon.updateSetState(() {
        formCon.backRefresh = true;
      });
    } else if (r.success == false) {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }
}
