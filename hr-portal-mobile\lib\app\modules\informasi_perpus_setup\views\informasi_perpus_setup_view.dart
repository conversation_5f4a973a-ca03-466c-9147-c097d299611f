import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../controllers/informasi_perpus_setup_controller.dart';

class InformasiPerpusSetupView extends GetView<InformasiPerpusSetupController> {
  const InformasiPerpusSetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      controller: controller.formCon,
      title: 'E-Perpus',
      children: () => [
        InputTextComponent(
          label: 'Tentang',
          controller: controller.namaSertifikasiCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: '<PERSON><PERSON><PERSON>',
          controller: controller.kategoriCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: 'Nomor',
          controller: controller.nomorCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: 'Tahun',
          controller: controller.tahunCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDatetimeComponent(
          controller: controller.tglDiundangkanCon,
          label: 'Tanggal Diundangkan',
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDatetimeComponent(
          controller: controller.tglDiterbitkanCon,
          label: 'Tanggal Diterbitkan',
          required: true,
          editable: controller.formCon.editable,
        ),
        InputFileComponent(
          controller: controller.berkasPendukungCon,
          required: true,
          label: 'Berkas (*pdf)',
          editable: controller.formCon.editable,
        ),
      ],
    );
  }
}
