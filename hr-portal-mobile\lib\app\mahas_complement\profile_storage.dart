import 'package:hr_portal/app/mahas_complement/service_data.dart';

class ProfileService {
  static dynamic profile = {};

  static saveToLocalStorage() async {
    await mainStorage.put("profile", profile);
  }

  static loadDataFromDB() async {
    profile = await mainStorage.get("profile");
  }

  static savedata() {
    saveToLocalStorage();
  }

  static void clearData() {
    // profile.clear();
    saveToLocalStorage();
  }
}
