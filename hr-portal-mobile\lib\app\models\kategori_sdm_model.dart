import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class KategoriSdmModel {
  int? id;
  String? nama;

  KategoriSdmModel();

  static KategoriSdmModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static KategoriSdmModel fromDynamic(dynamic dynamicData) {
    final model = KategoriSdmModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.nama = dynamicData['nama'];

    return model;
  }
}
