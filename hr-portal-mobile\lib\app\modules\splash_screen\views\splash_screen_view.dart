import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/constant/environment_constant.dart';

import '../controllers/splash_screen_controller.dart';

class SplashScreenView extends GetView<SplashScreenController> {
  const SplashScreenView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        color: Colors.white,
        child: Center(
          child: SizedBox(
            height: 200,
            child: Image.asset(
              EnvironmentConstant.imageLogo,
              scale: 1,
            ),
          ),
        ),
      ),
    );
  }
}
