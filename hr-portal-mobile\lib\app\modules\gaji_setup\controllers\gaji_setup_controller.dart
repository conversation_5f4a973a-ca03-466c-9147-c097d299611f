// ignore_for_file: unrelated_type_equality_checks

import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:open_file/open_file.dart';

import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../models/gaji_model.dart';

class MenuItemModel {
  final String title;
  final GestureTapCallback onTab;

  MenuItemModel(this.title, this.onTab);
}

class GajiSetupController extends GetxController {
  RxInt selectedIndex = 0.obs;
  final List<MenuItemModel> menus = [];
  RxList<GajiDetailModel> pengeluaran = <GajiDetailModel>[].obs;
  double totalPengeluaran = 0;
  double totalPemasukan = 0;
  double totalGaji = 0;

  dynamic id;
  var model = Rxn<GajiModel>();
  var modelStatis = Rxn<SlipGajiStatisModel>();
  RxBool isLoading = true.obs;
  RxBool isEmpty = false.obs;

  @override
  void onInit() {
    id = Get.parameters['id'];
    if (MahasConfig.profileMenu.slipGajiStatis == false) {
      getSlip();
      menus.add(MenuItemModel('Pemasukan', listPemasukan));
      menus.add(MenuItemModel('Pengeluaran', listPengeluaran));
    } else {
      getSlipStatis();
    }

    super.onInit();
  }

  Future<void> getSlip() async {
    var r = await HttpApi.get('/api/SlipGaji/$id');
    if (r.success) {
      model.value = GajiModel.fromJson(r.body);
      isEmpty.value = false;
    } else {
      Helper.dialogWarning("Tidak Ada Data");
      isEmpty.value = true;
    }
    listPemasukan();
    gaji();
    isLoading.value = false;
  }

  void getSlipStatis() async {
    var r = await HttpApi.get('/api/SlipGaji/Statis/$id');
    if (r.success) {
      modelStatis.value = SlipGajiStatisModel.fromJson(r.body);
      isEmpty.value = false;
    } else {
      Helper.dialogWarning("Tidak Ada Data");
      isEmpty.value = true;
    }
    isLoading.value = false;
  }

  void listPengeluaran() {
    pengeluaran.clear();
    pengeluaran.value =
        model.value!.detail!.where((e) => e.pendapatan != true).toList();
  }

  void listPemasukan() {
    pengeluaran.clear();
    pengeluaran.value =
        model.value!.detail!.where((e) => e.pendapatan == true).toList();
  }

  void kategoriOntap() {
    if (selectedIndex == 0) {
      listPemasukan();
    } else {
      listPengeluaran();
    }
  }

  void gaji() {
    RxList<GajiDetailModel> pengeluaran = <GajiDetailModel>[].obs;
    pengeluaran.value =
        model.value!.detail!.where((e) => e.pendapatan != true).toList();
    for (var i = 0; i < pengeluaran.length; i++) {
      totalPengeluaran += pengeluaran[i].nilai!;
    }
    pengeluaran.clear();
    pengeluaran.value =
        model.value!.detail!.where((e) => e.pendapatan == true).toList();
    for (var i = 0; i < pengeluaran.length; i++) {
      totalPemasukan += pengeluaran[i].nilai!;
    }
    totalGaji = totalPemasukan - totalPengeluaran;
  }

  void downloadReport() async {
    // Cek apakah Android 10 ke atas, tidak perlu izin storage
    if (Platform.isAndroid && (await _getAndroidVersion() >= 29)) {
      await _processDownload();
    } else {
      // Minta izin storage untuk Android 9 ke bawah
      var status = await Permission.storage.request();
      if (!status.isGranted) {
        Helper.dialogWarning("Aktifkan Permission Storage");
        return;
      }
      await _processDownload();
    }
  }

  Future<void> _processDownload() async {
    int? tahun;
    int? bulan;

    if (MahasConfig.profileMenu.slipGajiStatis == false) {
      tahun = model.value?.tahun;
      bulan = model.value?.bulan;
    } else {
      tahun = modelStatis.value?.tahun;
      bulan = modelStatis.value?.bulan;
    }

    var r =
        await HttpApi.get('/api/SlipGaji/ReportUrl?Bulan=$bulan&Tahun=$tahun');
    if (r.success) {
      ReportModel model = ReportModel.fromJson(r.body);
      String downloadUrl = MahasConfig.profileMenu.slipGajiStatis == false
          ? model.urlreport!
          : model.urlreportstatis!;
      await download(downloadUrl, bulan, tahun);
    } else {
      Helper.dialogWarning(r.message);
    }
  }

  Future<void> download(String url, int? bulan, int? tahun) async {
    EasyLoading.show();
    String? filePath;
    try {
      Directory? saveDir;

      // Cek platform dan dapatkan directory penyimpanan yang sesuai
      if (Platform.isAndroid) {
        saveDir = await getExternalStorageDirectory();
      } else if (Platform.isIOS) {
        saveDir = await getApplicationDocumentsDirectory();
      } else {
        throw Exception("Platform not supported");
      }

      if (saveDir == null) {
        throw Exception("Cannot access directory");
      }

      String savePath = "${saveDir.path}/HR_SlipGaji_${bulan}_$tahun.pdf";

      // Mulai proses download
      var response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        File file = File(savePath);
        filePath = savePath;
        await file.writeAsBytes(response.bodyBytes); // Simpan file
      } else {
        throw Exception('Failed to download file');
      }
    } catch (e) {
      EasyLoading.dismiss();
      Helper.dialogWarning("Gagal Download");
    } finally {
      EasyLoading.dismiss();
      if (filePath != null) {
        OpenFile.open(filePath); // Buka file setelah berhasil di-download
      }
    }
  }

  Future<int> _getAndroidVersion() async {
    var deviceInfo = DeviceInfoPlugin();
    AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;

    return androidInfo.version.sdkInt;
  }
}
