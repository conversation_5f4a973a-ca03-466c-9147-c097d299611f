import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class GolonganDarahModel {
  int? id;
  String? nama;

  GolonganDarahModel();

  static GolonganDarahModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static GolonganDarahModel fromDynamic(dynamic dynamicData) {
    final model = GolonganDarahModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.nama = dynamicData['nama'];

    return model;
  }
}
