import 'package:flutter/material.dart';
import 'package:hr_portal/app/mahas/components/mahas_themes.dart';
import 'package:hr_portal/app/mahas/mahas_colors.dart';
import 'package:hr_portal/app/mahas_complement/mahas_colors.dart';
import 'package:hr_portal/app/models/absensi_model.dart';
import 'package:hr_portal/app/models/informasi_absensi_sesuai_divisi.dart';
import 'package:intl/intl.dart';

class MahasWidget {
  static Widget smoothRectangleItemWidget(Widget child) {
    return Container(
      margin: const EdgeInsets.only(
        left: 10,
        right: 10,
        bottom: 5,
      ),
      decoration: BoxDecoration(
        border: Border.all(
          color: MahasColors.grey,
        ),
        borderRadius: BorderRadius.all(
          Radius.circular(MahasThemes.borderRadius),
        ),
      ),
      child: child,
    );
  }

  static Widget absensiSayaItemWidget(AbsensiModel item) {
    return smoothRectangleItemWidget(
      ListTile(
        title: Text(item.lokasiabsensi ?? "-"),
        trailing: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              DateFormat("dd MMMM yyyy").format(item.tanggaljam!),
              style: MahasColor.muted,
            ),
            Text(
              DateFormat("HH:mm").format(item.tanggaljam!),
              style: MahasColor.muted,
            ),
          ],
        ),
      ),
    );
  }

  static Widget absensiDivisiItemWidget(InformasiabsensiModel item) {
    return smoothRectangleItemWidget(
      ListTile(
        title: Text(item.pegawai ?? "-"),
        subtitle: Text(
          item.lokasiabsensi ?? "-",
          style: MahasColor.muted,
        ),
        trailing: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              DateFormat("dd MMMM yyyy").format(item.tanggaljam!),
              style: MahasColor.muted,
            ),
            Text(
              DateFormat("HH:mm").format(item.tanggaljam!),
              style: MahasColor.muted,
            ),
          ],
        ),
      ),
    );
  }
}
