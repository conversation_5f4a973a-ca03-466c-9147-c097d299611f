import 'dart:convert';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/mahas/services/http_api.dart';
import 'package:hr_portal/app/models/informasi_cuti_tahunan_model.dart';

class InformasiQuotaCutiTahunanController extends GetxController {
  var models = RxList<InformasicutitahunanModel>();
  var isLoading = false.obs;

  Future<void> onRefresh() async {
    if (isLoading.isTrue) return;
    isLoading.value = true;
    var r = await HttpApi.get('/api/Informasi/QuotaCutiTahunan');
    if (r.success) {
      final datas = json.decode(r.body);
      models.clear();
      for (var e in datas) {
        models.add(InformasicutitahunanModel.fromDynamic(e));
      }
    } else {
      Helper.fetchErrorMessage(r, httpMethod: HttpMethod.get);
    }
    isLoading.value = false;
  }

  @override
  void onInit() {
    super.onInit();
    onRefresh();
  }
}
