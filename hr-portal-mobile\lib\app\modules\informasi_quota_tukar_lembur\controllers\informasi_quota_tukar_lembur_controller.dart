import 'dart:convert';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/mahas/services/http_api.dart';
import 'package:hr_portal/app/models/informasi_tukar_lembur_model.dart';

class InformasiQuotaTukarLemburController extends GetxController {
  var models = RxList<InformasitukarlemburModel>();
  var isLoading = false.obs;
  RxBool noData = true.obs;

  Future<void> onRefresh() async {
    if (isLoading.isTrue) return;
    isLoading.value = true;
    var r = await HttpApi.get('/api/Informasi/QuotaTukarLembur');
    if (r.success) {
      final datas = json.decode(r.body);
      if (r.body != "[]") {
        noData.value = false;
      }
      models.clear();
      for (var e in datas) {
        models.add(InformasitukarlemburModel.fromDynamic(e));
      }
    } else {
      Helper.fetchErrorMessage(r, httpMethod: HttpMethod.get);
    }
    isLoading.value = false;
  }

  @override
  void onInit() {
    super.onInit();
    onRefresh();
  }
}
