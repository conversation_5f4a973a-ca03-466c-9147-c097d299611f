import 'dart:convert';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_checkbox_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/models/api_list_resut_model.dart';
import '../../../mahas/services/http_api.dart';
import '../../../models/bank_model.dart';
import '../../../models/profile_rekening_model.dart';
import '../../../routes/app_pages.dart';

class ProfileRekeningSetupController extends GetxController {
  late SetupPageController formCon;

  final noRekeningCon = InputTextController();
  final namaBankCon = InputDropdownController();
  final atasNamaCon = InputTextController();
  final aktifCon = InputCheckboxController();
  final prioritasCon = InputCheckboxController();

  RxInt idPegawaiRekening = 0.obs;

  @override
  void onInit() {
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PegawaiRekening/$id',
      urlApiPost: () => '/api/PegawaiRekening',
      urlApiPut: (id) => '/api/PegawaiRekening/$id',
      urlApiDelete: (id) => '/api/PegawaiRekening/$id',
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "id_PegawaiRekening": idPegawaiRekening.value,
        "noRekening": noRekeningCon.value,
        "nama": atasNamaCon.value,
        "id_Bank": namaBankCon.value,
        "aktif": aktifCon.checked,
        "prioritas": prioritasCon.checked,
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (!noRekeningCon.isValid) return false;
        if (!namaBankCon.isValid) return false;
        if (!atasNamaCon.isValid) return false;

        return true;
      },
      apiToView: (json) {
        ProfileRekeningModel model = ProfileRekeningModel.fromJson(json);

        //mahasComponent
        noRekeningCon.value = model.norekening;
        namaBankCon.value = model.idBank;
        atasNamaCon.value = model.nama;
        if (model.aktif == true) {
          aktifCon.checked = true;
        } else {
          aktifCon.checked = false;
        }
        if (model.prioritas == true) {
          prioritasCon.checked = true;
        } else {
          prioritasCon.checked = false;
        }

        //non mahas
        idPegawaiRekening.value = model.id ?? 0;
      },
      onInit: () async {
        await getBank();
      },
    );

    if (MahasConfig.hasHistory) {
      formCon.onSuccessSubmit = (r) {
        var idHistory = json.decode(r.body)['id'];
        Get.toNamed(
          Routes.PROFILE_REKENING_HISTORY_SETUP,
          parameters: {
            'id': idHistory.toString(),
            'showStatus': true.toString(),
            'withActionBack': true.toString(),
          },
        );
      };
      formCon.deleteOnTap = (id) {
        Get.toNamed(
          Routes.PROFILE_REKENING_HISTORY_SETUP,
          parameters: {
            'id': id.toString(),
            'showStatus': true.toString(),
            'withActionBack': true.toString(),
          },
        );
      };
    }

    super.onInit();
  }

  Future<void> getBank() async {
    if (EasyLoading.isShow) {
      EasyLoading.dismiss();
    }
    EasyLoading.show();
    var r = await HttpApi.get("/api/Bank");
    if (r.success) {
      RxList<BankModel> listModel = RxList<BankModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(BankModel.fromDynamic(e));
      }
      if (namaBankCon.items.isNotEmpty) {
        namaBankCon.items.clear();
      }
      if (listModel.isNotEmpty && namaBankCon.items.isEmpty) {
        namaBankCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      }
    }
    EasyLoading.dismiss();
  }
}
