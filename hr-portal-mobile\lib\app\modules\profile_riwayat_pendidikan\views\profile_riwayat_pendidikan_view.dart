import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/others/list_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../models/profile_riwayat_pendidikan_model.dart';
import '../controllers/profile_riwayat_pendidikan_controller.dart';

class ProfileRiwayatPendidikanView
    extends GetView<ProfileRiwayatPendidikanController> {
  const ProfileRiwayatPendidikanView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Riwayat Pendidikan'),
        centerTitle: true,
        actions: [
          GetBuilder<ProfileRiwayatPendidikanController>(
            builder: (controller) {
              return PopupMenuButton(
                onSelected: controller.popupMenuButtonOnSelected,
                itemBuilder: (context) {
                  List<PopupMenuItem<String>> r = [];
                  r.add(
                    const PopupMenuItem(
                      value: 'Tambah',
                      child: Text('Tambah'),
                    ),
                  );
                  if (MahasConfig.hasHistory) {
                    r.add(
                      const PopupMenuItem(
                        value: 'History',
                        child: Text('History'),
                      ),
                    );
                  }

                  return r;
                },
              );
            },
          ),
        ],
      ),
      body: ListComponent(
        controller: controller.listCon,
        itemBuilder: (RiwayatpendidikanModel e) {
          return ListTile(
            title: Text("Pendidikan : ${e.namapendidikan ?? "-"}",
                style: MahasThemes.title),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                    "Klasifikasi Pendidikan : ${e.namaklasifikasipendidikan ?? "-"}",
                    style: MahasThemes.muted),
                Text("Program Studi : ${e.programstudi ?? "-"}",
                    style: MahasThemes.muted),
              ],
            ),
            trailing: Text("${e.daritahun} - ${e.sampaitahun}",
                style: MahasThemes.muted),
            onTap: () => controller.itemOnTab(e.id!),
          );
        },
      ),
    );
  }
}
