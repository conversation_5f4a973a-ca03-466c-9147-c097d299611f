import 'package:flutter/material.dart';

import 'package:get/get.dart';
import '../../../mahas/components/inputs/input_detail_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_colors.dart';
import '../../../models/etikcet_model.dart';
import '../controllers/eticket_controller.dart';

class EticketView extends GetView<EticketController> {
  const EticketView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      childrenPadding: false,
      controller: controller.formCon,
      title: 'E-Ticket',
      children: () => [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Column(
            children: [
              InputTextComponent(
                label: 'Judul',
                controller: controller.judulCon,
                required: true,
                editable: controller.formCon.editable,
              ),
            ],
          ),
        ),
        InputDetailComponent<LookupEtiketPegawaiModel>(
          label: "<PERSON>pada Pegawai",
          controller: controller.detailCon,
          editable: controller.formCon.editable,
          required: true,
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: InputTextComponent(
            label: 'Deskripsi',
            controller: controller.deskripsiCon,
            required: true,
            editable: controller.formCon.editable,
          ),
        ),
        Visibility(
          visible: controller.formCon.editable == false,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Obx(() => InputTextComponent(
                  label: 'Feedback',
                  controller: controller.feedbackCon,
                  required: true,
                  editable: controller.editableFeedBack.value,
                )),
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Obx(
            () {
              return Visibility(
                visible: controller.isVisible.value,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          controller.batalOnPress();
                        },
                        style: ElevatedButton.styleFrom(
                            backgroundColor: MahasColors.red),
                        child: const Text("Batal"),
                      ),
                    ),
                    const SizedBox(
                      width: 20,
                    ),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          controller.selesaiOnPress();
                        },
                        child: const Text("Selesai"),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
