import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';

class TukarJadwalModel {
  int? id;
  int? idDivisi;
  int? idPegawaiPengganti;
  int? idPegawaiRequest;
  int? idJadwaltanggalRequest;
  int? idJadwaltanggalPengganti;
  int? idPegawaiKadiv;
  int? idShiftRequest;
  int? idShiftPengganti;
  String? alasan;
  String? alasanTolak;
  bool? approvepengganti;
  bool? approvekadiv;
  DateTime? jadwaltanggalrequest;
  DateTime? jadwaltanggalpengganti;
  String? pegawairequest;
  String? pegawaikadiv;
  String? pegawaipengganti;
  String? shiftrequest;
  String? shiftrequestkode;
  String? shiftrequestkodewarna;
  String? shiftpengganti;
  String? shiftpenggantikode;
  String? shiftpenggantikodewarna;
  String? divisi;

  TukarJadwalModel();
  TukarJadwalModel.init(this.pegawaipengganti, this.idJadwaltanggalPengganti);

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static TukarJadwalModel fromDynamic(dynamic dynamicData) {
    final model = TukarJadwalModel();

    model.id = dynamicData['id'];
    model.idDivisi = dynamicData['id_Divisi'];
    model.idPegawaiPengganti = dynamicData['id_Pegawai_Pengganti'];
    model.idPegawaiRequest = dynamicData['id_Pegawai_Request'];
    model.idJadwaltanggalRequest = dynamicData['id_JadwalTanggal_Request'];
    model.idJadwaltanggalPengganti = dynamicData['id_JadwalTanggal_Pengganti'];
    model.idPegawaiKadiv = dynamicData['id_Pegawai_Kadiv'];
    model.idShiftRequest = dynamicData['id_Shift_Request'];
    model.idShiftPengganti = dynamicData['id_Shift_Pengganti'];
    model.alasan = dynamicData['alasan'];
    model.alasanTolak = dynamicData['alasanTolak'];
    model.approvepengganti = dynamicData['approvePengganti'];
    model.approvekadiv = dynamicData['approveKadiv'];
    model.jadwaltanggalrequest =
        MahasFormat.stringToDateTime(dynamicData['jadwalTanggalRequest']);
    model.jadwaltanggalpengganti =
        MahasFormat.stringToDateTime(dynamicData['jadwalTanggalPengganti']);
    model.pegawairequest = dynamicData['pegawaiRequest'];
    model.pegawaikadiv = dynamicData['pegawaiKadiv'];
    model.pegawaipengganti = dynamicData['pegawaiPengganti'];
    model.shiftrequest = dynamicData['shiftRequest'];
    model.shiftrequestkode = dynamicData['shiftRequestKode'];
    model.shiftrequestkodewarna = dynamicData['shiftRequestKodeWarna'];
    model.shiftpengganti = dynamicData['shiftPengganti'];
    model.shiftpenggantikode = dynamicData['shiftPenggantiKode'];
    model.shiftpenggantikodewarna = dynamicData['shiftPenggantiKodeWarna'];
    model.divisi = dynamicData['divisi'];

    return model;
  }
}

class TukarjadwalrequestModel {
  int? id;
  int? idShift;
  int? tanggal;
  String? shift;
  String? kode;
  String? kodewarna;
  TimeOfDay? jammulai;
  TimeOfDay? jamselesai;
  bool? shiftke2;
  TimeOfDay? jammulaike2;
  TimeOfDay? jamselesaike2;
  bool? shiftke3;
  TimeOfDay? jammulaike3;
  TimeOfDay? jamselesaike3;

  TukarjadwalrequestModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static TukarjadwalrequestModel fromDynamic(dynamic dynamicData) {
    final model = TukarjadwalrequestModel();

    model.id = dynamicData['id'];
    model.idShift = dynamicData['id_Shift'];
    model.tanggal = dynamicData['tanggal'];
    model.shift = dynamicData['shift'];
    model.kode = dynamicData['kode'];
    model.kodewarna = dynamicData['kodeWarna'];
    model.jammulai = MahasFormat.stringToTime(dynamicData['jamMulai']);
    model.jamselesai = MahasFormat.stringToTime(dynamicData['jamSelesai']);
    model.shiftke2 = dynamicData['shiftKe2'];
    model.jammulaike2 = MahasFormat.stringToTime(dynamicData['jamMulaiKe2']);
    model.jamselesaike2 =
        MahasFormat.stringToTime(dynamicData['jamSelesaiKe2']);
    model.shiftke3 = dynamicData['shiftKe3'];
    model.jammulaike3 = MahasFormat.stringToTime(dynamicData['jamMulaiKe3']);
    model.jamselesaike3 =
        MahasFormat.stringToTime(dynamicData['jamSelesaiKe3']);

    return model;
  }
}

List<TukarJadwalTukarModel> tukarJadwalTukarModelFromJson(String str) =>
    List<TukarJadwalTukarModel>.from(
        json.decode(str).map((x) => TukarJadwalTukarModel.fromJson(x)));

String tukarJadwalTukarModelToJson(List<TukarJadwalTukarModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class TukarJadwalTukarModel {
  TukarJadwalTukarModel({
    this.id,
    this.idPegawai,
    this.pegawai,
    this.idShift,
    this.tanggal,
    this.shift,
    this.kode,
    this.kodeWarna,
    this.jamMulai,
    this.jamSelesai,
    this.shiftKe2,
    this.jamMulaiKe2,
    this.jamSelesaiKe2,
    this.shiftKe3,
    this.jamMulaiKe3,
    this.jamSelesaiKe3,
  });

  int? id;
  int? idPegawai;
  String? pegawai;
  int? idShift;
  int? tanggal;
  String? shift;
  String? kode;
  String? kodeWarna;
  TimeOfDay? jamMulai;
  TimeOfDay? jamSelesai;
  bool? shiftKe2;
  TimeOfDay? jamMulaiKe2;
  TimeOfDay? jamSelesaiKe2;
  bool? shiftKe3;
  TimeOfDay? jamMulaiKe3;
  TimeOfDay? jamSelesaiKe3;

  factory TukarJadwalTukarModel.fromJson(Map<String, dynamic> json) =>
      TukarJadwalTukarModel(
        id: json["id"],
        idPegawai: json["id_Pegawai"],
        pegawai: json["pegawai"],
        idShift: json["id_Shift"],
        tanggal: json["tanggal"],
        shift: json["shift"],
        kode: json["kode"],
        kodeWarna: json["kodeWarna"],
        jamMulai: MahasFormat.stringToTime(json["jamMulai"]),
        jamSelesai: MahasFormat.stringToTime(json["jamSelesai"]),
        shiftKe2: json["shiftKe2"],
        jamMulaiKe2: MahasFormat.stringToTime(json["jamMulaiKe2"]),
        jamSelesaiKe2: MahasFormat.stringToTime(json["jamSelesaiKe2"]),
        shiftKe3: json["shiftKe3"],
        jamMulaiKe3: MahasFormat.stringToTime(json["jamMulaiKe3"]),
        jamSelesaiKe3: MahasFormat.stringToTime(json["jamSelesaiKe3"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "id_Pegawai": idPegawai,
        "pegawai": pegawai,
        "id_Shift": idShift,
        "tanggal": tanggal,
        "shift": shift,
        "kode": kode,
        "kodeWarna": kodeWarna,
        "jamMulai": jamMulai,
        "jamSelesai": jamSelesai,
        "shiftKe2": shiftKe2,
        "jamMulai2": jamMulaiKe2,
        "jamSelesai2": jamSelesaiKe2,
        "shiftKe3": shiftKe3,
        "jamMulai3": jamMulaiKe3,
        "jamSelesai3": jamSelesaiKe3,
      };
}
