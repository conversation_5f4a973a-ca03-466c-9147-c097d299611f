import 'package:get/get.dart';
import 'package:hr_portal/app/models/cuti_tukar_lembur.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/services/http_api.dart';
import '../../../models/divisi_model.dart';
import '../../../routes/app_pages.dart';

class PermintaanJadwalCutiTukarLemburController extends GetxController {
  final listCon = ListComponentController<CutitukarlemburModel>(
    urlApi: (index, filter) =>
        '/api/PermintaanJadwal/CutiTukarLembur/List?pageIndex=$index',
    fromDynamic: (e) => CutitukarlemburModel.fromDynamic(e),
    allowSearch: false,
  );

  List<DivisiModel>? listDivisi;
  RxBool isKadiv = false.obs;
  bool isDinamis = MahasConfig.profileMenu.approvalDinamis;

  void addOnPress() {
    Get.toNamed(Routes.PERMINTAAN_JADWAL_CUTI_TUKAR_LEMBUR_SETUP)
        ?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PERMINTAAN_JADWAL_CUTI_TUKAR_LEMBUR_SETUP,
      parameters: {'id': id.toString(), 'isKadiv': isKadiv.value.toString()},
    )?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }

  @override
  void onInit() {
    getDivisi();
    super.onInit();
  }

  getDivisi() async {
    var r = await HttpApi.get('/api/Profile/Divisi');
    if (r.success) {
      listDivisi = divisiModelFromJson(r.body);
      for (var i = 0; i < listDivisi!.length; i++) {
        if (MahasConfig.selectedDivisi == listDivisi![i].id) {
          if (MahasConfig.profile!.nama == listDivisi![i].kadiv ||
              MahasConfig.profile!.nama == listDivisi![i].manager) {
            isKadiv.value = true;
          }
        }
      }
    }
  }
}
