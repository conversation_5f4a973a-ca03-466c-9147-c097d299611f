import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:url_launcher/url_launcher.dart';

/// Widget sederhana untuk menampilkan konten HTML
///
/// <PERSON><PERSON><PERSON> penggunaan:
/// ```dart
/// HtmlViewerComponent(
///   htmlContent: '<p>test link l <a href="https://www.google.com/">ini&nbsp;</a></p>',
/// )
/// ```
class HtmlViewerComponent extends StatelessWidget {
  final String htmlContent;
  final double? fontSize;
  final EdgeInsets? padding;
  final bool showBorder;
  final Color? backgroundColor;
  final double? maxHeight;

  const HtmlViewerComponent({
    super.key,
    required this.htmlContent,
    this.fontSize = 14,
    this.padding,
    this.showBorder = false,
    this.backgroundColor,
    this.maxHeight,
  });

  @override
  Widget build(BuildContext context) {
    Widget htmlWidget = Html(
      data: htmlContent.isNotEmpty
          ? htmlContent
          : '<p style="color: #999; font-style: italic;">Tidak ada konten</p>',
      style: {
        "body": Style(
          fontSize: FontSize(fontSize ?? 14),
          margin: Margins.zero,
          padding: HtmlPaddings.zero,
          color: Colors.black87,
        ),
        "p": Style(
          margin: Margins.only(bottom: 8),
          lineHeight: const LineHeight(1.4),
        ),
        "a": Style(
          color: Colors.blue,
          textDecoration: TextDecoration.underline,
        ),
        "img": Style(
          width: Width(20),
          height: Height.auto(),
        ),
        "strong, b": Style(
          fontWeight: FontWeight.bold,
        ),
        "em, i": Style(
          fontStyle: FontStyle.italic,
        ),
        "h1, h2, h3, h4, h5, h6": Style(
          fontWeight: FontWeight.bold,
          margin: Margins.only(top: 16, bottom: 8),
        ),
        "ul, ol": Style(
          margin: Margins.only(bottom: 8),
          padding: HtmlPaddings.only(left: 16),
        ),
        "li": Style(
          margin: Margins.only(bottom: 4),
        ),
        "blockquote": Style(
          margin: Margins.only(left: 16, bottom: 8),
          padding: HtmlPaddings.only(left: 16),
          border: const Border(
            left: BorderSide(color: Colors.grey, width: 4),
          ),
          backgroundColor: Colors.grey.shade50,
        ),
      },
      onLinkTap: (url, attributes, element) async {
        if (url != null) {
          try {
            final uri = Uri.parse(url);
            try {
              await launchUrl(uri, mode: LaunchMode.externalApplication);
            } catch (e) {
              //debugPrint('Error launching URL: $e');
            }
          } catch (e) {
            //debugPrint('Error launching URL: $e');
          }
        }
      },
    );

    // Wrap dengan container jika ada styling tambahan
    if (showBorder ||
        backgroundColor != null ||
        padding != null ||
        maxHeight != null) {
      htmlWidget = Container(
        width: double.infinity,
        constraints:
            maxHeight != null ? BoxConstraints(maxHeight: maxHeight!) : null,
        padding: padding ?? const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: backgroundColor,
          border: showBorder ? Border.all(color: Colors.grey.shade300) : null,
          borderRadius: showBorder ? BorderRadius.circular(8) : null,
        ),
        child: maxHeight != null
            ? SingleChildScrollView(child: htmlWidget)
            : htmlWidget,
      );
    }

    return htmlWidget;
  }
}

/// Widget untuk preview HTML dengan label
class HtmlPreviewComponent extends StatelessWidget {
  final String htmlContent;
  final double? fontSize;

  const HtmlPreviewComponent({
    super.key,
    required this.htmlContent,
    this.fontSize = 14,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          HtmlViewerComponent(
            htmlContent: htmlContent,
            fontSize: fontSize,
            showBorder: true,
            backgroundColor: Colors.grey.shade50,
            maxHeight: 300,
          ),
        ],
      ),
    );
  }
}
