import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class PegawaistrModel {
  int? id;
  int? idPegawai;
  int? idJenisprofesi;
  String? pegawainama;
  String? nama;
  String? namaprofesi;
  String? nomorstr;
  DateTime? tanggalstr;
  DateTime? tanggalberakhirstr;
  String? nomorsip;
  DateTime? tanggalsip;
  DateTime? tanggalberakhirsip;
  DateTime? tglpermintaan;
  bool? approval;
  String? alasanTolak;
  int? idPegawaiApproval;
  String? pegawaiapprovalnama;
  DateTime? tglapprove;
  String? status;

  PegawaistrModel();

  static PegawaistrModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static PegawaistrModel fromDynamic(dynamic dynamicData) {
    final model = PegawaistrModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.idJenisprofesi =
        MahasFormat.dynamicToInt(dynamicData['id_JenisProfesi']);
    model.nama = dynamicData['nama'];
    model.pegawainama = dynamicData['pegawaiNama'];
    model.namaprofesi = dynamicData['jenisProfesiNama'];
    model.nomorstr = dynamicData['nomorSTR'];
    model.tanggalstr = MahasFormat.dynamicToDateTime(dynamicData['tanggalSTR']);
    model.tanggalberakhirstr =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalBerakhirSTR']);
    model.nomorsip = dynamicData['nomorSIP'];
    model.tanggalsip = MahasFormat.dynamicToDateTime(dynamicData['tanggalSIP']);
    model.tanggalberakhirsip =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalBerakhirSIP']);
    model.tglpermintaan =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalRequest']);
    model.approval = MahasFormat.dynamicToBool(dynamicData['approved']);
    model.alasanTolak = dynamicData['alasanTolak'];
    model.idPegawaiApproval =
        MahasFormat.dynamicToInt(dynamicData['id_PegawaiApproval']);
    model.pegawaiapprovalnama = dynamicData['namaPegawaiApproval'];
    model.tglapprove =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalApproval']);
    model.status = dynamicData['status'];
    return model;
  }
}
