import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class ProfileRekeningModel {
  int? id;
  int? idPegawairekening;
  int? idPegawai;
  String? pegawainama;
  String? norekening;
  String? nama;
  int? idBank;
  String? namabank;
  bool? aktif;
  bool? prioritas;
  DateTime? tglpermintaan;
  bool? approval;
  String? alasantolak;
  int? idPegawaiApproval;
  String? pegawaiapprovalnama;
  DateTime? tglapprove;
  String? status;

  ProfileRekeningModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static ProfileRekeningModel fromDynamic(dynamic dynamicData) {
    final model = ProfileRekeningModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idPegawairekening =
        MahasFormat.dynamicToInt(dynamicData['id_PegawaiRekening']);
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.pegawainama = dynamicData['pegawaiNama'];
    model.norekening = dynamicData['noRekening'];
    model.nama = dynamicData['nama'];
    model.idBank = MahasFormat.dynamicToInt(dynamicData['id_Bank']);
    model.namabank = dynamicData['namaBank'];
    model.aktif = MahasFormat.dynamicToBool(dynamicData['aktif']);
    model.prioritas = MahasFormat.dynamicToBool(dynamicData['prioritas']);
    model.tglpermintaan =
        MahasFormat.dynamicToDateTime(dynamicData['tglPermintaan']);
    model.approval = MahasFormat.dynamicToBool(dynamicData['approval']);
    model.alasantolak = dynamicData['alasanTolak'];
    model.idPegawaiApproval =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Approval']);
    model.pegawaiapprovalnama = dynamicData['pegawaiApprovalNama'];
    model.tglapprove = MahasFormat.dynamicToDateTime(dynamicData['tglApprove']);
    model.status = dynamicData['status'];

    return model;
  }
}
