import 'dart:convert';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';

class CutitukarlemburModel {
  int? id;
  int? idDivisi;
  DateTime? tanggalinput;
  bool? approvekadiv;
  bool? approvemanager;
  String? alasan;
  String? alasantolak;
  int? idPegawaiRequest;
  String? divisi;
  String? pegawairequest;
  String? pegawaikadiv;
  String? pegawaimanager;
  int? idPegawaiKadiv;
  int? idPegawaiManager;
  List<CutitukarlemburDetailModel>? detail;

  CutitukarlemburModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static CutitukarlemburModel fromDynamic(dynamic dynamicData) {
    final model = CutitukarlemburModel();

    model.id = dynamicData['id'];
    model.idDivisi = dynamicData['id_Divisi'];
    model.tanggalinput =
        MahasFormat.stringToDateTime(dynamicData['tanggalInput']);
    model.approvekadiv = dynamicData['approveKadiv'];
    model.approvemanager = dynamicData['approveManager'];
    model.alasan = dynamicData['alasan'];
    model.alasantolak = dynamicData['alasanTolak'];
    model.idPegawaiRequest = dynamicData['id_Pegawai_Request'];
    model.divisi = dynamicData['divisi'];
    model.pegawairequest = dynamicData['pegawaiRequest'];
    model.pegawaikadiv = dynamicData['pegawaiKadiv'];
    model.pegawaimanager = dynamicData['pegawaiManager'];
    model.idPegawaiKadiv = dynamicData['id_Pegawai_Kadiv'];
    model.idPegawaiManager = dynamicData['id_Pegawai_Manager'];
    if (dynamicData['detail'] != null) {
      final detailT = dynamicData['detail'] as List;
      model.detail = [];
      for (var i = 0; i < detailT.length; i++) {
        model.detail!.add(CutitukarlemburDetailModel.fromDynamic(detailT[i]));
      }
    }

    return model;
  }
}
  
class CutitukarlemburDetailModel {
  int? id;
  DateTime? tanggal;
  bool? batal;
  String? alasanbatal;

  CutitukarlemburDetailModel();

  CutitukarlemburDetailModel.init(this.id, this.tanggal);

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static CutitukarlemburDetailModel fromDynamic(dynamic dynamicData) {
    final model = CutitukarlemburDetailModel();

    model.id = dynamicData['id'];
    model.tanggal = MahasFormat.stringToDateTime(dynamicData['tanggal']);
    model.batal = dynamicData['batal'];
    model.alasanbatal = dynamicData['alasanBatal'];

    return model;
  }
}


List<LookupCutiTukarLemburShiftModel> lookupCutiTukarLemburShiftModelFromJson(String str) =>
    List<LookupCutiTukarLemburShiftModel>.from(
        json.decode(str).map((x) => LookupCutiTukarLemburShiftModel.fromJson(x)));
class LookupCutiTukarLemburShiftModel {
  int? id;
  String? nama;
  String? kode;

  LookupCutiTukarLemburShiftModel({this.id, this.nama, this.kode});

  LookupCutiTukarLemburShiftModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    nama = json['nama'];
    kode = json['kode'];
  }
}

