import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/mahas_themes.dart';
import 'package:hr_portal/app/mahas/components/others/empty_component.dart';
import 'package:hr_portal/app/mahas/mahas_colors.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';
import 'package:hr_portal/app/mahas_complement/mahas_colors.dart';

import '../../../mahas/mahas_config.dart';
import '../controllers/permintaan_jadwal_lembur_custom_pegawai_setup_controller.dart';

class PermintaanJadwalLemburCustomPegawaiSetupView
    extends GetView<PermintaanJadwalLemburCustomPegawaiSetupController> {
  const PermintaanJadwalLemburCustomPegawaiSetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Approval'),
        centerTitle: true,
      ),
      body: Obx(
        () => Container(
          padding: const EdgeInsets.all(10),
          child: controller.isLoading.value
              ? const Center(child: CircularProgressIndicator())
              : controller.isEmpty.value == true
                  ? EmptyComponent(
                      onPressed: controller.onRefresh,
                    )
                  : Column(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 5, vertical: 5),
                          height: 52,
                          // width: 100,
                          decoration: const BoxDecoration(
                            color: MahasColor.greyInputan,
                            borderRadius: BorderRadius.all(
                              Radius.circular(14.0),
                            ),
                          ),
                          child: Row(
                            children: List.generate(
                              controller.menus.length,
                              (index) {
                                var item = controller.menus[index];

                                return Expanded(
                                  child: InkWell(
                                    onTap: () {
                                      controller.selectedIndex.value = index;
                                      item.onTab();
                                    },
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12.0,
                                        vertical: 8.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: index ==
                                                controller.selectedIndex.value
                                            ? MahasColors.primary
                                            : MahasColor.greyInputan,
                                        borderRadius: const BorderRadius.all(
                                          Radius.circular(8.0),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          item.title,
                                          style: TextStyle(
                                            color: index ==
                                                    controller
                                                        .selectedIndex.value
                                                ? Colors.white
                                                : MahasColors.primary,
                                            fontSize: 11.0,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        Expanded(
                          child: ListView.builder(
                            itemCount: controller.listData.length,
                            physics: const ScrollPhysics(),
                            itemBuilder: (BuildContext context, int i) {
                              var item = controller.listData[i];
                              return Container(
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: MahasColors.grey,
                                  ),
                                  borderRadius: BorderRadius.all(
                                    Radius.circular(MahasThemes.borderRadius),
                                  ),
                                ),
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 10),
                                margin: const EdgeInsets.only(bottom: 10),
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: InkWell(
                                        onTap: () =>
                                            controller.itemOnTab(item.id!),
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 8),
                                          child: Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Expanded(
                                                child: Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                        MahasConfig.kadivmanager
                                                                .kadiv ??
                                                            "Kepala Unit",
                                                        style:
                                                            MahasThemes.title),
                                                    Text(
                                                        item.namaPegawaiKadiv
                                                            .toString(),
                                                        style:
                                                            MahasThemes.title),
                                                    const SizedBox(height: 5),
                                                    Text(
                                                        "${item.lamaHari} Hari, ${item.lamaJam} Jam, ${item.lamaMenit} Menit"),
                                                    Text(item.keterangan ?? ""),
                                                  ],
                                                ),
                                              ),
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.end,
                                                children: [
                                                  Text(
                                                    MahasFormat.displayDate(
                                                        item.tanggalLembur),
                                                    style: MahasColor.muted,
                                                  ),
                                                  Text(
                                                    MahasFormat.displayTime(
                                                        item.jamLembur!),
                                                    style: MahasColor.muted,
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
        ),
      ),
    );
  }
}
