import 'dart:convert';

import 'package:hr_portal/app/mahas/services/mahas_format.dart';

class ApprovalModel {
  int? id;
  int? idPegawairequest;
  int? jenis;
  int? idDivisi;
  String? divisi;
  String? pegawairequest;
  DateTime? tanggalinput;
  int? idPegawaikadiv;
  int? idPegawaimanager;
  int? idPegawaipengganti;
  bool? approvekadiv;
  bool? approvemanager;
  bool? approvepengganti;
  bool? approve;
  bool? approveHeader;
  String? approvalMethod;
  DateTime? tanggalJam;
  int? lamaJam;

  ApprovalModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static ApprovalModel fromDynamic(dynamic dynamicData) {
    final model = ApprovalModel();

    model.id = dynamicData['id'];
    model.idPegawairequest = dynamicData['id_PegawaiRequest'];
    model.jenis = dynamicData['jenis'];
    model.idDivisi = dynamicData['id_Divisi'];
    model.divisi = dynamicData['divisi'];
    model.pegawairequest = dynamicData['pegawaiRequest'];
    model.tanggalinput =
        MahasFormat.stringToDateTime(dynamicData['tanggalInput']);
    model.idPegawaikadiv = dynamicData['id_PegawaiKadiv'];
    model.idPegawaimanager = dynamicData['id_PegawaiManager'];
    model.idPegawaipengganti = dynamicData['id_PegawaiPengganti'];
    model.approvekadiv = MahasFormat.dynamicToBool(dynamicData['approveKadiv']);
    model.approvemanager =
        MahasFormat.dynamicToBool(dynamicData['approveManager']);
    model.approvepengganti =
        MahasFormat.dynamicToBool(dynamicData['approvePengganti']);
    model.approve = MahasFormat.dynamicToBool(dynamicData['approve']);
    model.approveHeader =
        MahasFormat.dynamicToBool(dynamicData['approveHeader']);
    model.approvalMethod = dynamicData['approvalMethod'];
    model.tanggalJam = MahasFormat.stringToDateTime(dynamicData['tanggalJam']);
    model.lamaJam = MahasFormat.dynamicToInt(dynamicData['lamaJam']);

    return model;
  }
}
