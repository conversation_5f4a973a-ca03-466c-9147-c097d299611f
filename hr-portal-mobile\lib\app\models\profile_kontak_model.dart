import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class ProfilekontakModel {
  int? id;
  int? idPegawaiKontak;
  int? idPegawai;
  String? pegawainama;
  int? idJeniskontak;
  String? namajeniskontak;
  String? nama;
  DateTime? tglpermintaan;
  bool? approval;
  String? alasanTolak;
  int? idPegawaiApproval;
  String? pegawaiapprovalnama;
  DateTime? tglapprove;
  String? status;

  ProfilekontakModel();

  static ProfilekontakModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static ProfilekontakModel fromDynamic(dynamic dynamicData) {
    final model = ProfilekontakModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idPegawaiKontak =
        MahasFormat.dynamicToInt(dynamicData['id_PegawaiKontak']);
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.pegawainama = dynamicData['pegawaiNama'];
    model.idJeniskontak =
        MahasFormat.dynamicToInt(dynamicData['id_JenisKontak']);
    model.namajeniskontak = dynamicData['namaJenisKontak'];
    model.nama = dynamicData['nama'];
    model.tglpermintaan =
        MahasFormat.dynamicToDateTime(dynamicData['tglPermintaan']);
    model.approval = MahasFormat.dynamicToBool(dynamicData['approval']);
    model.alasanTolak = dynamicData['alasanTolak'];
    model.idPegawaiApproval =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Approval']);
    model.pegawaiapprovalnama = dynamicData['pegawaiApprovalNama'];
    model.tglapprove = MahasFormat.dynamicToDateTime(dynamicData['tglApprove']);
    model.status = dynamicData['status'];

    return model;
  }
}
