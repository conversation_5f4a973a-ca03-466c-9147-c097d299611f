import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/services/http_api.dart';

import '../../../models/bios_model.dart';

class ProfileKategoribiosController extends GetxController {
  Rx<BiosModel> dataCon = BiosModel().obs;

  @override
  void onInit() {
    getData();
    super.onInit();
  }

  void getData() async {
    var r = await HttpApi.get('/api/PegawaiKategoriBios');
    if (r.success) {
      dataCon.value = BiosModel.fromJson(r.body);
      update();
    }
  }
}
