import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';

import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/others/list_component.dart';
import '../../../models/profile_model.dart';
import '../controllers/profile_foto_history_controller.dart';

class ProfileFotoHistoryView extends GetView<ProfileFotoHistoryController> {
  const ProfileFotoHistoryView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('History Foto'),
        centerTitle: true,
      ),
      body: ListComponent<HistoryProfileModel>(
        controller: controller.listCon,
        itemBuilder: (e) {
          return ListTile(
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(MahasFormat.displayDate(e.tanggalrequest),
                    style: MahasThemes.title),
                Text(
                    e.approved == null
                        ? "Menunggu"
                        : e.approved == false
                            ? "Ditolak"
                            : "Diterima",
                    style: MahasThemes.muted)
              ],
            ),
            onTap: () => controller.itemOnTab(e.id!),
          );
        },
      ),
    );
  }
}
