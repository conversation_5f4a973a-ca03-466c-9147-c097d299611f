import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/mahas_themes.dart';
import 'package:hr_portal/app/mahas/mahas_colors.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

import '../controllers/qr_code_scanner_controller.dart';

class QrCodeScannerView extends GetView<QrCodeScannerController> {
  const QrCodeScannerView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Scan QR Code'),
        centerTitle: true,
      ),
      body: MobileScanner(
        controller: controller.cameraController,
        errorBuilder: (context, err, widget) => AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                FontAwesomeIcons.triangleExclamation,
                color: MahasColors.yellow,
                size: 40,
              ),
              const Padding(padding: EdgeInsets.all(7)),
              Text(
                err.errorDetails?.code == null
                    ? "<PERSON><PERSON><PERSON><PERSON> k<PERSON>\nPastikan anda memberikan izin kamera pada aplikasi ini"
                    : "${err.errorDetails?.code} - ${err.errorDetails?.message}",
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        onDetect: (capture) {
          controller.codeOnDetect(capture);
        },
        overlayBuilder: (context, constraints) {
          return Stack(
            children: [
              Container(
                padding: const EdgeInsets.all(35),
                child: Center(
                  child: CustomPaint(
                    foregroundPainter: BorderPainter(),
                    child: SizedBox(
                      width: Get.width,
                      height: Get.width - 70,
                    ),
                  ),
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                child: Column(
                  children: [
                    Card(
                      shape: MahasThemes.cardBorderShape,
                      elevation: 5,
                      color: MahasColors.light,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 5),
                        width: Get.width,
                        child: Text(
                          controller.note,
                          maxLines: 2,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    const Expanded(
                      child: SizedBox(),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 10, horizontal: 5),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          InkWell(
                            onTap: () =>
                                controller.cameraController.toggleTorch(),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(50),
                              child: Container(
                                padding: const EdgeInsets.all(15),
                                color: MahasColors.primary,
                                child: const Icon(
                                  FontAwesomeIcons.bolt,
                                  size: 25,
                                  color: MahasColors.light,
                                ),
                              ),
                            ),
                          ),
                          InkWell(
                            onTap: () =>
                                controller.cameraController.switchCamera(),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(50),
                              child: Container(
                                padding: const EdgeInsets.all(15),
                                color: MahasColors.primary,
                                child: const Icon(
                                  FontAwesomeIcons.camera,
                                  size: 25,
                                  color: MahasColors.light,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

class BorderPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    double sh = size.height; // for convenient shortage
    double sw = size.width; // for convenient shortage
    double cornerSide = sh * 0.075; // desirable value for corners side

    Paint paint = Paint()
      ..color = MahasColors.light
      ..strokeWidth = 5
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    Path path = Path()
      ..moveTo(cornerSide, 0)
      ..quadraticBezierTo(0, 0, 0, cornerSide)
      ..moveTo(0, sh - cornerSide)
      ..quadraticBezierTo(0, sh, cornerSide, sh)
      ..moveTo(sw - cornerSide, sh)
      ..quadraticBezierTo(sw, sh, sw, sh - cornerSide)
      ..moveTo(sw, cornerSide)
      ..quadraticBezierTo(sw, 0, sw - cornerSide, 0);

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(BorderPainter oldDelegate) => false;

  @override
  bool shouldRebuildSemantics(BorderPainter oldDelegate) => false;
}
