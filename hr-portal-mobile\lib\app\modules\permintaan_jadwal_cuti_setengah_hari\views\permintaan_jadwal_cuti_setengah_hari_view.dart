import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:get/get.dart';

import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/others/list_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../mahas_complement/approval_status.dart';
import '../../../mahas_complement/mahas_colors.dart';
import '../../../models/cuti_setengah_hari.dart';
import '../controllers/permintaan_jadwal_cuti_setengah_hari_controller.dart';

class PermintaanJadwalCutiSetengahHariView
    extends GetView<PermintaanJadwalCutiSetengahHariController> {
  const PermintaanJadwalCutiSetengahHariView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Cuti Setengah Hari"),
        centerTitle: true,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 10),
            child: GestureDetector(
              onTap: controller.addOnPress,
              child: const Icon(
                FontAwesomeIcons.plus,
                size: 24,
              ),
            ),
          ),
        ],
      ),
      body: controller.isDinamis == false ? listStatis() : listDinamis(),
    );
  }

  ListComponent<CutiSetengahHariModel> listStatis() {
    return ListComponent(
        controller: controller.listCon,
        itemBuilder: (CutiSetengahHariModel e) {
          return InkWell(
            onTap: () => controller.itemOnTab(e.id!),
            child: Padding(
              padding:
                  const EdgeInsets.only(left: 12, right: 12, top: 8, bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(e.alasan ?? "Cuti Setengah Hari",
                            style: MahasThemes.title),
                        const SizedBox(height: 5),
                        Visibility(
                          visible:
                              MahasConfig.dinamisForm.approval?.hanyaManager ==
                                  false,
                          child: Column(
                            children: [
                              const SizedBox(height: 5),
                              ApprovalStatusWidget(
                                pegawai: e.pegawaikadiv.toString(),
                                approveStatus: e.approvekadiv,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 5),
                        ApprovalStatusWidget(
                          pegawai: e.pegawaimanager.toString(),
                          approveStatus: e.approvemanager,
                        ),
                      ],
                    ),
                  ),
                  Text(
                    MahasFormat.displayDate(e.tanggalinput),
                    style: MahasColor.muted,
                  ),
                ],
              ),
            ),
          );
        });
  }

  ListComponent<CutiSetengahHariModel> listDinamis() {
    return ListComponent(
      controller: controller.listCon,
      itemBuilder: (CutiSetengahHariModel e) {
        return Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => controller.itemOnTab(e.id!),
                child: Padding(
                  padding: const EdgeInsets.only(
                      left: 12, right: 12, top: 12, bottom: 0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(e.alasan ?? "Cuti Tahunan",
                          style: MahasThemes.title),
                      const SizedBox(height: 15),
                    ],
                  ),
                ),
              ),
            ),
            InkWell(
              onTap: () async {
                await Helper.statusApproval(
                  "/api/PermintaanJadwal/CutiTahunan/Approval/${e.id}",
                  alasanTolak: e.alasantolak,
                );
              },
              child: Padding(
                padding: const EdgeInsets.only(
                    left: 12, right: 12, top: 12, bottom: 0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      MahasFormat.displayDate(e.tanggalinput),
                      style: MahasColor.muted,
                    ),
                    Text(
                      "Lihat Status",
                      style: TextStyle(
                        color: MahasColor.primaryColor.shade300,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
