import 'dart:convert';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';

class CutiDayPaymentModel {
  int? id;
  int? idDivisi;
  DateTime? tanggalinput;
  bool? approvekadiv;
  bool? approvemanager;
  String? alasantolak;
  int? idPegawaiRequest;
  DateTime? daritanggal;
  DateTime? sampaitanggal;
  int? jumlahhari;
  String? jenis;
  String? keterangan;
  int? idShift;
  String? divisi;
  String? pegawairequest;
  String? pegawaikadiv;
  String? pegawaimanager;
  String? approvemethod;
  int? idPegawaiKadiv;
  int? idPegawaiManager;
  int? idUnitbisnis;
  List<CutiDayPaymentDetailModel>? detail;
  List<CutiDayPaymentDetailModel>? detailPengganti;

  CutiDayPaymentModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static CutiDayPaymentModel fromDynamic(dynamic dynamicData) {
    final model = CutiDayPaymentModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idDivisi = MahasFormat.dynamicToInt(dynamicData['id_Divisi']);
    model.tanggalinput =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalInput']);
    model.approvekadiv = MahasFormat.dynamicToBool(dynamicData['approveKadiv']);
    model.approvemanager =
        MahasFormat.dynamicToBool(dynamicData['approveManager']);
    model.alasantolak = dynamicData['alasanTolak'];
    model.idPegawaiRequest =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Request']);
    model.daritanggal =
        MahasFormat.dynamicToDateTime(dynamicData['dariTanggal']);
    model.sampaitanggal =
        MahasFormat.dynamicToDateTime(dynamicData['sampaiTanggal']);
    model.jumlahhari = MahasFormat.dynamicToInt(dynamicData['jumlahHari']);
    model.jenis = dynamicData['jenis'];
    model.keterangan = dynamicData['keterangan'];
    model.idShift = MahasFormat.dynamicToInt(dynamicData['id_Shift']);
    model.divisi = dynamicData['divisi'];
    model.pegawairequest = dynamicData['pegawaiRequest'];
    model.pegawaikadiv = dynamicData['pegawaiKadiv'];
    model.pegawaimanager = dynamicData['pegawaiManager'];
    model.approvemethod = dynamicData['approveMethod'];
    model.idPegawaiKadiv =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Kadiv']);
    model.idPegawaiManager =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Manager']);
    model.idUnitbisnis = MahasFormat.dynamicToInt(dynamicData['id_UnitBisnis']);
    if (dynamicData['detail'] != null) {
      final detailT = dynamicData['detail'] as List;
      model.detail = [];
      for (var i = 0; i < detailT.length; i++) {
        model.detail!.add(CutiDayPaymentDetailModel.fromDynamic(detailT[i]));
      }
    }
    if (dynamicData['detailPengganti'] != null) {
      final detailP = dynamicData['detailPengganti'] as List;
      model.detailPengganti = [];
      for (var i = 0; i < detailP.length; i++) {
        model.detailPengganti!
            .add(CutiDayPaymentDetailModel.fromDynamic(detailP[i]));
      }
    }

    return model;
  }
}

class CutiDayPaymentDetailModel {
  int? id;
  DateTime? tanggal;

  CutiDayPaymentDetailModel();

  CutiDayPaymentDetailModel.init(this.id, this.tanggal);

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static CutiDayPaymentDetailModel fromDynamic(dynamic dynamicData) {
    final model = CutiDayPaymentDetailModel();

    model.id = dynamicData['id'];
    model.tanggal = MahasFormat.stringToDateTime(dynamicData['tanggal']);

    return model;
  }
}

List<LookupCutiDayPaymentShiftModel> lookupCutiDayPaymentShiftModelFromJson(
        String str) =>
    List<LookupCutiDayPaymentShiftModel>.from(json
        .decode(str)
        .map((x) => LookupCutiDayPaymentShiftModel.fromJson(x)));

class LookupCutiDayPaymentShiftModel {
  int? id;
  String? nama;
  String? kode;

  LookupCutiDayPaymentShiftModel({this.id, this.nama, this.kode});

  LookupCutiDayPaymentShiftModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    nama = json['nama'];
    kode = json['kode'];
  }
}

class CutiDayPaymentApprovalModel {
  int? idPegawaiapprove;
  String? pegawaiapprovenama;
  int? tingkatan;
  bool? approve;

  CutiDayPaymentApprovalModel();

  static CutiDayPaymentApprovalModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static CutiDayPaymentApprovalModel fromDynamic(dynamic dynamicData) {
    final model = CutiDayPaymentApprovalModel();

    model.idPegawaiapprove =
        MahasFormat.dynamicToInt(dynamicData['id_PegawaiApprove']);
    model.pegawaiapprovenama = dynamicData['pegawaiApproveNama'];
    model.tingkatan = MahasFormat.dynamicToInt(dynamicData['tingkatan']);
    model.approve = MahasFormat.dynamicToBool(dynamicData['approve']);

    return model;
  }
}

class ShiftDayPaymentModel {
  int? id;
  String? nama;
  String? kode;

  ShiftDayPaymentModel();

  static ShiftDayPaymentModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static ShiftDayPaymentModel fromDynamic(dynamic dynamicData) {
    final model = ShiftDayPaymentModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.nama = dynamicData['nama'];
    model.kode = dynamicData['kode'];

    return model;
  }
}
