import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/others/list_component.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../models/pangkat_history_model.dart';
import '../controllers/profile_pegawai_pangkat_controller.dart';

class ProfilePegawaiPangkatView
    extends GetView<ProfilePegawaiPangkatController> {
  const ProfilePegawaiPangkatView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('History Pangkat'),
        centerTitle: true,
      ),
      body: ListComponent(
        controller: controller.listCon,
        itemBuilder: (PangkathistoryModel e) {
          return InkWell(
            onTap: () => controller.itemOnTab(e.id!),
            child: Padding(
              padding:
                  const EdgeInsets.only(left: 12, right: 12, top: 8, bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 5),
                        Text("Nomor KEP : ${e.nomorkep ?? "-"}",
                            style: MahasThemes.title),
                        Text(
                          "Pangkat : ${e.idPangkatText}",
                          style: MahasThemes.muted,
                        ),
                        Text(
                          "Terhitung mulai : ${MahasFormat.displayDate(e.terhitungmulaitanggal)}",
                          style: MahasThemes.muted,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
