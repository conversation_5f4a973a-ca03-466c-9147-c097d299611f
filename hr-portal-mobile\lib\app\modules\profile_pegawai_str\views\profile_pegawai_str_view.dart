import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/others/list_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../models/profile_pegawai_str_model.dart';
import '../controllers/profile_pegawai_str_controller.dart';

class ProfilePegawaiStrView extends GetView<ProfilePegawaiStrController> {
  const ProfilePegawaiStrView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Data STR dan SIP'),
        centerTitle: true,
        actions: <Widget>[
          GetBuilder<ProfilePegawaiStrController>(
            builder: (controller) {
              return PopupMenuButton(
                onSelected: controller.popupMenuButtonOnSelected,
                itemBuilder: (context) {
                  List<PopupMenuItem<String>> r = [];
                  r.add(
                    const PopupMenuItem(
                      value: 'Tambah',
                      child: Text('Tambah'),
                    ),
                  );
                  if (MahasConfig.hasHistory) {
                    r.add(
                      const PopupMenuItem(
                        value: 'History',
                        child: Text('History'),
                      ),
                    );
                  }

                  return r;
                },
              );
            },
          ),
        ],
      ),
      body: ListComponent(
        controller: controller.listCon,
        itemBuilder: (PegawaistrModel e) {
          return InkWell(
            onTap: () => controller.itemOnTab(e.id!),
            child: Padding(
              padding:
                  const EdgeInsets.only(left: 12, right: 12, top: 8, bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 5),
                        Row(
                          children: [
                            SizedBox(
                              width: 80,
                              child: Text("Jenis Profesi",
                                  style: MahasThemes.title),
                            ),
                            Text(": ${e.nama.toString()}",
                                style: MahasThemes.title),
                          ],
                        ),
                        Row(
                          children: [
                            SizedBox(
                              width: 80,
                              child:
                                  Text("Nomor STR", style: MahasThemes.title),
                            ),
                            Text(": ${e.nomorstr.toString()}"),
                          ],
                        ),
                        Row(
                          children: [
                            SizedBox(
                              width: 80,
                              child:
                                  Text("Nomor SIP", style: MahasThemes.title),
                            ),
                            Text(": ${e.nomorsip.toString()}"),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
