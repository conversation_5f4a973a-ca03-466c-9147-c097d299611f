import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/others/empty_component.dart';
import 'package:hr_portal/app/mahas/components/others/shimmer_component.dart';
import 'package:hr_portal/app/mahas/mahas_colors.dart';

import '../../../mahas/components/mahas_themes.dart';
import '../controllers/informasi_perpus_controller.dart';

class InformasiPerpusView extends GetView<InformasiPerpusController> {
  const InformasiPerpusView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('E-Perpus'),
        centerTitle: true,
        actions: [
          InkWell(
            onTap: controller.filterOnTap,
            child: SizedBox(
              width: 50,
              height: Get.height,
              child: const Center(
                child: Icon(
                  FontAwesomeIcons.filter,
                  size: 20,
                  color: MahasColors.light,
                ),
              ),
            ),
          )
        ],
      ),
      body: Obx(
        () => Container(
          child: controller.isLoading.value
              ? const ShimmerComponent()
              : controller.uniqueCategories.isEmpty
                  ? EmptyComponent(onPressed: controller.onRefresh)
                  : ListView.builder(
                      itemCount: controller.uniqueCategories.length,
                      itemBuilder: (context, index) {
                        var currentCategory =
                            controller.uniqueCategories[index];
                        var categoryData = controller.listData
                            .where((data) =>
                                data.kategoriperpusnama == currentCategory)
                            .toList();

                        return ExpansionTile(
                          textColor: MahasColors.primary,
                          iconColor: MahasColors.primary,
                          title: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 5,
                                  vertical: 5,
                                ),
                                decoration: BoxDecoration(
                                  color: MahasColors.primary,
                                  borderRadius: BorderRadius.circular(
                                    MahasThemes.borderRadius,
                                  ),
                                ),
                                child: Center(
                                  child: Text(
                                    categoryData.length.toString(),
                                    style: const TextStyle(
                                      color: MahasColors.light,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(
                                width: 10,
                              ),
                              SizedBox(
                                width: Get.width * 0.65,
                                child: Text(
                                  currentCategory,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w600,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                ),
                              ),
                            ],
                          ),
                          children: categoryData
                              .map((data) => ListTile(
                                    title: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "Tentang : ",
                                          style: MahasThemes.title,
                                        ),
                                        SizedBox(
                                          width: Get.width * 0.6,
                                          child: Text(
                                            data.tentang!,
                                            style: MahasThemes.title,
                                            overflow: TextOverflow.ellipsis,
                                            maxLines: 2,
                                          ),
                                        ),
                                      ],
                                    ),
                                    subtitle: Row(
                                      children: [
                                        const Text(
                                          "Nomor : ",
                                        ),
                                        Text(data.nomor.toString()),
                                      ],
                                    ),
                                    onTap: () {
                                      controller.itemOnTab(data.id!);
                                    },
                                  ))
                              .toList(),
                        );
                      },
                    ),
        ),
      ),
    );
  }
}
