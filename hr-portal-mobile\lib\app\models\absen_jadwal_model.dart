import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';

AbsenJadwal absenJadwalFromJson(String str) =>
    AbsenJadwal.fromJson(json.decode(str));

class AbsenJadwal {
  AbsenJadwal({
    this.jadwal,
    this.lembur,
  });

  List<Jadwal>? jadwal;
  List<Lembur>? lembur;

  factory AbsenJadwal.fromJson(Map<String, dynamic> json) => AbsenJadwal(
        jadwal:
            List<Jadwal>.from(json["jadwal"].map((x) => Jadwal.fromJson(x))),
        lembur:
            List<Lembur>.from(json["lembur"].map((x) => Lembur.fromJson(x))),
      );
}

class Jadwal {
  Jadwal({
    this.id,
    this.tanggal,
    this.idDivisi,
    this.kode,
    this.nama,
    this.kodeWarna,
    this.jamMulai,
    this.jamSelesai,
    this.shiftKe2,
    this.jamMulaiKe2,
    this.jamSelesaiKe2,
    this.shiftKe3,
    this.jamMulaiKe3,
    this.jamSelesaiKe3,
    this.divisi,
  });

  int? id;
  DateTime? tanggal;
  int? idDivisi;
  String? kode;
  String? nama;
  String? kodeWarna;
  TimeOfDay? jamMulai;
  TimeOfDay? jamSelesai;
  bool? shiftKe2;
  TimeOfDay? jamMulaiKe2;
  TimeOfDay? jamSelesaiKe2;
  bool? shiftKe3;
  TimeOfDay? jamMulaiKe3;
  TimeOfDay? jamSelesaiKe3;
  String? divisi;

  factory Jadwal.fromJson(Map<String, dynamic> json) => Jadwal(
        id: json["id"],
        tanggal: MahasFormat.stringToDateTime(json["tanggal"]),
        idDivisi: json["id_Divisi"],
        kode: json["kode"],
        nama: json["nama"],
        kodeWarna: json["kodeWarna"],
        jamMulai: MahasFormat.stringToTime(json["jamMulai"]),
        jamSelesai: MahasFormat.stringToTime(json["jamSelesai"]),
        shiftKe2: json["shiftKe2"],
        jamMulaiKe2: MahasFormat.stringToTime(json["jamMulaiKe2"]),
        jamSelesaiKe2: MahasFormat.stringToTime(json["jamSelesaiKe2"]),
        shiftKe3: json["shiftKe3"],
        jamMulaiKe3: MahasFormat.stringToTime(json["jamMulaiKe3"]),
        jamSelesaiKe3: MahasFormat.stringToTime(json["jamSelesaiKe3"]),
        divisi: json["divisi"],
      );
}

class Lembur {
  Lembur({
    this.id,
    this.idDivisi,
    this.alasan,
    this.tanggalJam,
    this.lamaJam,
    this.jenis,
    this.divisi,
  });

  int? id;
  int? idDivisi;
  String? alasan;
  DateTime? tanggalJam;
  int? lamaJam;
  String? jenis;
  String? divisi;

  factory Lembur.fromJson(Map<String, dynamic> json) => Lembur(
        id: json["id"],
        idDivisi: json["id_Divisi"],
        alasan: json["alasan"],
        tanggalJam: MahasFormat.stringToDateTime(json["tanggalJam"]),
        lamaJam: json["lamaJam"],
        jenis: json["jenis"],
        divisi: json["divisi"],
      );
}
