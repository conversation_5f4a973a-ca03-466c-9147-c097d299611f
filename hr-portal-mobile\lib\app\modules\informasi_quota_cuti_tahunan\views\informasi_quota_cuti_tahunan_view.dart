import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/mahas_themes.dart';
import 'package:hr_portal/app/mahas/components/others/shimmer_component.dart';

import '../../../mahas/mahas_colors.dart';
import '../controllers/informasi_quota_cuti_tahunan_controller.dart';

class InformasiQuotaCutiTahunanView
    extends GetView<InformasiQuotaCutiTahunanController> {
  const InformasiQuotaCutiTahunanView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cuti Tahunan'),
        centerTitle: true,
      ),
      body: RefreshIndicator(
        backgroundColor: Colors.white,
        onRefresh: controller.onRefresh,
        child: ListView(
          children: [
            Container(
              padding: const EdgeInsets.only(top: 10, left: 10, right: 10),
              child: Obx(
                () => Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Visibility(
                      visible: controller.isLoading.value,
                      child: const ShimmerComponent(),
                    ),
                    Visibility(
                      visible: !controller.isLoading.value,
                      child: Column(
                        children: controller.models
                            .map(
                              (e) => Container(
                                padding: const EdgeInsets.all(10),
                                margin: const EdgeInsets.only(bottom: 10),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color:
                                        MahasColors.grey.withValues(alpha: .5),
                                  ),
                                  borderRadius: BorderRadius.circular(
                                      MahasThemes.borderRadius),
                                ),
                                child: Column(
                                  crossAxisAlignment:
                                      CrossAxisAlignment.stretch,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Text("Tahun", style: MahasThemes.muted),
                                    Text("${e.tahun}"),
                                    const Padding(padding: EdgeInsets.all(5)),
                                    Text("Quota", style: MahasThemes.muted),
                                    Text("${e.quota}"),
                                    const Padding(padding: EdgeInsets.all(5)),
                                    Text("Terpakai", style: MahasThemes.muted),
                                    Text("${e.terpakai}"),
                                    const Padding(padding: EdgeInsets.all(5)),
                                    Text("Sisa Cuti", style: MahasThemes.muted),
                                    Text("${e.sisaCuti}"),
                                    const Padding(padding: EdgeInsets.all(10)),
                                  ],
                                ),
                              ),
                            )
                            .toList(),
                      ),
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
