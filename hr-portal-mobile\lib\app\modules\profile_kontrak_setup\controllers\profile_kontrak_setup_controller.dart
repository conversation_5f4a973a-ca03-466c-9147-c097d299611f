import 'dart:convert';

import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../models/profile_kontrak_model.dart';

class ProfileKontrakSetupController extends GetxController {
  late SetupPageController formCon;

  final tglBergabungCon = InputDatetimeController();
  final tglBerlakuCon = InputDatetimeController();
  final tglBerakhirCon = InputDatetimeController();
  final expiredKontrakCon = InputTextController(
    type: InputTextType.number,
  );
  final waktuBekerjaCon = InputTextController(
    type: InputTextType.number,
  );
  final expiredPensiunCon = InputTextController(
    type: InputTextType.number,
  );
  final noSuratCon = InputTextController();
  final noSkCon = InputTextController();
  final namaJabatanCon = InputTextController();

  @override
  void onInit() {
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PegawaiKontrak/$id',
      allowEdit: false,
      allowDelete: false,
      bodyApi: (id) => {},
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (!tglBergabungCon.isValid) return false;

        return true;
      },
      apiToView: (json) {
        KontrakModel model = KontrakModel.fromJson(json);
        tglBergabungCon.value = model.tanggalgabung;
        tglBerlakuCon.value = model.tanggalberlaku;
        tglBerakhirCon.value = model.tanggalberakhir;
        expiredKontrakCon.value = model.sisahariexpiredkontrak;
        waktuBekerjaCon.value = model.lamawaktubekerja;
        expiredPensiunCon.value = model.sisahariexpiredpensiun;
        noSuratCon.value = model.nosurat;
        noSkCon.value = model.nosk;
        namaJabatanCon.value = model.namajabatan;
      },
    );

    super.onInit();
  }
}
