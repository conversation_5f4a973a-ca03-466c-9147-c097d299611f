import 'dart:convert';

import 'package:hr_portal/app/mahas/services/mahas_format.dart';

class MutasiBarangModel {
  int? id;
  String? noBukti;
  String? urlFaktur;
  bool? approve;
  int? idPegawaiApproval;
  DateTime? tanggal;
  DateTime? tanggalApprove;

  MutasiBarangModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static MutasiBarangModel fromDynamic(dynamic dynamicData) {
    final model = MutasiBarangModel();

    model.id = dynamicData['id'];
    model.noBukti = dynamicData['noBukti'];
    model.urlFaktur = dynamicData['urlFaktur'];
    model.approve = dynamicData['approve'];
    model.idPegawaiApproval = dynamicData['id_PegawaiApproval'];
    model.tanggal = MahasFormat.stringToDateTime(dynamicData['tanggal']);
    model.tanggalApprove =
        MahasFormat.stringToDateTime(dynamicData['tanggalApprove']);

    return model;
  }
}
