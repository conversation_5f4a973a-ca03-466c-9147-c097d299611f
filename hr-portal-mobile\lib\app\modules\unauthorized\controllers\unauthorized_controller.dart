// ignore_for_file: unused_local_variable

import 'package:get/get.dart';
import '../../../controllers/auth_controller.dart';

class UnauthorizedController extends GetxController {
  void singOutOnPress() {
    AuthController.instance.signOut();
  }

  late String isiPesanCon;

  @override
  void onInit() {
    cekStatus();
    super.onInit();
  }

  void cekStatus() {
    final status = Get.parameters['status'].toString();
    final message = Get.parameters['message'].toString();
    isiPesanCon = message;
  }
}
