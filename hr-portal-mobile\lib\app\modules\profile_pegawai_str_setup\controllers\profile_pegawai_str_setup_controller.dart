import 'dart:convert';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/models/api_list_resut_model.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../models/profesi_model.dart';
import '../../../models/profile_pegawai_str_model.dart';
import '../../../routes/app_pages.dart';

class ProfilePegawaiStrSetupController extends GetxController {
  late SetupPageController formCon;
  final profesiCon = InputDropdownController();
  final noStrCon = InputTextController();
  final tglStrCon = InputDatetimeController();
  final tglBerakhirStrCon = InputDatetimeController();
  final noSipCon = InputTextController();
  final tglSipCon = InputDatetimeController();
  final tglBerakhirSipCon = InputDatetimeController();

  RxInt idPegawaiStr = 0.obs;

  @override
  void onInit() {
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PegawaiStr/$id',
      urlApiPost: () => '/api/PegawaiStr',
      urlApiPut: (id) => '/api/PegawaiStr/$id',
      urlApiDelete: (id) => '/api/PegawaiStr/$id',
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "id_JenisProfesi": profesiCon.value,
        "id_PegawaiStr": idPegawaiStr.value,
        "nomorSTR": noStrCon.value,
        "tanggalSTR": MahasFormat.dateToString(tglStrCon.value),
        "tanggalBerakhirSTR": MahasFormat.dateToString(tglBerakhirStrCon.value),
        "nomorSIP": noSipCon.value,
        "tanggalSIP": MahasFormat.dateToString(tglSipCon.value),
        "tanggalBerakhirSIP": MahasFormat.dateToString(tglBerakhirSipCon.value),
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      apiToView: (json) {
        PegawaistrModel model = PegawaistrModel.fromJson(json);

        //mahascomponent
        profesiCon.value = model.idJenisprofesi;
        noStrCon.value = model.nomorstr;
        tglStrCon.value = model.tanggalstr;
        tglBerakhirStrCon.value = model.tanggalberakhirstr;
        noSipCon.value = model.nomorsip;
        tglSipCon.value = model.tanggalsip;
        tglBerakhirSipCon.value = model.tanggalberakhirsip;

        //non mahas
        idPegawaiStr.value = model.id ?? 0;
      },
      onBeforeSubmit: () {
        if (!profesiCon.isValid) return false;
        if (!noStrCon.isValid) return false;
        if (!tglStrCon.isValid) return false;
        if (!tglBerakhirStrCon.isValid) return false;
        if (!noSipCon.isValid) return false;
        if (!tglSipCon.isValid) return false;
        if (!tglBerakhirSipCon.isValid) return false;
        return true;
      },
      onInit: () async {
        await getProfesi();
      },
    );

    if (MahasConfig.hasHistory) {
      formCon.onSuccessSubmit = (r) {
        var idHistory = json.decode(r.body)['id'];
        Get.toNamed(
          Routes.PROFILE_PEGAWAI_STR_HISTORY_SETUP,
          parameters: {
            'id': idHistory.toString(),
            'showStatus': true.toString(),
            'withActionBack': true.toString(),
          },
        );
      };
      formCon.deleteOnTap = (id) {
        Get.toNamed(
          Routes.PROFILE_PEGAWAI_STR_HISTORY_SETUP,
          parameters: {
            'id': id.toString(),
            'showStatus': true.toString(),
            'withActionBack': true.toString(),
          },
        );
      };
    }

    super.onInit();
  }

  Future<void> getProfesi() async {
    if (EasyLoading.isShow) return;
    EasyLoading.show();
    var r = await HttpApi.get("/api/JenisProfesi");
    if (r.success) {
      RxList<ProfesiModel> listModel = RxList<ProfesiModel>();
      ApiResultListModel model = ApiResultListModel.fromJson(r.body);
      for (var e in (model.datas ?? [])) {
        listModel.add(ProfesiModel.fromDynamic(e));
      }
      if (profesiCon.items.isNotEmpty) {
        profesiCon.items.clear();
      }
      if (listModel.isNotEmpty && profesiCon.items.isEmpty) {
        profesiCon.items = listModel
            .map<DropdownItem>(
              (e) => DropdownItem.init(e.nama, e.id),
            )
            .toList();
      }
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }
}
