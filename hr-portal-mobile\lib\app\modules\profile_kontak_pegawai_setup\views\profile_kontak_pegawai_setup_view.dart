import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../controllers/profile_kontak_pegawai_setup_controller.dart';

class ProfileKontakPegawaiSetupView
    extends GetView<ProfileKontakPegawaiSetupController> {
  const ProfileKontakPegawaiSetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      controller: controller.formCon,
      title: "Informasi",
      children: () => [
        InputDropdownComponent(
          label: "<PERSON><PERSON>",
          controller: controller.jenisKontakCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        Obx(
          () => InputTextComponent(
            label: controller.namaLabel.value,
            controller: controller.namaCon,
            required: true,
            editable: controller.formCon.editable,
          ),
        ),
      ],
    );
  }
}
