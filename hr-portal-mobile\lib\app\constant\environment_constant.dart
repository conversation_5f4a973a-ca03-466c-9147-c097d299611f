import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:package_info_plus/package_info_plus.dart';

enum MahasEnvironmentType {
  sanata,
  devsanata,
}

class EnvironmentConstant {
  static String imageLogo = "";
  static String localNotifImage = "";
  static String localNotifChannelId = "";

  static void environment() {
    if (MahasConfig.currentEnv == MahasEnvironmentType.sanata) {
      imageLogo = "assets/images/logonobgsanata.png";
      localNotifImage = "@drawable/logosanata";
      localNotifChannelId = "com.sanata.hrportal";
    } else if (MahasConfig.currentEnv == MahasEnvironmentType.devsanata) {
      imageLogo = "assets/images/logonobgsanata.png";
      localNotifImage = "@drawable/logosanata";
      localNotifChannelId = "com.sanata.hrportal.dev";
    }
  }

  static Future<MahasEnvironmentType> currentEnv() async {
    final packageInfo = await PackageInfo.fromPlatform();

    if (packageInfo.packageName == "com.sanata.hrportal") {
      return MahasEnvironmentType.sanata;
    } else if (packageInfo.packageName == "com.sanata.hrportal.dev") {
      return MahasEnvironmentType.devsanata;
    } else {
      return MahasEnvironmentType.devsanata;
    }
  }
}
