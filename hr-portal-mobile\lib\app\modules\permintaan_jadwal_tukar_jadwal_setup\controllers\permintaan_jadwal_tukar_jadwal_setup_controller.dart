// ignore_for_file: unused_local_variable

import 'dart:convert';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_dropdown_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_radio_component.dart';
import 'package:hr_portal/app/mahas/models/api_result_model.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/mahas/services/http_api.dart';
import 'package:hr_portal/app/models/tukar_jadwal_model.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../models/pegawai_approval.dart';

class PermintaanJadwalTukarJadwalSetupController extends GetxController {
  late SetupPageController formCon;
  late InputDatetimeController dariTanggalCon;
  late InputDatetimeController keTanggalCon;
  late InputRadioController shiftCon;
  final viewShiftCon = InputTextController();
  final tukarCon = InputDropdownController();
  final viewPenggantiCon = InputTextController();
  final alasanCon = InputTextController(type: InputTextType.paragraf);
  RxInt idRequest = 0.obs;
  late String buttonVisibility;
  RxBool keTanggalVisible = true.obs;
  RxBool listPengganti = true.obs;
  RxBool shiftVisible = true.obs;
  RxBool alasanVisible = false.obs;

  final alasanTolakCon = InputTextController();
  final String nama = MahasConfig.profile!.nama!;
  late String approve;
  late bool allowED;
  Rxn<bool> accKadiv = Rxn<bool>();
  Rxn<bool> accPegawai = Rxn<bool>();
  RxBool isVisible = false.obs;
  late RxString pegawaiReq = ''.obs;
  late RxString pegawaiKadiv = ''.obs;
  late RxString pegawaiPegawai = ''.obs;
  RxString alasanTolak = ''.obs;
  RxInt idPegawaiRequest = 0.obs;
  RxInt idPegawaiPengganti = 0.obs;
  final isDinamisApprove = MahasConfig.profileMenu.approvalDinamis;
  RxBool isStop = false.obs;
  RxList<PegawaiApprovalModel> modelApproval = <PegawaiApprovalModel>[].obs;
  int? id;

  @override
  void onInit() {
    cekApproval();
    dariTanggalCon = InputDatetimeController(onChanged: () {
      dariTanggalCon.isValid;
      keTanggalVisible.value = true;
      getDataRequest();
    });
    keTanggalCon = InputDatetimeController(onChanged: () {
      keTanggalCon.isValid;
      shiftVisible.value = true;
      if (shiftCon.isValid == true) {
        getDataTukar();
      }
    });
    shiftCon = InputRadioController(onChanged: (x) {
      shiftCon.isValid;
      getDataTukar();
      alasanVisible.value = true;
    });
    tukarCon.onChanged = (x) {};
    formCon = SetupPageController(
      urlApiGet: (id) => '/api/PermintaanJadwal/TukarJadwal/$id',
      urlApiPost: () => '/api/PermintaanJadwal/TukarJadwal',
      urlApiPut: (id) => '/api/PermintaanJadwal/TukarJadwal/$id',
      urlApiDelete: (id) => '/api/PermintaanJadwal/TukarJadwal/$id',
      allowDelete: allowED,
      allowEdit: allowED,
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "id_JadwalTanggal_Request": idRequest.value,
        "id_JadwalTanggal_Pengganti": tukarCon.value,
        "alasan": alasanCon.value,
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      onBeforeSubmit: () {
        if (!dariTanggalCon.isValid) return false;
        if (!keTanggalCon.isValid) return false;
        if (!shiftCon.isValid) return false;
        if (!tukarCon.isValid) return false;
        if (!alasanCon.isValid) return false;
        return true;
      },
      apiToView: (json) {
        TukarJadwalModel model = TukarJadwalModel.fromJson(json);
        id = model.id;
        dariTanggalCon.value = model.jadwaltanggalrequest;
        keTanggalCon.value = model.jadwaltanggalpengganti;
        viewShiftCon.value = model.shiftrequest;
        viewPenggantiCon.value =
            '${model.shiftpengganti} - ${model.pegawaipengganti}';
        alasanCon.value = model.alasan;
        pegawaiReq.value = model.pegawairequest!;
        accPegawai.value = model.approvepengganti;
        accKadiv.value = model.approvekadiv;
        pegawaiKadiv.value = model.pegawaikadiv!;
        pegawaiPegawai.value = model.pegawaipengganti!;
        idPegawaiRequest.value = model.idPegawaiRequest!;
        idPegawaiPengganti.value = model.idPegawaiPengganti!;

        alasanTolak.value = model.alasanTolak ?? "";

        // approval
        if (isDinamisApprove == false && allowED == false) {
          if ((model.approvepengganti == null &&
                  model.idPegawaiPengganti == MahasConfig.profile!.id) ||
              (model.approvepengganti == true &&
                  model.approvekadiv == null &&
                  model.idPegawaiKadiv == MahasConfig.profile!.id)) {
            isVisible.value = true;
          }
        }

        if (isDinamisApprove == true && allowED == false) {
          if (model.approvepengganti == null &&
              model.idPegawaiPengganti == MahasConfig.profile!.id) {
            isVisible.value = true;
          }
          getDataApproval();
        }
      },
      onInit: () async {
        await Future.delayed(const Duration(seconds: 1));
      },
    );
    cekVisibility();
    super.onInit();
  }

  cekVisibility() {
    buttonVisibility = Get.parameters['buttonVisibility'].toString();
    if (buttonVisibility == "false") {
      keTanggalVisible.value = false;
      shiftVisible.value = false;
      listPengganti.value = false;
    } else if (formCon.editable == true || formCon.editable == false) {
      keTanggalVisible.value = false;
      shiftVisible.value = false;
      listPengganti.value = false;
    }
  }

  getDataRequest() async {
    if (!dariTanggalCon.isValid) return false;
    if (EasyLoading.isShow) return;
    EasyLoading.show();
    final date = MahasFormat.dateToString(dariTanggalCon.value);
    final url =
        "/api/PermintaanJadwal/TukarJadwal/JadwalTanggal/Request?Id_Divisi=${MahasConfig.selectedDivisi}&Tanggal=$date";
    final r = await HttpApi.get(url);
    if (r.success) {
      TukarjadwalrequestModel model = TukarjadwalrequestModel.fromJson(r.body);
      if (r.body != null) {
        idRequest.value = model.id!;
        keTanggalVisible.value = true;
        if (shiftCon.items.isEmpty) {
          model.shift != null &&
                  model.shiftke2 == true &&
                  model.shiftke3 == true
              ? shiftCon.items = [
                  RadioButtonItem.autoId(model.shift!, model.idShift!),
                  RadioButtonItem.autoId("Shift Ke-2", 1),
                  RadioButtonItem.autoId("Shift Ke-3", 2)
                ]
              : model.shift != null &&
                      model.shiftke2 == true &&
                      model.shiftke3 == false
                  ? shiftCon.items = [
                      RadioButtonItem.autoId(model.shift!, model.idShift!),
                      RadioButtonItem.autoId("Shift Ke-2", 1)
                    ]
                  : shiftCon.items = [
                      RadioButtonItem.autoId(model.shift!, model.idShift!)
                    ];
        } else {
          Helper.dialogWarning("Anda belum memilik Shift!");
        }
      } else if (r.body == null) {
        Helper.dialogWarning("Anda Belum Memiliki Jadwal!");
      } else {
        Helper.dialogWarning(r.message);
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning("Gagal memuat data, silahkan cek koneksi internet");
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }

  getDataTukar() async {
    if (!keTanggalCon.isValid) return false;
    if (!dariTanggalCon.isValid) return false;
    if (EasyLoading.isShow) return;
    EasyLoading.show();
    final date = MahasFormat.dateToString(keTanggalCon.value);
    final url =
        "/api/PermintaanJadwal/TukarJadwal/JadwalTanggal/Tukar?Id_Divisi=${MahasConfig.selectedDivisi}&Tanggal=$date";
    final r = await HttpApi.get(url);
    if (r.success) {
      List<TukarJadwalTukarModel> model = tukarJadwalTukarModelFromJson(r.body);
      if (r.body != null) {
        if (tukarCon.items.isEmpty) {
          tukarCon.items = model
              .map<DropdownItem>(
                  (e) => DropdownItem.init("${e.shift} - ${e.pegawai}", e.id))
              .toList();
        }
        listPengganti.value = true;
      } else if (r.body == null) {
        Helper.dialogWarning("Anda Belum Memiliki Jadwal!");
      } else {
        Helper.dialogWarning(r.message);
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning("Gagal memuat data, silahkan cek koneksi internet");
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }

  void cekApproval() {
    approve = Get.parameters['approval'].toString();
    if (approve == "approval") {
      allowED = false;
    } else {
      allowED = true;
    }
  }

  Future<void> approvalOnPress(bool approve) async {
    var id = Get.parameters['id'].toString();
    String urlKadiv = '/api/PermintaanJadwal/TukarJadwal/Approve/Pengganti/';
    String urlManager = '/api/PermintaanJadwal/TukarJadwal/Approve/Kadiv/';
    String urlApprove = '/api/PermintaanJadwal/TukarJadwal/Approve/';
    var action = await Helper.approvalAction(
      id,
      approve,
      alasanTolakCon.value,
      accPegawai.value,
      urlKadiv,
      urlManager,
      isDinamis: isDinamisApprove,
      urlApprove: urlApprove,
      isWithPengganti: true,
    );
    if (action.success) {
      final url = '/api/PermintaanJadwal/TukarJadwal/$id';
      ApiResultModel r;
      r = await HttpApi.get(url);
      if (r.success) {
        TukarJadwalModel model = TukarJadwalModel.fromJson(r.body);
        accKadiv.value = model.approvekadiv;
        accPegawai.value = model.approvepengganti;
        alasanTolak.value = model.alasanTolak ?? '';
        if (isDinamisApprove) {
          getDataApproval();
        }
        isVisible.value = false;
        update();
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan approve, silahkan cek koneksi internet");
    } else if (!action.success) {
      Helper.dialogWarning(
        "Gagal melakukan approval",
        resMassage: action.message,
        noInternetMassage:
            "Gagal melakukan approval, silahkan cek koneksi internet",
      );
    }
  }

  Future<void> getDataApproval() async {
    if (MahasConfig.appName == "SI MADE RS Bhayangkara Denpasar") {
      return;
    }
    EasyLoading.show();
    final url = '/api/PermintaanJadwal/TukarJadwal/Approval/$id';
    final r = await HttpApi.get(url);
    modelApproval.clear();
    if (r.success) {
      if (r.body != null) {
        var data = json.decode(r.body);
        for (var e in data['datas']) {
          modelApproval.add(PegawaiApprovalModel.fromDynamic(e));
        }
        bool canApprove = modelApproval.any((item) =>
            item.idPegawaiapprove == MahasConfig.profile?.id &&
            item.approve == null);
        if (canApprove) {
          isVisible.value = true;
        }
        bool notApprove = modelApproval.any((item) => item.approve == false);
        if (notApprove) {
          isVisible.value = false;
        }
        isStop.value = modelApproval.any((item) => item.approve == false);
        if (accPegawai.value == false) {
          isStop.value = true;
        }
      } else {
        Helper.dialogWarning(r.message);
      }
    } else if (MahasConfig.hasInternet == false) {
      Helper.dialogWarning(
          "Gagal melakukan aksi, silahkan cek koneksi internet");
    } else if (r.statusCode == 404) {
      Helper.dialogWarning("Tidak Ada Data Pegawai Approval");
    } else {
      Helper.dialogWarning(r.message);
    }
    EasyLoading.dismiss();
  }
}
