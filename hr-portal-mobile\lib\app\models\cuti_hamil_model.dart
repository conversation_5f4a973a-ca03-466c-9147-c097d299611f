import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class CutihamilModel {
  int? id;
  int? idPegawaiRequest;
  int? idDivisi;
  DateTime? tanggalinput;
  DateTime? tanggalhpl;
  bool? approvekadiv;
  bool? approvemanager;
  int? anakke;
  int? berapahari;
  DateTime? daritanggal;
  DateTime? sampaitanggal;
  String? alasantolak;
  int? idPegawaiKadiv;
  int? idPegawaiManager;
  int? idUnitbisnis;
  String? divisi;
  String? pegawairequest;
  String? pegawaikadiv;
  String? pegawaimanager;
  String? pathfoto;

  CutihamilModel();

  static CutihamilModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static CutihamilModel fromDynamic(dynamic dynamicData) {
    final model = CutihamilModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idPegawaiRequest =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Request']);
    model.idDivisi = MahasFormat.dynamicToInt(dynamicData['id_Divisi']);
    model.tanggalinput =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalInput']);
    model.tanggalhpl = MahasFormat.dynamicToDateTime(dynamicData['tanggalHpl']);
    model.approvekadiv = MahasFormat.dynamicToBool(dynamicData['approveKadiv']);
    model.approvemanager =
        MahasFormat.dynamicToBool(dynamicData['approveManager']);
    model.anakke = MahasFormat.dynamicToInt(dynamicData['anakKe']);
    model.berapahari = MahasFormat.dynamicToInt(dynamicData['berapaHari']);
    model.daritanggal =
        MahasFormat.dynamicToDateTime(dynamicData['dariTanggal']);
    model.sampaitanggal =
        MahasFormat.dynamicToDateTime(dynamicData['sampaiTanggal']);
    model.alasantolak = dynamicData['alasanTolak'];
    model.idPegawaiKadiv =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Kadiv']);
    model.idPegawaiManager =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Manager']);
    model.idUnitbisnis = MahasFormat.dynamicToInt(dynamicData['id_UnitBisnis']);
    model.divisi = dynamicData['divisi'];
    model.pegawairequest = dynamicData['pegawaiRequest'];
    model.pegawaikadiv = dynamicData['pegawaiKadiv'];
    model.pegawaimanager = dynamicData['pegawaiManager'];
    model.pathfoto = dynamicData['pathFoto'];

    return model;
  }
}
