import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_dropdown_component.dart';
import 'package:hr_portal/app/mahas/components/inputs/input_file_component.dart';

import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../controllers/profile_riwayat_pendidikan_setup_controller.dart';

class ProfileRiwayatPendidikanSetupView
    extends GetView<ProfileRiwayatPendidikanSetupController> {
  const ProfileRiwayatPendidikanSetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      controller: controller.formCon,
      title: "Informasi",
      children: () => [
        InputDropdownComponent(
          label: "Pendidikan",
          controller: controller.pendidikanCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDropdownComponent(
          label: "Klasifikasi Pendidikan",
          controller: controller.klasifikasiPendidikanCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Program Studi",
          controller: controller.programStudiCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Dari Tahun",
          controller: controller.dariTahunCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Sampai Tahun",
          controller: controller.sampaiTahunCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Keterangan",
          controller: controller.keteranganCon,
          required: false,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Nomor Ijazah",
          controller: controller.nomorIjazahCon,
          required: false,
          editable: controller.formCon.editable,
        ),
        InputFileComponent(
          controller: controller.fileCon,
          label: "Upload Dokumen",
          editable: controller.formCon.editable,
          required: false,
        ),
      ],
    );
  }
}
