import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/constant/environment_constant.dart';
import 'package:hr_portal/app/mahas/components/mahas_themes.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:hr_portal/app/mahas/mahas_service.dart';
import 'app/routes/app_pages.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Environment
  MahasConfig.currentEnv = await EnvironmentConstant.currentEnv();
  EnvironmentConstant.environment();
  await MahasService.init();

  runApp(
    GetMaterialApp(
      debugShowCheckedModeBanner: false,
      title: "HR Portal",
      initialRoute: AppPages.INITIAL,
      getPages: AppPages.routes,
      builder: EasyLoading.init(),
      theme: MahasThemes.light,
    ),
  );
}
