import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class PegawaiApprovalModel {
  int? idPegawaiapprove;
  String? pegawaiapprovenama;
  int? tingkatan;
  bool? approve;

  PegawaiApprovalModel();

  static PegawaiApprovalModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static PegawaiApprovalModel fromDynamic(dynamic dynamicData) {
    final model = PegawaiApprovalModel();

    model.idPegawaiapprove =
        MahasFormat.dynamicToInt(dynamicData['id_PegawaiApprove']);
    model.pegawaiapprovenama = dynamicData['pegawaiApproveNama'];
    model.tingkatan = MahasFormat.dynamicToInt(dynamicData['tingkatan']);
    model.approve = MahasFormat.dynamicToBool(dynamicData['approve']);

    return model;
  }
}
