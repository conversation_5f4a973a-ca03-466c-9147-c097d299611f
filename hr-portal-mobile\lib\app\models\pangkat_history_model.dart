import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class PangkathistoryModel {
  int? id;
  int? idPangkat;
  String? idPangkatText;
  int? idPegawai;
  String? nomorkep;
  DateTime? terhitungmulaitanggal;
  bool? aktif;

  PangkathistoryModel();

  static PangkathistoryModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static PangkathistoryModel fromDynamic(dynamic dynamicData) {
    final model = PangkathistoryModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idPangkat = MahasFormat.dynamicToInt(dynamicData['id_Pangkat']);
    model.idPangkatText = dynamicData['id_Pangkat_Text'];
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.nomorkep = dynamicData['nomorKEP'];
    model.terhitungmulaitanggal =
        MahasFormat.dynamicToDateTime(dynamicData['terhitungMulaiTanggal']);
    model.aktif = MahasFormat.dynamicToBool(dynamicData['aktif']);

    return model;
  }
}
