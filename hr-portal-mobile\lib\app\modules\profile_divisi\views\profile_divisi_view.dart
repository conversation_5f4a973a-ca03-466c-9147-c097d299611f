import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../mahas/mahas_colors.dart';
import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/others/shimmer_component.dart';
import '../../../mahas/mahas_config.dart';
import '../controllers/profile_divisi_controller.dart';

class ProfileDivisiView extends GetView<ProfileDivisiController> {
  const ProfileDivisiView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Divisi'),
        centerTitle: true,
      ),
      body: RefreshIndicator(
        backgroundColor: Colors.white,
        onRefresh: controller.onRefresh,
        child: ListView(
          children: [
            Container(
              padding: const EdgeInsets.only(top: 10, left: 10, right: 10),
              child: Obx(
                () => Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Visibility(
                      visible: controller.isLoading.value,
                      child: const ShimmerComponent(),
                    ),
                    Visibility(
                      visible: !controller.isLoading.value,
                      child: Column(
                        children: controller.models
                            .map(
                              (e) => Container(
                                padding: const EdgeInsets.all(10),
                                margin: const EdgeInsets.only(bottom: 10),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color:
                                        MahasColors.grey.withValues(alpha: .5),
                                  ),
                                  borderRadius: BorderRadius.circular(
                                      MahasThemes.borderRadius),
                                ),
                                child: Column(
                                  crossAxisAlignment:
                                      CrossAxisAlignment.stretch,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Text("Divisi", style: MahasThemes.muted),
                                    Text(e.nama!),
                                    const Padding(padding: EdgeInsets.all(5)),
                                    Text(MahasConfig.kadivmanager.kadiv!,
                                        style: MahasThemes.muted),
                                    Text(e.kadiv!),
                                    const Padding(padding: EdgeInsets.all(5)),
                                    Text(MahasConfig.kadivmanager.manager!,
                                        style: MahasThemes.muted),
                                    Text(e.manager!),
                                    const Padding(padding: EdgeInsets.all(10)),
                                    Text("Teams (${e.pegawai!.length})",
                                        style: MahasThemes.muted),
                                    const Padding(padding: EdgeInsets.all(5)),
                                    ...e.pegawai!.map(
                                      (p) => Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.stretch,
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          Text(p.nama ?? "-"),
                                          const Padding(
                                              padding: EdgeInsets.all(5)),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            )
                            .toList(),
                      ),
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
