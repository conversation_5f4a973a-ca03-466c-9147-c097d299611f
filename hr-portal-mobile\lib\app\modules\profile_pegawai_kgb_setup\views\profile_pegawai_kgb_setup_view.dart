import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../controllers/profile_pegawai_kgb_setup_controller.dart';

class ProfilePegawaiKgbSetupView
    extends GetView<ProfilePegawaiKgbSetupController> {
  const ProfilePegawaiKgbSetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      controller: controller.formCon,
      title: 'Informasi',
      children: () => [
        InputTextComponent(
          label: "Nomor KEP",
          controller: controller.nomorKepCon,
          editable: false,
        ),
        InputDatetimeComponent(
          label: 'Ter<PERSON><PERSON> Mulai',
          controller: controller.terhitungMulaiCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDatetimeComponent(
          label: 'KGB Berikutnya',
          controller: controller.kgbBerikutnyaCon,
          required: true,
          editable: controller.formCon.editable,
        ),
      ],
    );
  }
}
