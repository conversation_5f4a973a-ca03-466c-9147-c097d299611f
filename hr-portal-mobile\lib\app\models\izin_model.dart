import 'dart:convert';

import 'package:hr_portal/app/mahas/services/mahas_format.dart';

class IzinModel {
  int? id;
  int? idDivisi;
  DateTime? tanggalinput;
  bool? approvekadiv;
  bool? approvemanager;
  DateTime? daritanggal;
  int? berapahari;
  String? alasan;
  String? alasantolak;
  int? idPegawaiRequest;
  int? idPegawaiKadiv;
  int? idPegawaiManager;
  String? divisi;
  String? pegawairequest;
  String? pegawaikadiv;
  String? pegawaimanager;
  int? idJenisIzin;
  String? berkasPendukung;
  String? namaJenisIzin;

  IzinModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static IzinModel fromDynamic(dynamic dynamicData) {
    final model = IzinModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idDivisi = MahasFormat.dynamicToInt(dynamicData['id_Divisi']);
    model.tanggalinput =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalInput']);
    model.approvekadiv = MahasFormat.dynamicToBool(dynamicData['approveKadiv']);
    model.approvemanager =
        MahasFormat.dynamicToBool(dynamicData['approveManager']);
    model.daritanggal =
        MahasFormat.dynamicToDateTime(dynamicData['dariTanggal']);
    model.berapahari = MahasFormat.dynamicToInt(dynamicData['berapaHari']);
    model.alasan = dynamicData['alasan'];
    model.alasantolak = dynamicData['alasanTolak'];
    model.idPegawaiRequest =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Request']);
    model.idPegawaiKadiv =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Kadiv']);
    model.idPegawaiManager =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Manager']);
    model.divisi = dynamicData['divisi'];
    model.pegawairequest = dynamicData['pegawaiRequest'];
    model.pegawaikadiv = dynamicData['pegawaiKadiv'];
    model.pegawaimanager = dynamicData['pegawaiManager'];
    model.idJenisIzin = MahasFormat.dynamicToInt(dynamicData['id_JenisIzin']);
    model.berkasPendukung = dynamicData['berkasPendukung'];
    model.namaJenisIzin = dynamicData['namaJenisIzin'];
    return model;
  }
}

class LookupizinModel {
  int? id;
  String? nama;

  LookupizinModel();
  LookupizinModel.init(this.id, this.nama);

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static LookupizinModel fromDynamic(dynamic dynamicData) {
    final model = LookupizinModel();

    model.id = dynamicData['id'];
    model.nama = dynamicData['nama'];

    return model;
  }
}
