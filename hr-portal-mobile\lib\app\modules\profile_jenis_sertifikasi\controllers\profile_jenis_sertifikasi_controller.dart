// ignore_for_file: unnecessary_overrides

import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../models/profile_sertifikasi_model.dart';
import '../../../routes/app_pages.dart';

class ProfileJenisSertifikasiController extends GetxController {
  final listCon = ListComponentController<SertifikasiModel>(
    urlApi: (index, filter) => '/api/PegawaiSertifikasi/List?pageIndex=$index',
    fromDynamic: SertifikasiModel.fromDynamic,
    allowSearch: false,
  );

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PROFILE_JENIS_SERTIFIKASI_SETUP,
      parameters: {
        'id': id.toString(),
      },
    )?.then((value) {
      listCon.refresh();
    });
  }

  void popupMenuButtonOnSelected(String v) async {
    if (v == 'Tambah') {
      Get.toNamed(Routes.PROFILE_JENIS_SERTIFIKASI_SETUP)?.then(
        (value) {
          listCon.refresh();
        },
      );
    } else if (v == 'History') {
      Get.toNamed(
        Routes.PROFILE_JENIS_SERTIFIKASI_HISTORY,
        parameters: {
          'showStatus': true.toString(),
        },
      )?.then(
        (value) {
          listCon.refresh();
        },
      );
    }
  }
}
