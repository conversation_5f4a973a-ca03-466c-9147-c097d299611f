import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';
import 'package:intl/intl.dart';

import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/mahas_colors.dart';
import '../../../mahas_complement/mahas_colors.dart';
import '../controllers/informasi_absensi_jadwal_anggota_divisi_controller.dart';

class InformasiAbsensiJadwalAnggotaDivisiView
    extends GetView<InformasiAbsensiJadwalAnggotaDivisiController> {
  const InformasiAbsensiJadwalAnggotaDivisiView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Absensi Jadwal Anggota Divisi'),
        centerTitle: true,
        actions: [
          InkWell(
            onTap: controller.filterOnTap,
            child: SizedB<PERSON>(
              width: 50,
              height: Get.height,
              child: const Center(
                child: Icon(
                  FontAwesomeIcons.filter,
                  size: 20,
                  color: MahasColors.light,
                ),
              ),
            ),
          )
        ],
      ),
      body: Column(
        children: [
          Obx(
            () => Container(
              padding: const EdgeInsets.all(5),
              decoration: const BoxDecoration(color: MahasColor.greyInputan),
              child: Row(
                children: List.generate(
                  controller.menus.length,
                  (index) {
                    var item = controller.menus[index];
                    return Expanded(
                      child: InkWell(
                        onTap: () {
                          controller.selectedIndex.value = index;
                        },
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: index == controller.selectedIndex.value
                                ? MahasColors.primary
                                : MahasColor.greyInputan,
                            borderRadius: const BorderRadius.all(
                              Radius.circular(5),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                item.icon,
                                size: 20,
                                color: index == controller.selectedIndex.value
                                    ? MahasColors.light
                                    : MahasColors.primary,
                              ),
                              const SizedBox(
                                width: 10,
                              ),
                              Text(
                                item.title,
                                style: TextStyle(
                                  color: index == controller.selectedIndex.value
                                      ? MahasColors.light
                                      : MahasColors.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
          Expanded(
            child: Obx(
              () => Padding(
                padding: const EdgeInsets.symmetric(vertical: 10),
                child: controller.isLoading.value
                    ? const Center(child: CircularProgressIndicator())
                    : ListView.builder(
                        itemCount: controller.selectedIndex.value == 0
                            ? controller.masukList.length
                            : controller.keluarList.length,
                        itemBuilder: (context, index) {
                          var e = controller.selectedIndex.value == 0
                              ? controller.masukList[index]
                              : controller.keluarList[index];
                          return Padding(
                            padding: const EdgeInsets.only(
                              left: 5,
                              right: 5,
                              bottom: 10,
                            ),
                            child: LimitedBox(
                              maxHeight: 70.0,
                              child: Row(
                                children: [
                                  Container(
                                    width: 20,
                                    decoration: BoxDecoration(
                                      color: controller
                                          .colorKeterangan(e.keterangan ?? ""),
                                      border: Border.all(
                                        color: MahasColors.grey.withValues(),
                                      ),
                                      borderRadius: BorderRadius.only(
                                        bottomRight: Radius.zero,
                                        topRight: Radius.zero,
                                        topLeft: Radius.circular(
                                            MahasThemes.borderRadius),
                                        bottomLeft: Radius.circular(
                                            MahasThemes.borderRadius),
                                      ),
                                    ),
                                  ),
                                  Container(
                                    width:
                                        MediaQuery.of(context).size.width - 30,
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                        color: MahasColors.grey.withValues(),
                                      ),
                                      borderRadius: BorderRadius.only(
                                        topRight: Radius.circular(
                                            MahasThemes.borderRadius),
                                        bottomLeft: Radius.zero,
                                        bottomRight: Radius.circular(
                                            MahasThemes.borderRadius),
                                        topLeft: Radius.zero,
                                      ),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Text(
                                                  e.pegawai!,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                                Text(
                                                  "${e.namaShift ?? ""} ${MahasFormat.displayTime(e.jamMulai)} - ${MahasFormat.displayTime(e.jamSelesai)}",
                                                  style: MahasColor.muted,
                                                ),
                                                Text(
                                                  e.lokasiabsensi ?? "",
                                                  style: MahasColor.muted,
                                                ),
                                              ],
                                            ),
                                          ),
                                          Column(
                                            mainAxisSize: MainAxisSize.min,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.end,
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Text(
                                                e.keterangan ?? "",
                                                style: MahasColor.muted,
                                              ),
                                              Text(
                                                e.tanggaljam == null
                                                    ? ""
                                                    : DateFormat("dd MMMM")
                                                        .format(e.tanggaljam!),
                                                style: MahasColor.muted,
                                              ),
                                              Text(
                                                e.tanggaljam == null
                                                    ? ""
                                                    : DateFormat("HH:mm")
                                                        .format(e.tanggaljam!),
                                                style: MahasColor.muted,
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
