import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:hr_portal/app/constant/environment_constant.dart';
import 'package:hr_portal/app/mahas/components/mahas_themes.dart';
import 'package:hr_portal/app/mahas/components/others/list_component.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:hr_portal/app/mahas/mahas_widget.dart';
import 'package:hr_portal/app/mahas_complement/mahas_colors.dart';
import 'package:hr_portal/app/models/absensi_model.dart';
import 'package:intl/intl.dart';

import '../../../mahas/mahas_colors.dart';
import '../controllers/data_absen_controller.dart';

class DataAbsenView extends GetView<DataAbsenController> {
  const DataAbsenView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Data Absensi'),
        centerTitle: true,
        actions: [
          InkWell(
            onTap: controller.filterOnTap,
            child: SizedBox(
              width: 50,
              height: Get.height,
              child: const Center(
                child: Icon(
                  FontAwesomeIcons.filter,
                  size: 20,
                  color: MahasColors.light,
                ),
              ),
            ),
          )
        ],
      ),
      body: MahasConfig.currentEnv == MahasEnvironmentType.devsanata ||
              MahasConfig.currentEnv == MahasEnvironmentType.sanata
          ? Column(
              children: [
                Obx(
                  () => Container(
                    padding: const EdgeInsets.all(5),
                    decoration:
                        const BoxDecoration(color: MahasColor.greyInputan),
                    child: Row(
                      children: List.generate(
                        controller.menus.length,
                        (index) {
                          var item = controller.menus[index];
                          return Expanded(
                            child: InkWell(
                              onTap: () {
                                controller.selectedIndex.value = index;
                                controller.listCon.refresh();
                              },
                              child: Container(
                                padding: const EdgeInsets.all(10),
                                decoration: BoxDecoration(
                                  color: index == controller.selectedIndex.value
                                      ? MahasColors.primary
                                      : MahasColor.greyInputan,
                                  borderRadius: const BorderRadius.all(
                                    Radius.circular(5),
                                  ),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      item.icon,
                                      size: 20,
                                      color: index ==
                                              controller.selectedIndex.value
                                          ? MahasColors.light
                                          : MahasColors.primary,
                                    ),
                                    const SizedBox(
                                      width: 10,
                                    ),
                                    Text(
                                      item.title,
                                      style: TextStyle(
                                        color: index ==
                                                controller.selectedIndex.value
                                            ? MahasColors.light
                                            : MahasColors.primary,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Expanded(
                  child: ListComponent(
                    controller: controller.listCon,
                    separatorBuilder: (context, index, length) =>
                        const SizedBox(),
                    itemBuilder: (e) {
                      return e.status?.toLowerCase() == "masuk" &&
                              controller.selectedIndex.value == 0
                          ? MahasWidget.absensiSayaItemWidget(e)
                          : e.status?.toLowerCase() == "keluar" &&
                                  controller.selectedIndex.value == 1
                              ? MahasWidget.absensiSayaItemWidget(e)
                              : const SizedBox();
                    },
                  ),
                ),
              ],
            )
          : Padding(
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: ListComponent(
                controller: controller.listCon,
                itemBuilder: (AbsensiModel e) => Padding(
                  padding:
                      const EdgeInsets.only(left: 10, right: 10, bottom: 10),
                  child: LimitedBox(
                    maxHeight: 60.0,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Container(
                          width: 50,
                          decoration: BoxDecoration(
                            color: e.status == "Masuk"
                                ? MahasColor.colorGreen
                                : MahasColor.colorRed,
                            border: Border.all(
                              color: MahasColors.grey.withValues(),
                            ),
                            borderRadius: BorderRadius.only(
                              bottomRight: Radius.zero,
                              topRight: Radius.zero,
                              topLeft:
                                  Radius.circular(MahasThemes.borderRadius),
                              bottomLeft:
                                  Radius.circular(MahasThemes.borderRadius),
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Center(
                                child: e.status == "Masuk"
                                    ? const Icon(
                                        Icons.login,
                                        color: Colors.white,
                                      )
                                    : const Icon(
                                        Icons.logout,
                                        color: Colors.white,
                                      ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          width: MediaQuery.of(context).size.width - 70,
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: MahasColors.grey.withValues(),
                            ),
                            borderRadius: BorderRadius.only(
                              topRight:
                                  Radius.circular(MahasThemes.borderRadius),
                              bottomLeft: Radius.zero,
                              bottomRight:
                                  Radius.circular(MahasThemes.borderRadius),
                              topLeft: Radius.zero,
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(e.lokasiabsensi!),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      DateFormat("dd MMMM yyyy")
                                          .format(e.tanggaljam!),
                                      style: MahasColor.muted,
                                    ),
                                    Text(
                                      DateFormat("HH:mm").format(e.tanggaljam!),
                                      style: MahasColor.muted,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
    );
  }
}
