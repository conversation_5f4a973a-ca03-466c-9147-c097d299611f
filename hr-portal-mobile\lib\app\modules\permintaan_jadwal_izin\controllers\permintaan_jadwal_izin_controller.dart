import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/components/others/list_component.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:hr_portal/app/models/izin_model.dart';
import 'package:hr_portal/app/routes/app_pages.dart';

import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../models/divisi_model.dart';

class PermintaanJadwalIzinController extends GetxController {
  final listCon = ListComponentController<IzinModel>(
    urlApi: (index, filter) =>
        '/api/PermintaanJadwal/Izin/List?pageIndex=$index',
    fromDynamic: IzinModel.fromDynamic,
    allowSearch: false,
  );

  List<DivisiModel>? listDivisi;
  RxBool isKadiv = false.obs;
  RxBool isManagement = false.obs;
  bool isDinamis = MahasConfig.profileMenu.approvalDinamis;

  void addOnPress() {
    Get.toNamed(Routes.PERMINTAAN_JADWAL_IZIN_SETUP, parameters: {
      'isKadiv': isKadiv.value.toString(),
      'isManagement': isManagement.value.toString()
    })?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }

  @override
  void onInit() {
    getDivisi();
    super.onInit();
  }

  getDivisi() async {
    var r = await HttpApi.get('/api/Profile/Divisi');
    if (r.success) {
      listDivisi = divisiModelFromJson(r.body);
      for (var i = 0; i < listDivisi!.length; i++) {
        if (MahasConfig.selectedDivisi == listDivisi![i].id) {
          if (MahasConfig.profile!.nama == listDivisi![i].kadiv) {
            isKadiv.value = true;
          }
          if (MahasConfig.profile!.nama == listDivisi![i].kadiv ||
              MahasConfig.profile!.nama == listDivisi![i].manager) {
            isManagement.value = true;
          }
        }
      }
    } else {
      if (MahasConfig.hasInternet == false) {
        Helper.dialogWarning(
            "Gagal memuat data, silahkan cek koneksi internet");
      } else {
        Helper.dialogWarning("Gagal memuat data");
      }
    }
  }

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PERMINTAAN_JADWAL_IZIN_SETUP,
      parameters: {
        'id': id.toString(),
        'isKadiv': isKadiv.value.toString(),
        'isManagement': isManagement.value.toString()
      },
    )?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }
}
