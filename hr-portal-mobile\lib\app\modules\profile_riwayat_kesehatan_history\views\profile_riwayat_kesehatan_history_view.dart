import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:get/get.dart';

import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/others/list_component.dart';
import '../../../mahas/mahas_colors.dart';
import '../../../models/profile_riwayat_kesehatan_model.dart';
import '../controllers/profile_riwayat_kesehatan_history_controller.dart';

class ProfileRiwayatKesehatanHistoryView
    extends GetView<ProfileRiwayatKesehatanHistoryController> {
  const ProfileRiwayatKesehatanHistoryView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('History Riwayat Kesehatan'),
        centerTitle: true,
      ),
      body: ListComponent(
        separatorBuilder: (context, index, length) => const Sized<PERSON><PERSON>(),
        controller: controller.listCon,
        itemBuilder: (RiwayatkesehatanModel e) {
          return ListTile(
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text("Riwayat Kesehatan", style: MahasThemes.title),
                    Text(
                        e.approved == null
                            ? "Menunggu"
                            : e.approved == false
                                ? "Ditolak"
                                : "Diterima",
                        style: MahasThemes.muted)
                  ],
                ),
                Text("Nomor : ${e.nomor ?? "-"}"),
              ],
            ),
            subtitle: e.status == "ADD"
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const Icon(
                        FontAwesomeIcons.plus,
                        size: 15,
                        color: MahasColors.green,
                      ),
                      const SizedBox(
                        width: 5,
                      ),
                      Text(
                        "Tambah",
                        style: MahasThemes.muted
                            .copyWith(color: MahasColors.green),
                      ),
                    ],
                  )
                : e.status == "UPDATE"
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Icon(
                            FontAwesomeIcons.penToSquare,
                            size: 15,
                            color: MahasColors.warning,
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          Text(
                            "Ubah",
                            style: MahasThemes.muted
                                .copyWith(color: MahasColors.warning),
                          ),
                        ],
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Icon(
                            FontAwesomeIcons.trash,
                            size: 15,
                            color: MahasColors.danger,
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          Text(
                            "Hapus",
                            style: MahasThemes.muted
                                .copyWith(color: MahasColors.danger),
                          ),
                        ],
                      ),
            onTap: () => controller.itemOnTab(e.id!),
          );
        },
      ),
    );
  }
}
