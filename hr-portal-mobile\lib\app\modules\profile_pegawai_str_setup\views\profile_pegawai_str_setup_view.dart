import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/inputs/input_dropdown_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../controllers/profile_pegawai_str_setup_controller.dart';

class ProfilePegawaiStrSetupView
    extends GetView<ProfilePegawaiStrSetupController> {
  const ProfilePegawaiStrSetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      controller: controller.formCon,
      title: "Data STR dan SIP",
      children: () => [
        InputDropdownComponent(
          label: "Jenis <PERSON>esi",
          controller: controller.profesiCon,
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Nomor STR",
          controller: controller.noStrCon,
          required: true,
          editable: controller.formCon.isState != SetupPageState.update
              ? controller.formCon.editable
              : false,
        ),
        InputDatetimeComponent(
          controller: controller.tglStrCon,
          label: 'Tanggal STR',
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDatetimeComponent(
          controller: controller.tglBerakhirStrCon,
          label: 'Tanggal Berakhir STR',
          required: true,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Nomor SIP",
          controller: controller.noSipCon,
          required: true,
          editable: controller.formCon.isState != SetupPageState.update
              ? controller.formCon.editable
              : false,
        ),
        InputDatetimeComponent(
          controller: controller.tglSipCon,
          label: 'Tanggal SIP',
          required: true,
          editable: controller.formCon.editable,
        ),
        InputDatetimeComponent(
          controller: controller.tglBerakhirSipCon,
          label: 'Tanggal Berakhir SIP',
          required: true,
          editable: controller.formCon.editable,
        ),
      ],
    );
  }
}
