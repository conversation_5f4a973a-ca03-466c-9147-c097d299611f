
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';

import '../../../mahas/models/menu_item_model.dart';
import '../../../routes/app_pages.dart';

class DataAbsensiListController extends GetxController {
  final List<MenuItemModel> menus = [];

  void cKantor() {
    Get.toNamed(Routes.DATA_ABSEN);
  }

  void cTugas() {
    Get.toNamed(Routes.ABSEN_SURAT_TUGAS);
  }
  
  @override
  void onInit() {
    menus.add(MenuItemModel(
        'Absensi Kantor', FontAwesomeIcons.buildingCircleCheck, cKantor));
    menus.add(MenuItemModel('Absensi Tugas',
        FontAwesomeIcons.buildingCircleArrowRight, cTugas));
    super.onInit();
  }

}
