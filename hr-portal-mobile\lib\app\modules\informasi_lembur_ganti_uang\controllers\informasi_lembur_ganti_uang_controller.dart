import 'dart:convert';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/services/helper.dart';
import 'package:hr_portal/app/mahas/services/http_api.dart';
import 'package:hr_portal/app/models/informasi_lembur_ganti_uang.dart';

class InformasiLemburGantiUangController extends GetxController {
  var models = RxList<LemburgantiuangModel>();
  var isLoading = false.obs;
  RxBool noData = false.obs;

  Future<void> onRefresh() async {
    if (isLoading.isTrue) return;
    isLoading.value = true;
    var r = await HttpApi.get('/api/Informasi/LemburGantiUang');
    if (r.success) {
      final datas = json.decode(r.body);
      final listData = datas['datas'];

      models.clear();
      for (var e in listData) {
        models.add(LemburgantiuangModel.fromDynamic(e));
      }

      if (models.isEmpty) {
        noData.value = true;
        // print(noData.value);
      }
    } else {
      Helper.fetchErrorMessage(r, httpMethod: HttpMethod.get);
    }
    isLoading.value = false;
  }

  @override
  void onInit() {
    super.onInit();
    onRefresh();
  }
}
