import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';

import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../controllers/informasi_pengumuman_setup_controller.dart';

class InformasiPengumumanSetupView
    extends GetView<InformasiPengumumanSetupController> {
  const InformasiPengumumanSetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      title: 'Pengumuman',
      controller: controller.formCon,
      children: () => [
        InputTextComponent(
          label: "Judul",
          required: true,
          controller: controller.judulCon,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Keterangan",
          required: true,
          controller: controller.keteranganCon,
          editable: controller.formCon.editable,
        ),
        Html(
          data: controller.keteranganCon.value,
          style: {
            "body": Style(
              fontSize: FontSize(16),
              margin: Margins.zero,
              padding: HtmlPaddings.zero,
            ),
            "img": Style(
              width: Width(20),
              height: Height(20),
              display: Display.inlineBlock,
              verticalAlign: VerticalAlign.middle,
            ),
          },
          onLinkTap: (url, attributes, element) {
            // Handle link taps if needed
            // For now, we'll just ignore them
          },
        ),
        InputFileComponent(
          controller: controller.fileCon,
          label: "Berkas",
          editable: controller.formCon.editable,
          required: true,
        ),
      ],
    );
  }
}
