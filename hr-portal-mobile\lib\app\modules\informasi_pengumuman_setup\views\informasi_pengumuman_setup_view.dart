import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../mahas/components/inputs/input_file_component.dart';
import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../controllers/informasi_pengumuman_setup_controller.dart';

class InformasiPengumumanSetupView
    extends GetView<InformasiPengumumanSetupController> {
  const InformasiPengumumanSetupView({super.key});
  @override
  Widget build(BuildContext context) {
    return SetupPageComponent(
      title: 'Pengumuman',
      controller: controller.formCon,
      children: () => [
        InputTextComponent(
          label: "Judul",
          required: true,
          controller: controller.judulCon,
          editable: controller.formCon.editable,
        ),
        InputTextComponent(
          label: "Keterangan",
          required: false,
          controller: controller.keteranganCon,
          editable: controller.formCon.editable,
        ),
        InputFileComponent(
          controller: controller.fileCon,
          label: "Berkas",
          editable: controller.formCon.editable,
          required: true,
        ),
      ],
    );
  }
}
