import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class SeragamModel {
  int? id;
  int? idPegawai;
  int? idSeragam;
  String? namaSeragam;
  String? kodeinventory;
  String? ukuran;
  int? jumlah;
  DateTime? tanggalterima;

  SeragamModel();

  static SeragamModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static SeragamModel fromDynamic(dynamic dynamicData) {
    final model = SeragamModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.idSeragam = MahasFormat.dynamicToInt(dynamicData['id_Seragam']);
    model.namaSeragam = dynamicData['nama_Seragam'];
    model.kodeinventory = dynamicData['kodeInventory'];
    model.ukuran = dynamicData['ukuran'];
    model.jumlah = MahasFormat.dynamicToInt(dynamicData['jumlah']);
    model.tanggalterima =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalTerima']);

    return model;
  }
}

class JenisSeragamModel {
  int? id;
  String? nama;
  bool? aktif;

  JenisSeragamModel();

  static JenisSeragamModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static JenisSeragamModel fromDynamic(dynamic dynamicData) {
    final model = JenisSeragamModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.nama = dynamicData['nama'];
    model.aktif = MahasFormat.dynamicToBool(dynamicData['aktif']);

    return model;
  }
}
