CARA PUBLIS CODEMAGIC.IO

IOS
1. buka pubspec.yaml -> update version -> contoh version: 4.0.13+32
2. buka xcode -> pilih flavor client -> contoh sanata
3. runner -> target runner -> general -> identity -> update version dan build numner -> contoh 4.0.13 dan 32
4. buat branch baru, commit dan push -> contoh temp-production-sanata
5. buka code magic -> start new build sesuai dari workflow dan branch
6. login ke appstoreconnect.apple.com
7. pilih app contoh HR Portal -> + -> masukan nama versi yang baru, contoh 4.0.13 -> create
8. Build -> Add Build -> Pilih build
9. isi What's New in This Version
10. Save -> Add for review -> Submit to App Review