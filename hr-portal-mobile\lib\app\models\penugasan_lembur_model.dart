import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';

class PenugasanLemburModel {
  int? id;
  int? idPegawai;
  String? namaPegawai;
  int? idPegawaiKadiv;
  String? namaPegawaiKadiv;
  DateTime? tanggalLembur;
  TimeOfDay? jamLembur;
  int? lamaHari;
  int? lamaJam;
  int? lamaMenit;
  String? keterangan;
  String? alasanTolak;
  bool? approve;
  String? urlReport;
  String? keteranganLembur;

  PenugasanLemburModel();

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static PenugasanLemburModel fromDynamic(dynamic dynamicData) {
    final model = PenugasanLemburModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.namaPegawai = dynamicData['namaPegawai'];
    model.idPegawaiKadiv =
        MahasFormat.dynamicToInt(dynamicData['id_Pegawai_Kadiv']);
    model.namaPegawaiKadiv = dynamicData['namaPegawaiKadiv'];
    model.tanggalLembur =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalLembur']);
    model.jamLembur = MahasFormat.stringToTime(dynamicData['jamLembur']);
    model.lamaHari = MahasFormat.dynamicToInt(dynamicData['lamaHari']);
    model.lamaJam = MahasFormat.dynamicToInt(dynamicData['lamaJam']);
    model.lamaMenit = MahasFormat.dynamicToInt(dynamicData['lamaMenit']);
    model.keterangan = dynamicData['keterangan'];
    model.keteranganLembur = dynamicData['keteranganLembur'];
    model.approve = MahasFormat.dynamicToBool(dynamicData['approve']);
    model.alasanTolak = dynamicData['alasanTolak'];
    model.urlReport = dynamicData['urlReport'];

    return model;
  }
}

class LookUpPenugasanLemburModel {
  int? id;
  String? nama;

  LookUpPenugasanLemburModel();
  LookUpPenugasanLemburModel.init(this.id, this.nama);

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static LookUpPenugasanLemburModel fromDynamic(dynamic dynamicData) {
    final model = LookUpPenugasanLemburModel();

    model.id = dynamicData['id'];
    model.nama = dynamicData['nama'];

    return model;
  }
}
