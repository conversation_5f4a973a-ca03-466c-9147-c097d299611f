import 'package:get/get.dart';
import 'package:hr_portal/app/models/profile_riwayat_pendidikan_model.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../routes/app_pages.dart';

class ProfileRiwayatPendidikanHistoryController extends GetxController {
  final listCon = ListComponentController<RiwayatpendidikanModel>(
    urlApi: (index, filter) =>
        '/api/PegawaiRiwayatPendidikan/History/List?pageIndex=$index',
    fromDynamic: RiwayatpendidikanModel.fromDynamic,
    allowSearch: false,
  );

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PROFILE_RIWAYAT_PENDIDIKAN_HISTORY_SETUP,
      parameters: {
        'id': id.toString(),
        'showStatus': true.toString(),
      },
    )?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }
}
