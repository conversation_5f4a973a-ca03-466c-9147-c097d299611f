import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class DokumenModel {
  int? id;
  int? idPegawai;
  int? idDokumen;
  String? namadokumen;
  String? nomor;
  int? sisaHariExpired;
  DateTime? tanggalberlaku;
  DateTime? tanggalberakhir;
  String? statuspajak;
  bool? aktif;
  String? pathfile;

  DokumenModel();

  static DokumenModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static DokumenModel fromDynamic(dynamic dynamicData) {
    final model = DokumenModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.idPegawai = MahasFormat.dynamicToInt(dynamicData['id_Pegawai']);
    model.idDokumen = MahasFormat.dynamicToInt(dynamicData['id_Dokumen']);
    model.namadokumen = dynamicData['namaDokumen'];
    model.nomor = dynamicData['nomor'];
    model.sisaHariExpired =
        MahasFormat.dynamicToInt(dynamicData['sisaHariExpired']);
    model.tanggalberlaku =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalBerlaku']);
    model.tanggalberakhir =
        MahasFormat.dynamicToDateTime(dynamicData['tanggalBerakhir']);
    model.pathfile = dynamicData['pathFile'];
    model.statuspajak = dynamicData['statusPajak'];
    model.aktif = MahasFormat.dynamicToBool(dynamicData['aktif']);

    return model;
  }
}

class JenisDokumenModel {
  int? id;
  String? nama;
  bool? aktif;

  JenisDokumenModel();

  static JenisDokumenModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static JenisDokumenModel fromDynamic(dynamic dynamicData) {
    final model = JenisDokumenModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.nama = dynamicData['nama'];
    model.aktif = MahasFormat.dynamicToBool(dynamicData['aktif']);

    return model;
  }
}
