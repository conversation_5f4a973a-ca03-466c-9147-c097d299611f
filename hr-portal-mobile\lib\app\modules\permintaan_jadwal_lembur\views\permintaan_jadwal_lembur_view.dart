import 'package:get/get.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter/material.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:hr_portal/app/mahas_complement/mahas_colors.dart';
import 'package:hr_portal/app/models/lembur_model.dart';
import '../../../mahas/components/mahas_themes.dart';
import '../../../mahas/components/others/list_component.dart';
import '../../../mahas/mahas_colors.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/mahas_format.dart';
import '../../../mahas_complement/approval_status.dart';
import '../controllers/permintaan_jadwal_lembur_controller.dart';

class PermintaanJadwalLemburView
    extends GetView<PermintaanJadwalLemburController> {
  const PermintaanJadwalLemburView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Lembur'),
        centerTitle: true,
        actions: [
          Obx(() {
            return Visibility(
              visible: controller.isKadiv.value,
              child: Padding(
                padding: const EdgeInsets.only(right: 10),
                child: GestureDetector(
                  onTap: controller.addOnPress,
                  child: const Icon(
                    FontAwesomeIcons.plus,
                    size: 24,
                  ),
                ),
              ),
            );
          }),
        ],
      ),
      body: controller.isDinamis == false
          ? listStatis()
          : MahasConfig.profileMenu.customLemburPage
              ? listStatis()
              : listDinamis(),
    );
  }

  ListComponent<LemburModel> listStatis() {
    return ListComponent(
      controller: controller.listCon,
      itemBuilder: (LemburModel e) {
        return InkWell(
          onTap: () => controller.itemOnTab(e.id!, e.approvemanager.toString()),
          child: MahasConfig.profileMenu.customLemburPage
              ? penugasanLembur(e)
              : baseLembur(e),
        );
      },
    );
  }

  Padding penugasanLembur(LemburModel e) {
    return Padding(
      padding: const EdgeInsets.only(left: 12, right: 12, top: 8, bottom: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ApprovalStatusWidget(
                      pegawai: e.namaPegawai.toString(),
                      approveStatus: e.approve,
                    ),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                        "${e.lamahari} Hari, ${e.lamajam} Jam, ${e.lamamenit} Menit",
                        style: MahasThemes.muted),
                    Text(
                      MahasFormat.displayDate(e.tanggallembur),
                      style: MahasThemes.muted,
                    ),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(e.keterangan ?? "-"),
                    Text(
                      MahasFormat.displayTime(e.jamLembur),
                      style: MahasThemes.muted,
                    ),
                  ],
                ),
                const SizedBox(height: 5),
              ],
            ),
          )
        ],
      ),
    );
  }

  Padding baseLembur(LemburModel e) {
    return Padding(
      padding: const EdgeInsets.only(left: 12, right: 12, top: 8, bottom: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(e.pegawai ?? "", style: MahasThemes.title),
                Text("Lembur ${e.jenis}"),
                const SizedBox(height: 5),
                Text("${e.lamajam} Jam"),
                Text(MahasFormat.displayTime(
                    TimeOfDay.fromDateTime(e.tanggaljam!))),
                const SizedBox(height: 5),
                Text(e.pegawaimanager.toString(), style: MahasThemes.title),
                e.approvemanager.toString() == "null"
                    ? const Row(
                        children: [
                          Icon(
                            FontAwesomeIcons.clock,
                            size: 12,
                          ),
                          Text(" Menunggu")
                        ],
                      )
                    : e.approvemanager == false
                        ? const Row(
                            children: [
                              Icon(
                                FontAwesomeIcons.circleXmark,
                                size: 12,
                                color: MahasColors.red,
                              ),
                              Text(
                                " Ditolak",
                                style: TextStyle(color: MahasColors.red),
                              )
                            ],
                          )
                        : e.approvemanager == true
                            ? const Row(
                                children: [
                                  Icon(
                                    FontAwesomeIcons.check,
                                    size: 12,
                                    color: MahasColor.colorGreen,
                                  ),
                                  Text(
                                    " Diterima",
                                    style: TextStyle(
                                      color: MahasColor.colorGreen,
                                    ),
                                  )
                                ],
                              )
                            : const Row(
                                children: [
                                  Icon(
                                    FontAwesomeIcons.clock,
                                    size: 12,
                                  ),
                                  Text(" Menunggu")
                                ],
                              ),
              ],
            ),
          ),
          Text(
            MahasFormat.displayDate(e.tanggaljam),
            style: MahasColor.muted,
          ),
        ],
      ),
    );
  }

  ListComponent<LemburModel> listDinamis() {
    return ListComponent(
      controller: controller.listCon,
      itemBuilder: (LemburModel e) {
        return Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () =>
                    controller.itemOnTab(e.id!, e.approvemanager.toString()),
                child: Padding(
                  padding: const EdgeInsets.only(
                      left: 12, right: 12, top: 12, bottom: 0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text("${e.alasan}", style: MahasThemes.title),
                      const SizedBox(height: 15),
                    ],
                  ),
                ),
              ),
            ),
            InkWell(
              onTap: () async {
                await Helper.statusApproval(
                  "/api/PermintaanJadwal/Lembur/Approval/${e.id}",
                  alasanTolak: e.alasanTolak,
                );
              },
              child: Padding(
                padding: const EdgeInsets.only(
                    left: 12, right: 12, top: 12, bottom: 0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      MahasFormat.displayDate(e.tanggalinput),
                      style: MahasColor.muted,
                    ),
                    Text(
                      "Lihat Status",
                      style: TextStyle(
                        color: MahasColor.primaryColor.shade300,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
