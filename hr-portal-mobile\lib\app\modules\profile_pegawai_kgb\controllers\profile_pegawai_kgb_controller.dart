import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../models/profile_kgb_model.dart';
import '../../../routes/app_pages.dart';

class ProfilePegawaiKgbController extends GetxController {
  final listCon = ListComponentController<KgbModel>(
    urlApi: (index, filter) => '/api/PegawaiKGB/History/List?pageIndex=$index',
    fromDynamic: KgbModel.fromDynamic,
    allowSearch: false,
  );

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PROFILE_PEGAWAI_KGB_SETUP,
      parameters: {
        'id': id.toString(),
      },
    )?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }
}
