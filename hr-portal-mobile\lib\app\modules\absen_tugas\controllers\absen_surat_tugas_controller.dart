import 'package:get/get.dart';
import 'package:hr_portal/app/models/absensi_surat_tugas_model.dart';
import '../../../mahas/components/inputs/input_datetime_component.dart';
import '../../../mahas/components/others/list_component.dart';
import '../../../mahas/services/helper.dart';
import '../../../routes/app_pages.dart';

class AbsenSuratTugasController extends GetxController {
  late ListComponentController<AbsensiSuratTugasModel> listCon;
  RxString dariTanggal =
      "${DateTime.now().year}-${DateTime.now().month}-${DateTime.now().day}"
          .obs;
  RxString sampaiTanggal =
      "${DateTime.now().year}-${DateTime.now().month}-${DateTime.now().day}"
          .obs;
  final InputDatetimeController dariTanggalCon = InputDatetimeController();
  final InputDatetimeController sampaiTanggalCon = InputDatetimeController();

  @override
  void onInit() {
    listCon = ListComponentController<AbsensiSuratTugasModel>(
      urlApi: (index, filter) =>
          '/api/AbsensiTugas/List?dariTanggal=${dariTanggal.value}&sampaiTanggal=${sampaiTanggal.value}&pageIndex=$index',
      fromDynamic: AbsensiSuratTugasModel.fromDynamic,
      allowSearch: false,
    );
    super.onInit();
  }

  Future<void> filterOnTap() async {
    Helper.dialogFilter(
      dariTanggalCon: dariTanggalCon,
      sampaiTanggalCon: sampaiTanggalCon,
      todayFilterOnTap: () => filter(false),
      periodFilterOnTap: () => filter(
        true,
      ),
    );
  }

  void filter(bool isPeriod) async {
    if (isPeriod) {
      dariTanggal.value = dariTanggalCon.value.toString();
      sampaiTanggal.value = sampaiTanggalCon.value.toString();
      Get.back(result: true);
      await listCon.refresh();
    } else {
      dariTanggal.value =
          "${DateTime.now().year}-${DateTime.now().month}-${DateTime.now().day}";
      sampaiTanggal.value =
          "${DateTime.now().year}-${DateTime.now().month}-${DateTime.now().day}";
      Get.back(result: true);
      await listCon.refresh();
    }
  }

  void addOnPress() {
    Get.toNamed(Routes.ABSEN_SURAT_TUGAS_SETUP)?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }

  itemOnTab(int id) {
    Get.toNamed(
      Routes.ABSEN_SURAT_TUGAS_SETUP,
      parameters: {
        'id': id.toString(),
      },
    )?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }
}
