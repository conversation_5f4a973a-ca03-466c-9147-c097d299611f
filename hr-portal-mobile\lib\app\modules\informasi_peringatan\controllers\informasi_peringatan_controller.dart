import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../models/peringatan_model.dart';
import '../../../routes/app_pages.dart';

class InformasiPeringatanController extends GetxController {
  final listCon = ListComponentController<PeringatanModel>(
    urlApi: (index, filter) => '/api/Peringatan?pageIndex=$index',
    fromDynamic: PeringatanModel.fromDynamic,
    allowSearch: false,
  );

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.INFORMASI_PERINGATAN_SETUP,
      parameters: {
        'id': id.toString(),
      },
    );
  }
}
