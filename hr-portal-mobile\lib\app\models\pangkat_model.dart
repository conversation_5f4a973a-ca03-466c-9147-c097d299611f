import 'dart:convert';
import '../mahas/services/mahas_format.dart';

class PangkatModel {
  int? id;
  String? nama;

  PangkatModel();

  static PangkatModel fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static PangkatModel fromDynamic(dynamic dynamicData) {
    final model = PangkatModel();

    model.id = MahasFormat.dynamicToInt(dynamicData['id']);
    model.nama = dynamicData['nama'];

    return model;
  }
}
