import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../models/pengumuman_model.dart';
import '../../../routes/app_pages.dart';

class InformasiPengumumanController extends GetxController {
  final listCon = ListComponentController<PengumumanModel>(
    urlApi: (index, filter) => '/api/Pengumuman?pageIndex=$index',
    fromDynamic: PengumumanModel.fromDynamic,
    allowSearch: false,
  );

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.INFORMASI_PENGUMUMAN_SETUP,
      parameters: {
        'id': id.toString(),
      },
    );
  }
}
