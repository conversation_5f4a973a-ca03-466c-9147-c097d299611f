<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>BGTaskSchedulerPermittedIdentifiers</key>
		<array>
			<string>com.sanata.hrportal</string>
		</array>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>$(APP_DISPLAY_NAME)</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>$(APP_DISPLAY_NAME)</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>com.googleusercontent.apps.762631444819-uebma5sepvgn63sb9rkq9v203rm7npk7</string>
					<string>com.googleusercontent.apps.673110614921-ue416g6106nnmra6n0uhl18q0ve2nai2</string>
					<string>com.googleusercontent.apps.976039200345-saa8le132v56u3t3v22jknptdpcfbqau</string>
					<string>com.googleusercontent.apps.546300849130-ho3nh5u6ub2h79j68dk5ol0jc3fk9rig</string>
					<string>com.googleusercontent.apps.786040226877-712frkl0vic6t9t1r5q9op3snqvak881</string>
					<string>com.googleusercontent.apps.117938321472-stt8lc3ca496u6noad0tpeacsu9s2m8b</string>
					<string>com.googleusercontent.apps.1000909257109-sam8p4b6hels1ctcetph5ab39ku0pk5p</string>
					<string>com.googleusercontent.apps.228772855287-jn124nef6c09sncu8cg923v677v6g50i</string>
					<string>com.googleusercontent.apps.525722564849-3gcffpfrqqauc2ekiq7cgkl3g50uhiuk</string>
					<string>com.googleusercontent.apps.68608939917-epmra47ic9nubkbtn033a73mm676n3pq</string>
					<string>com.googleusercontent.apps.466499966423-229tkfma8djq8g8juua6qk9qjrvsomko</string>
					<string>com.googleusercontent.apps.1068062661737-rrun61gf4icjpq5djfrgm160v456s5dd</string>
				</array>
			</dict>
		</array>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>This application requires GPS to track staff attendance</string>
		<key>NSLocationAlwaysUsageDescription</key>
		<string>This application requires GPS to track staff attendance</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>This application requires GPS to track staff attendance</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>This application requires access to photos library</string>
		<key>NSCameraUsageDescription</key>
		<string>This application requires access to camera</string>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>https://apps.apple.com/id/app/hr-portal/id1609184311</string>
		</array>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UIBackgroundModes</key>
		<array>
			<string>fetch</string>
			<string>nearby-interaction</string>
			<string>processing</string>
			<string>remote-notification</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false/>
	</dict>
</plist>
