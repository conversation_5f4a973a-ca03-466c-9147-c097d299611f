import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';

import '../../../mahas/components/inputs/input_text_component.dart';
import '../../../mahas/components/pages/setup_page_component.dart';
import '../../../mahas/mahas_colors.dart';
import '../../../mahas/mahas_config.dart';
import '../../../mahas/models/api_result_model.dart';
import '../../../mahas/services/helper.dart';
import '../../../mahas/services/http_api.dart';
import '../../../models/profile_model.dart';

class ProfileFotoHistorySetupController extends GetxController {
  late SetupPageController formCon;
  final alasanTolakCon = InputTextController();
  String? urlphoto;
  final images = Rxn<Uint8List>();
  final imageFile = Rxn<File>();
  Uint8List imageBytes = Uint8List(0);
  String? image;

  RxBool approvalButtonsVisible = false.obs;
  RxBool approvalFromNotif = false.obs;
  RxBool allowED = false.obs;
  Rxn<bool> statusApprovalHRD = Rxn<bool>();
  RxString status = "".obs;
  RxString namaHRD = "".obs;
  RxString namaPegawaiRequest = "".obs;
  RxString alasanTolakHRD = "".obs;
  Rx<DateTime> tglPermintaan = DateTime.now().obs;
  RxInt idPegawaiStr = 0.obs;

  @override
  void onInit() {
    //init
    String notifParam = Get.parameters['approval'].toString();
    approvalFromNotif.value = notifParam == "true" ? true : false;

    formCon = SetupPageController(
      urlApiGet: (id) => '/api/HistoryFotoProfile/$id',
      urlApiPut: (id) => '/api/HistoryFotoProfile/$id',
      urlApiDelete: (id) =>
          '/api/HistoryFotoProfile?id=$id&idDivisi=${MahasConfig.selectedDivisi}',
      allowDelete: allowED.value,
      allowEdit: allowED.value,
      bodyApi: (id) => {
        "id_Divisi": MahasConfig.selectedDivisi,
        "fotoProfile": image,
      },
      itemKey: (e) => e['id'],
      itemIdAfterSubmit: (e) => json.decode(e)['id'],
      apiToView: (json) {
        HistoryProfileModel model = HistoryProfileModel.fromJson(json);

        //mahascomponent
        urlphoto = model.fotoprofile;
        if (urlphoto != null) {
          images.value = base64Decode(urlphoto!);
        }

        //non mahas
        idPegawaiStr.value = model.id ?? 0;
        namaHRD.value = model.namapegawaiapproval ?? "";
        alasanTolakHRD.value = model.alasantolak ?? "";
        namaPegawaiRequest.value = model.pegawainama ?? "";
        statusApprovalHRD.value = model.approved;
        tglPermintaan.value = model.tanggalrequest ?? DateTime.now();

        //cek approval for allowedit or delete
        cekApproval();

        //approval
        if (approvalFromNotif.value && statusApprovalHRD.value == null) {
          approvalButtonsVisible.value = true;
        }
      },
      onBeforeSubmit: () {
        if (imageFile.value != null) {
          image = base64Encode(imageBytes);
        }
        return true;
      },
    );
    super.onInit();
  }

  void getFromGallery() async {
    XFile? pickedFile = await ImagePicker().pickImage(
      source: ImageSource.gallery,
      imageQuality: 30,
    );
    if (pickedFile != null) {
      imageFile.value = await croppedImage(image: pickedFile);
    }
    imageBytes = await imageFile.value!.readAsBytes();
  }

  Future<File?> croppedImage({required XFile image}) async {
    CroppedFile? croppedImage = await ImageCropper().cropImage(
      sourcePath: image.path,
      aspectRatio: const CropAspectRatio(ratioX: 1.0, ratioY: 1.0),
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: 'Cropper',
          toolbarColor: MahasColors.primary,
          toolbarWidgetColor: Colors.white,
          activeControlsWidgetColor: MahasColors.primary,
          statusBarColor: MahasColors.primary,
          initAspectRatio: CropAspectRatioPreset.original,
          lockAspectRatio: true,
          aspectRatioPresets: [
            CropAspectRatioPreset.square,
          ],
        ),
        IOSUiSettings(
          title: 'Cropper',
          aspectRatioPresets: [
            CropAspectRatioPreset.square,
          ],
        ),
      ],
    );
    if (croppedImage == null) return null;
    return File(croppedImage.path);
  }

  //cek approval parameters come from notifikasi
  void cekApproval() {
    if (approvalFromNotif.value || statusApprovalHRD.value != null) {
      allowED.value = false;
    } else {
      allowED.value = true;
    }
    formCon.setState(() {
      formCon.allowDelete = allowED.value;
      formCon.allowEdit = allowED.value;
    });
  }

  Future<void> approvalAction(bool approve) async {
    if (!alasanTolakCon.isValid) {
      Helper.dialogWarning("Alasan tidak boleh kosong");
    } else {
      if (EasyLoading.isShow) {
        EasyLoading.dismiss();
      }
      EasyLoading.show();
      String id = Get.parameters['id'].toString();
      final body = {
        'approve': approve,
        'alasanTolak': alasanTolakCon.value,
      };
      ApiResultModel? r;
      String url = '/api/HistoryFotoProfile/Approve/$id';
      r = await HttpApi.put(
        url,
        body: body,
      );
      if (r.success) {
        r = await HttpApi.get(
          '/api/HistoryFotoProfile/$id',
        );
        if (r.success) {
          HistoryProfileModel model = HistoryProfileModel.fromJson(r.body);
          statusApprovalHRD.value = model.approved;
          alasanTolakHRD.value = model.alasantolak ?? "";
          approvalButtonsVisible.value = false;
          update();
        }
      } else {
        Helper.dialogWarning(r.message);
      }
      EasyLoading.dismiss();
    }
  }
}
