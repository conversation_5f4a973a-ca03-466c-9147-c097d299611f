import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';
import 'package:hr_portal/app/models/lembur_model.dart';

class JadwalModel {
  int? id;
  int? idDivisi;
  int? idPegawaiManager;
  int? idPegawaiKadiv;
  int? tahun;
  int? bulan;
  bool? vinal;
  String? divisi;
  String? pegawaimanager;
  String? pegawaikadiv;
  List<JadwalDetailModel>? detail;

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static fromDynamic(dynamic dynamicData) {
    final model = JadwalModel();

    model.id = dynamicData['id'];
    model.idDivisi = dynamicData['id_Divisi'];
    model.idPegawaiManager = dynamicData['id_Pegawai_Manager'];
    model.idPegawaiKadiv = dynamicData['id_Pegawai_Kadiv'];
    model.tahun = dynamicData['tahun'];
    model.bulan = dynamicData['bulan'];
    model.vinal = dynamicData['final'];
    model.divisi = dynamicData['divisi'];
    model.pegawaimanager = dynamicData['pegawaiManager'];
    model.pegawaikadiv = dynamicData['pegawaiKadiv'];
    if (dynamicData['detail'] != null) {
      final detailT = dynamicData['detail'] as List;
      model.detail = [];
      for (var i = 0; i < detailT.length; i++) {
        model.detail!.add(JadwalDetailModel.fromDynamic(detailT[i]));
      }
    }

    return model;
  }
}

class JadwalDetailModel {
  int? id;
  int? idPegawai;
  int? idShift;
  int? tanggal;
  String? pegawai;
  String? shift;
  String? kode;
  String? kodewarna;
  TimeOfDay? jammulai;
  TimeOfDay? jamselesai;
  bool? shiftke2;
  TimeOfDay? jammulaike2;
  TimeOfDay? jamselesaike2;
  bool? shiftke3;
  TimeOfDay? jammulaike3;
  TimeOfDay? jamselesaike3;
  LemburModel? lembur;

  static fromJson(String jsonString) {
    final data = json.decode(jsonString);
    return fromDynamic(data);
  }

  static fromDynamic(dynamic dynamicData) {
    final model = JadwalDetailModel();

    model.id = dynamicData['id'];
    model.idPegawai = dynamicData['id_Pegawai'];
    model.idShift = dynamicData['id_Shift'];
    model.tanggal = dynamicData['tanggal'];
    model.pegawai = dynamicData['pegawai'];
    model.shift = dynamicData['shift'];
    model.kode = dynamicData['kode'];
    model.kodewarna = dynamicData['kodeWarna'];
    model.jammulai = MahasFormat.stringToTime(dynamicData['jamMulai']);
    model.jamselesai = MahasFormat.stringToTime(dynamicData['jamSelesai']);
    model.shiftke2 = dynamicData['shiftKe2'];
    model.jammulaike2 = MahasFormat.stringToTime(dynamicData['jamMulaiKe2']);
    model.jamselesaike2 =
        MahasFormat.stringToTime(dynamicData['jamSelesaiKe2']);
    model.shiftke3 = dynamicData['shiftKe3'];
    model.jammulaike3 = MahasFormat.stringToTime(dynamicData['jamMulaiKe3']);
    model.jamselesaike3 =
        MahasFormat.stringToTime(dynamicData['jamSelesaiKe3']);

    if (dynamicData['lembur'] != null) {
      model.lembur = LemburModel.fromDynamic(dynamicData['lembur']);
    }

    return model;
  }
}
