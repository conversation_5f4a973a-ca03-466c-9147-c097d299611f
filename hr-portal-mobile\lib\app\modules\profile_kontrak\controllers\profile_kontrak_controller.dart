import 'package:get/get.dart';

import '../../../mahas/components/others/list_component.dart';
import '../../../models/profile_kontrak_model.dart';
import '../../../routes/app_pages.dart';

class ProfileKontrakController extends GetxController {
  final listCon = ListComponentController<KontrakModel>(
    urlApi: (index, filter) => '/api/PegawaiKontrak/List?pageIndex=$index',
    fromDynamic: KontrakModel.fromDynamic,
    allowSearch: false,
  );

  void itemOnTab(int id) {
    Get.toNamed(
      Routes.PROFILE_KONTRAK_SETUP,
      parameters: {
        'id': id.toString(),
      },
    )?.then((value) {
      if (value) {
        listCon.refresh();
      }
    });
  }
}
