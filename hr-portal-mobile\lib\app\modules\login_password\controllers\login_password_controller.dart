import 'package:get/get.dart';
import 'package:hr_portal/app/controllers/auth_controller.dart';

import '../../../mahas/components/inputs/input_text_component.dart';

class LoginPasswordController extends GetxController {
  AuthController authCon = AuthController.instance;
  final emailCon = InputTextController(
    type: InputTextType.email,
  );

  final passwordCon = InputTextController(
    type: InputTextType.password,
  );

  final passwordConfirmCon = InputTextController(
    type: InputTextType.password,
  );
  RxBool isLogin = true.obs;

  void onSubmit() {
    if (!emailCon.isValid) return;
    if (!passwordCon.isValid) return;

    if (isLogin.value) {
      onLogin();
    } else if (!isLogin.value) {
      if (!passwordConfirmCon.isValid) return;

      if (passwordCon.value != passwordConfirmCon.value) {
        Get.snackbar(
          'Error',
          'Password tidak sama',
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }

      onRegister();
    }
  }

  Future<void> onLogin() async {
    await authCon.signInWithPassword(
        '${emailCon.value}', '${passwordCon.value}');
  }

  void onRegister() async {
    await authCon.signUpWithPassword(
        '${emailCon.value}', '${passwordCon.value}');
  }
}
