import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:get/get.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import 'package:hr_portal/app/mahas/components/mahas_themes.dart';
import 'package:hr_portal/app/mahas/components/others/list_component.dart';
import 'package:hr_portal/app/mahas/services/mahas_format.dart';
import 'package:hr_portal/app/mahas_complement/mahas_colors.dart';
import 'package:hr_portal/app/models/cuti_sakit_model.dart';

import '../../../mahas/services/helper.dart';
import '../../../mahas_complement/approval_status.dart';
import '../controllers/permintaan_jadwal_cuti_sakit_controller.dart';

class PermintaanJadwalCutiSakitView
    extends GetView<PermintaanJadwalCutiSakitController> {
  const PermintaanJadwalCutiSakitView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(MahasConfig.dinamisForm.cutiSakit?.tittle ?? "Cuti Sakit"),
        centerTitle: true,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 10),
            child: GestureDetector(
              onTap: controller.addOnPress,
              child: const Icon(
                FontAwesomeIcons.plus,
                size: 24,
              ),
            ),
          ),
        ],
      ),
      body: controller.isDinamis == false ? listStatis() : listDinamis(),
    );
  }

  ListComponent<CutiSakitModel> listStatis() {
    return ListComponent(
        controller: controller.listCutiSakitCon,
        itemBuilder: (CutiSakitModel e) {
          return InkWell(
            onTap: () => controller.itemOnTab(e.id!),
            child: Padding(
              padding:
                  const EdgeInsets.only(left: 12, right: 12, top: 8, bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(e.alasan ?? "Cuti Sakit",
                            style: MahasThemes.title),
                        Text("${e.berapaHari} Hari"),
                        const SizedBox(height: 5),
                        Visibility(
                          visible:
                              MahasConfig.dinamisForm.approval?.hanyaManager ==
                                  false,
                          child: Column(
                            children: [
                              const SizedBox(height: 5),
                              ApprovalStatusWidget(
                                pegawai: e.pegawaiKadiv.toString(),
                                approveStatus: e.approveKadiv,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 5),
                        ApprovalStatusWidget(
                          pegawai: e.pegawaiManager.toString(),
                          approveStatus: e.approveManager,
                        ),
                      ],
                    ),
                  ),
                  Text(
                    MahasFormat.displayDate(e.dariTanggal),
                    style: MahasColor.muted,
                  ),
                ],
              ),
            ),
          );
        });
  }

  ListComponent<CutiSakitModel> listDinamis() {
    return ListComponent(
      controller: controller.listCutiSakitCon,
      itemBuilder: (CutiSakitModel e) {
        return Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => controller.itemOnTab(e.id!),
                child: Padding(
                  padding: const EdgeInsets.only(
                      left: 12, right: 12, top: 12, bottom: 0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(e.alasan ?? "Cuti Sakit", style: MahasThemes.title),
                      const SizedBox(height: 15),
                    ],
                  ),
                ),
              ),
            ),
            InkWell(
              onTap: () async {
                await Helper.statusApproval(
                  "/api/PermintaanJadwal/CutiSakit/Approval/${e.id}",
                  alasanTolak: e.alasanTolak,
                );
              },
              child: Padding(
                padding: const EdgeInsets.only(
                    left: 12, right: 12, top: 12, bottom: 0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      MahasFormat.displayDate(e.tanggalInput),
                      style: MahasColor.muted,
                    ),
                    Text(
                      "Lihat Status",
                      style: TextStyle(
                        color: MahasColor.primaryColor.shade300,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
