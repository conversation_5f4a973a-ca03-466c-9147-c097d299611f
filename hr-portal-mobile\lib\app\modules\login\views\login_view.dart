import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hr_portal/app/constant/environment_constant.dart';
import 'package:hr_portal/app/mahas/components/mahas_themes.dart';
import 'package:hr_portal/app/mahas/mahas_config.dart';
import '../../../mahas/components/others/login_button.dart';
import '../controllers/login_controller.dart';

class LoginView extends GetView<LoginController> {
  const LoginView({super.key});
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Container(
          margin: const EdgeInsets.all(20),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(child: Container()),
                SizedBox(
                  height: 100,
                  child: Image.asset(EnvironmentConstant.imageLogo),
                ),
                const SizedBox(height: 10),
                Text(
                  MahasConfig.appName,
                  textAlign: TextAlign.center,
                  style: MahasThemes.h1,
                ),
                const SizedBox(height: 50),
                Text(
                  "Selamat datang di ${MahasConfig.appName}, aplikasi yang bertujuan untuk memudahkan pegawai untuk mendapatkan informasi",
                  textAlign: TextAlign.center,
                  style: MahasThemes.muted,
                  maxLines: 5,
                ),
                const SizedBox(height: 20),
                Text(
                  "Melanjutkan dengan",
                  textAlign: TextAlign.center,
                  style: MahasThemes.muted,
                  maxLines: 5,
                ),
                const SizedBox(height: 10),
                LoginButton(
                  onPressed: controller.googleLoginOnPress,
                  type: LoginButtonType.google,
                ),
                Visibility(
                  visible: Platform.isIOS,
                  child: LoginButton(
                    onPressed: controller.appleLoginOnPress,
                    type: LoginButtonType.apple,
                  ),
                ),
                Visibility(
                  visible: MahasConfig.profileMenu.loginEmail,
                  child: LoginButton(
                    onPressed: controller.emailLoginOnPress,
                    type: LoginButtonType.email,
                  ),
                ),
                Obx(
                  () => Visibility(
                    visible: controller.demo.isTrue,
                    child: TextButton(
                      onPressed: controller.demoOnPress,
                      child: const Text("Demo"),
                    ),
                  ),
                ),
                Expanded(child: Container()),
                Text(
                  "Dengan melanjutkan, anda menyetujui persyaratan Penguna dan Kebijakan Privasi ${MahasConfig.appName}",
                  textAlign: TextAlign.center,
                  style: MahasThemes.muted,
                  maxLines: 5,
                ),
                const SizedBox(height: 5),
                Text(
                  "${MahasConfig.packageInfo!.version}+${MahasConfig.packageInfo!.buildNumber}",
                  textAlign: TextAlign.center,
                  style: MahasThemes.muted,
                  maxLines: 2,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
